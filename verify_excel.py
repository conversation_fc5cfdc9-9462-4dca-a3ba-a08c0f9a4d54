#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def verify_new_excel():
    """验证新Excel文件的内容"""
    try:
        # 读取新的Excel文件
        df = pd.read_excel('催收 2.0-YXL 男声-intentions-新增开场白.xlsx', engine='openpyxl')
        
        print("✅ 新Excel文件验证成功")
        print(f"总数据行数: {len(df)}")
        
        # 查找新增的开场白数据
        print("\n🔍 查找新增的开场白数据:")
        
        # 首次逾期
        first_overdue = df[df['意图名称'] == '告知账单_首次逾期']
        print(f"首次逾期开场白数量: {len(first_overdue)}")
        
        # 偶尔逾期
        occasional_overdue = df[df['意图名称'] == '告知账单_偶尔逾期']
        print(f"偶尔逾期开场白数量: {len(occasional_overdue)}")
        
        # 惯性逾期
        habitual_overdue = df[df['意图名称'] == '告知账单_惯性逾期']
        print(f"惯性逾期开场白数量: {len(habitual_overdue)}")
        
        print("\n📝 新增开场白详细内容:")
        print("=" * 80)
        
        # 显示首次逾期的新开场白
        print("\n【首次逾期开场白】:")
        for idx, row in first_overdue.iterrows():
            if 'A01_First_Overdue_' in str(row['意图Code']):
                print(f"Code: {row['意图Code']}")
                print(f"话术: {row['意图话术']}")
                print("-" * 60)
        
        # 显示偶尔逾期的新开场白
        print("\n【偶尔逾期开场白】:")
        for idx, row in occasional_overdue.iterrows():
            if 'A01_Occasional_Overdue_' in str(row['意图Code']):
                print(f"Code: {row['意图Code']}")
                print(f"话术: {row['意图话术']}")
                print("-" * 60)
        
        # 显示惯性逾期的新开场白
        print("\n【惯性逾期开场白】:")
        for idx, row in habitual_overdue.iterrows():
            if 'A01_Inertial_Overdue_' in str(row['意图Code']):
                print(f"Code: {row['意图Code']}")
                print(f"话术: {row['意图话术']}")
                print("-" * 60)
                
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        return False

if __name__ == "__main__":
    verify_new_excel()
