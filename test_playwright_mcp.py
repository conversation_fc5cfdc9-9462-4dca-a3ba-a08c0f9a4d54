#!/usr/bin/env python3
"""
Playwright MCP 测试脚本
用于验证 Playwright MCP 服务器的基本功能
"""

import subprocess
import time
import json
import os
import signal
import sys
from pathlib import Path

class PlaywrightMCPTester:
    def __init__(self):
        self.mcp_process = None
        self.test_results = []
        
    def start_mcp_server(self, port=8931):
        """启动 Playwright MCP 服务器"""
        try:
            print(f"🚀 启动 Playwright MCP 服务器 (端口: {port})...")
            
            # 启动 MCP 服务器
            cmd = ["npx", "@playwright/mcp@latest", "--port", str(port), "--headless"]
            self.mcp_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待服务器启动
            time.sleep(5)
            
            # 检查进程是否还在运行
            if self.mcp_process.poll() is None:
                print("✅ MCP 服务器启动成功")
                return True
            else:
                stdout, stderr = self.mcp_process.communicate()
                print(f"❌ MCP 服务器启动失败")
                print(f"stdout: {stdout}")
                print(f"stderr: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 启动 MCP 服务器时出错: {e}")
            return False
    
    def test_mcp_help(self):
        """测试 MCP 帮助命令"""
        try:
            print("\n📋 测试 MCP 帮助命令...")
            result = subprocess.run(
                ["npx", "@playwright/mcp@latest", "--help"],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0 and "Usage:" in result.stdout:
                print("✅ MCP 帮助命令测试通过")
                self.test_results.append(("help_command", True, "帮助命令正常工作"))
                return True
            else:
                print(f"❌ MCP 帮助命令测试失败: {result.stderr}")
                self.test_results.append(("help_command", False, result.stderr))
                return False
                
        except Exception as e:
            print(f"❌ 测试帮助命令时出错: {e}")
            self.test_results.append(("help_command", False, str(e)))
            return False
    
    def test_mcp_version(self):
        """测试 MCP 版本命令"""
        try:
            print("\n🔢 测试 MCP 版本命令...")
            result = subprocess.run(
                ["npx", "@playwright/mcp@latest", "--version"],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0 and result.stdout.strip():
                version = result.stdout.strip()
                print(f"✅ MCP 版本: {version}")
                self.test_results.append(("version_command", True, f"版本: {version}"))
                return True
            else:
                print(f"❌ MCP 版本命令测试失败: {result.stderr}")
                self.test_results.append(("version_command", False, result.stderr))
                return False
                
        except Exception as e:
            print(f"❌ 测试版本命令时出错: {e}")
            self.test_results.append(("version_command", False, str(e)))
            return False
    
    def test_browser_installation(self):
        """测试浏览器安装"""
        try:
            print("\n🌐 测试浏览器安装...")
            
            # 检查是否已安装 Playwright 浏览器
            result = subprocess.run(
                ["npx", "playwright", "install", "--help"],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                print("✅ Playwright 浏览器安装工具可用")
                self.test_results.append(("browser_install_tool", True, "安装工具可用"))
                
                # 尝试安装 chromium（如果尚未安装）
                print("📦 检查 Chromium 浏览器...")
                install_result = subprocess.run(
                    ["npx", "playwright", "install", "chromium"],
                    capture_output=True,
                    text=True,
                    timeout=120
                )
                
                if install_result.returncode == 0:
                    print("✅ Chromium 浏览器安装/检查完成")
                    self.test_results.append(("browser_install", True, "Chromium 安装成功"))
                    return True
                else:
                    print(f"⚠️ Chromium 安装可能有问题: {install_result.stderr}")
                    self.test_results.append(("browser_install", False, install_result.stderr))
                    return False
            else:
                print(f"❌ Playwright 安装工具不可用: {result.stderr}")
                self.test_results.append(("browser_install_tool", False, result.stderr))
                return False
                
        except Exception as e:
            print(f"❌ 测试浏览器安装时出错: {e}")
            self.test_results.append(("browser_install", False, str(e)))
            return False
    
    def test_mcp_config_validation(self):
        """验证 MCP 配置文件"""
        try:
            print("\n⚙️ 验证 MCP 配置文件...")
            
            config_path = Path.home() / ".cursor" / "mcp.json"
            if not config_path.exists():
                print("❌ MCP 配置文件不存在")
                self.test_results.append(("config_validation", False, "配置文件不存在"))
                return False
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查 playwright 配置
            if "mcpServers" in config and "playwright" in config["mcpServers"]:
                playwright_config = config["mcpServers"]["playwright"]
                if "command" in playwright_config and "args" in playwright_config:
                    print("✅ Playwright MCP 配置验证通过")
                    self.test_results.append(("config_validation", True, "配置文件格式正确"))
                    return True
                else:
                    print("❌ Playwright MCP 配置格式不正确")
                    self.test_results.append(("config_validation", False, "配置格式不正确"))
                    return False
            else:
                print("❌ 配置文件中未找到 Playwright MCP 配置")
                self.test_results.append(("config_validation", False, "未找到 Playwright 配置"))
                return False
                
        except Exception as e:
            print(f"❌ 验证配置文件时出错: {e}")
            self.test_results.append(("config_validation", False, str(e)))
            return False
    
    def cleanup(self):
        """清理资源"""
        if self.mcp_process and self.mcp_process.poll() is None:
            print("\n🧹 清理 MCP 服务器进程...")
            self.mcp_process.terminate()
            try:
                self.mcp_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.mcp_process.kill()
                self.mcp_process.wait()
            print("✅ MCP 服务器进程已清理")
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 Playwright MCP 测试报告")
        print("="*60)
        
        passed = sum(1 for _, success, _ in self.test_results if success)
        total = len(self.test_results)
        
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {total - passed}")
        print(f"成功率: {passed/total*100:.1f}%" if total > 0 else "成功率: 0%")
        
        print("\n详细结果:")
        for test_name, success, message in self.test_results:
            status = "✅ 通过" if success else "❌ 失败"
            print(f"  {test_name}: {status} - {message}")
        
        # 保存报告到文件
        report_data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_tests": total,
            "passed": passed,
            "failed": total - passed,
            "success_rate": passed/total*100 if total > 0 else 0,
            "results": [
                {
                    "test": test_name,
                    "success": success,
                    "message": message
                }
                for test_name, success, message in self.test_results
            ]
        }
        
        with open("playwright_mcp_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: playwright_mcp_test_report.json")
        
        return passed == total

def main():
    """主函数"""
    print("🎭 Playwright MCP 功能验证测试")
    print("="*60)
    
    tester = PlaywrightMCPTester()
    
    try:
        # 运行所有测试
        tests = [
            tester.test_mcp_help,
            tester.test_mcp_version,
            tester.test_mcp_config_validation,
            tester.test_browser_installation,
        ]
        
        for test in tests:
            test()
        
        # 生成报告
        all_passed = tester.generate_report()
        
        if all_passed:
            print("\n🎉 所有测试通过！Playwright MCP 配置成功！")
            return 0
        else:
            print("\n⚠️ 部分测试失败，请检查上述错误信息")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中发生未预期的错误: {e}")
        return 1
    finally:
        tester.cleanup()

if __name__ == "__main__":
    sys.exit(main())
