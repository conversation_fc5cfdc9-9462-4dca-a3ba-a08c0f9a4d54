import requests
import base64
import wave
import simpleaudio as sa

# 设置请求的URL和头信息
url = "http://openapi-cloud-pub.zhonganinfo.com/ai/test/za/tts/basic/v1"
headers = {
    "Content-Type": "application/json",
    "Access-Key": "d4fe35c5696f4d558bafc9fae203aa97"
}

# 修改SSML文本格式
# 改成ssml格式
ssml_text = """
合同有规定，按月还款是您应尽的义务，再说贷款总是要还的，您可以找亲戚朋友周转下避免逾期导致还款金额更高好吧？	
"""

# 设置请求的body数据
data = {
    "text": ssml_text,
    "voiceId": 1200022,
    "audioFormat": "wav",
    # 设置音频的采样率
    "sampleRate": 8000,
    # 语速
    # "speed": 0.95,

}

# 发送POST请求
response = requests.post(url, headers=headers, json=data)

# 检查响应状态码
if response.status_code == 200:
    # 获取返回的JSON数据
    result = response.json()
    # 提取audioBase64字段
    audio_base64 = result.get("audioBase64")

    if audio_base64:
        # 解码base64数据
        audio_data = base64.b64decode(audio_base64)
        # 将音频数据写入文件
        output_file = "output5.wav"
        with open(output_file, "wb") as audio_file:
            audio_file.write(audio_data)
        print(f"音频文件已保存为 {output_file}")

        # 读取音频文件信息
        try:
            with wave.open(output_file, 'rb') as wf:
                # 添加调试信息
                print(f"音频通道数：{wf.getnchannels()}")
                print(f"采样宽度：{wf.getsampwidth()}")
                print(f"采样率：{wf.getframerate()}")
                print(f"帧数：{wf.getnframes()}")
                print(f"音频时长：{wf.getnframes() / wf.getframerate():.2f}秒")
                
                # 播放音频
                wave_obj = sa.WaveObject.from_wave_file(output_file)
                play_obj = wave_obj.play()
                play_obj.wait_done()  # 确保等待播放完成
        except Exception as e:
            print(f"读取或播放音频时出错: {e}")

    else:
        print("响应中没有找到 audioBase64 字段")
else:
    print(f"请求失败，状态码：{response.status_code}")
    print(f"响应内容：{response.text}")
