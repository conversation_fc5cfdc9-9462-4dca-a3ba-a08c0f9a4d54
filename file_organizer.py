#!/usr/bin/env python3
"""
文件整理工具 - 自动分析和整理下载目录中的文件
"""

import os
import shutil
import json
from pathlib import Path
from collections import defaultdict
import re
from datetime import datetime

class FileOrganizer:
    def __init__(self, downloads_dir="/Users/<USER>/Downloads"):
        self.downloads_dir = Path(downloads_dir)
        self.categories = {
            "01_AI语音训练素材": {
                "subcategories": {
                    "01_参考音频": {
                        "keywords": ["参考音频", "参考", "周志豪", "于沛", "胡中银", "张帅", "litianwei"],
                        "patterns": [r".*参考.*", r".*周志豪.*", r".*于沛.*", r".*胡中银.*"]
                    },
                    "02_TTS训练素材": {
                        "keywords": ["TTS", "训练", "素材", "GPT", "SoVITS", "音色"],
                        "patterns": [r".*TTS.*", r".*训练.*", r".*素材.*", r".*音色.*"]
                    },
                    "03_生成音频": {
                        "keywords": ["generated", "output", "tts_preview"],
                        "patterns": [r".*generated.*", r".*output.*", r".*preview.*"]
                    }
                },
                "extensions": [".wav", ".mp3", ".m4a"],
                "keywords": ["参考", "训练", "素材", "音色", "声音", "TTS", "GPT", "SoVITS"]
            },
            "02_催收话术配置": {
                "subcategories": {
                    "01_灵犀平台": {
                        "keywords": ["灵犀", "M1"],
                        "patterns": [r".*灵犀.*", r".*M1.*"]
                    },
                    "02_金融机构": {
                        "keywords": ["众安", "众利", "金融"],
                        "patterns": [r".*众安.*", r".*众利.*", r".*金融.*"]
                    },
                    "03_意图分类": {
                        "keywords": ["intentions"],
                        "patterns": [r".*intentions.*"]
                    },
                    "04_话术配置": {
                        "keywords": ["话术", "催收"],
                        "patterns": [r".*话术.*", r".*催收.*"]
                    }
                },
                "extensions": [".xls", ".xlsx", ".json"],
                "keywords": ["催收", "intentions", "话术", "灵犀", "众安", "众利"]
            },
            "03_预提醒系统": {
                "subcategories": {
                    "01_预提醒配置": {
                        "keywords": ["预提醒"],
                        "patterns": [r".*预提醒.*"]
                    },
                    "02_逾期处理": {
                        "keywords": ["逾期"],
                        "patterns": [r".*逾期.*"]
                    }
                },
                "extensions": [".xls", ".xlsx", ".wav", ".json"],
                "keywords": ["预提醒", "提醒", "逾期", "账单"]
            },
            "04_金融外呼系统": {
                "subcategories": {
                    "01_外呼配置": {
                        "keywords": ["外呼", "webcall"],
                        "patterns": [r".*外呼.*", r".*webcall.*"]
                    },
                    "02_金融产品": {
                        "keywords": ["金融", "financial"],
                        "patterns": [r".*金融.*", r".*financial.*"]
                    }
                },
                "extensions": [".wav", ".xls", ".xlsx", ".json"],
                "keywords": ["金融", "外呼", "financial", "webcall"]
            },
            "05_电销营销": {
                "extensions": [".wav", ".xls", ".xlsx"],
                "keywords": ["电销", "营销", "保险", "健康险"]
            },
            "06_录音文件": {
                "subcategories": {
                    "01_通话录音": {
                        "keywords": ["sip", "call", "通话"],
                        "patterns": [r".*sip.*", r".*call.*", r".*通话.*"]
                    },
                    "02_音频片段": {
                        "keywords": ["audio", "录音"],
                        "patterns": [r".*audio.*", r".*录音.*"]
                    },
                    "03_开场白": {
                        "keywords": ["开场", "开场白"],
                        "patterns": [r".*开场.*"]
                    }
                },
                "extensions": [".wav", ".mp3", ".m4a"],
                "keywords": ["录音", "通话", "call", "record", "audio", "sip"]
            },
            "07_测试数据": {
                "extensions": [".xlsx", ".xls", ".json", ".wav"],
                "keywords": ["测试", "test", "测试集"]
            },
            "08_知识库文档": {
                "subcategories": {
                    "01_知识库": {
                        "keywords": ["knowledge", "知识"],
                        "patterns": [r".*knowledge.*", r".*知识.*"]
                    },
                    "02_FAQ": {
                        "keywords": ["FAQ", "问答"],
                        "patterns": [r".*FAQ.*", r".*问答.*"]
                    }
                },
                "extensions": [".xlsx", ".json", ".txt"],
                "keywords": ["知识", "knowledge", "FAQ", "问答"]
            },
            "09_软件安装包": {
                "subcategories": {
                    "01_应用程序": {
                        "extensions": [".dmg", ".app"],
                        "keywords": ["安装", "软件"]
                    },
                    "02_压缩包": {
                        "extensions": [".zip"],
                        "keywords": ["压缩", "zip"]
                    }
                },
                "extensions": [".dmg", ".zip", ".app"],
                "keywords": ["安装", "软件"]
            },
            "10_图片截图": {
                "extensions": [".png", ".jpeg", ".jpg", ".gif"],
                "keywords": ["截图", "图片", "图标"]
            },
            "11_配置文件": {
                "subcategories": {
                    "01_系统配置": {
                        "keywords": ["config", "设置", "配置"],
                        "patterns": [r".*config.*", r".*设置.*"]
                    },
                    "02_业务配置": {
                        "keywords": ["copy", "技能", "流程"],
                        "patterns": [r".*copy.*", r".*技能.*", r".*流程.*"]
                    },
                    "03_监控告警": {
                        "keywords": ["监工", "告警", "触达"],
                        "patterns": [r".*监工.*", r".*告警.*", r".*触达.*"]
                    }
                },
                "extensions": [".json", ".conf", ".ini", ".yaml"],
                "keywords": ["config", "设置", "配置", "copy", "监工", "告警"]
            },
            "12_临时文件": {
                "patterns": [r"^\.~.*", r".*temp.*", r".*tmp.*", r".*\.backup$"],
                "extensions": [".tmp", ".temp", ".backup"],
                "keywords": ["temp", "tmp", "临时", "backup"]
            }
        }
        
    def analyze_files(self):
        """分析文件并生成统计报告"""
        file_stats = defaultdict(list)
        total_files = 0
        total_size = 0
        
        for item in self.downloads_dir.iterdir():
            if item.is_file():
                total_files += 1
                size = item.stat().st_size
                total_size += size
                
                category = self.categorize_file(item)
                file_stats[category].append({
                    'name': item.name,
                    'size': size,
                    'modified': datetime.fromtimestamp(item.stat().st_mtime)
                })
        
        return file_stats, total_files, total_size
    
    def categorize_file(self, file_path):
        """根据文件名、扩展名和关键词对文件进行分类，支持子分类"""
        file_name = file_path.name.lower()
        file_ext = file_path.suffix.lower()

        for category, rules in self.categories.items():
            # 检查文件扩展名
            if file_ext in rules.get("extensions", []):
                # 先检查子分类
                subcategories = rules.get("subcategories", {})
                for subcat_name, subcat_rules in subcategories.items():
                    # 检查子分类的扩展名
                    if file_ext in subcat_rules.get("extensions", rules.get("extensions", [])):
                        # 检查子分类关键词
                        for keyword in subcat_rules.get("keywords", []):
                            if keyword.lower() in file_name:
                                return f"{category}/{subcat_name}"

                        # 检查子分类正则表达式
                        for pattern in subcat_rules.get("patterns", []):
                            if re.search(pattern, file_name, re.IGNORECASE):
                                return f"{category}/{subcat_name}"

                # 如果没有匹配子分类，检查主分类
                # 检查主分类关键词
                for keyword in rules.get("keywords", []):
                    if keyword.lower() in file_name:
                        return category

                # 检查主分类正则表达式模式
                for pattern in rules.get("patterns", []):
                    if re.search(pattern, file_name, re.IGNORECASE):
                        return category

        # 特殊处理：按文件名模式进一步分类其他文件
        return self._categorize_other_files(file_path)

    def _categorize_other_files(self, file_path):
        """对其他文件进行进一步分类"""
        file_name = file_path.name.lower()
        file_ext = file_path.suffix.lower()

        # 按文件类型分类
        if file_ext in ['.wav', '.mp3', '.m4a']:
            if any(keyword in file_name for keyword in ['新录音', '录音', 'audio']):
                return "99_其他文件/01_音频文件"
            return "99_其他文件/01_音频文件"

        elif file_ext in ['.xls', '.xlsx']:
            if any(keyword in file_name for keyword in ['list', '列表', '数据']):
                return "99_其他文件/02_数据表格"
            return "99_其他文件/02_数据表格"

        elif file_ext in ['.json']:
            return "99_其他文件/03_JSON配置"

        elif file_ext in ['.html', '.htm']:
            return "99_其他文件/04_网页文件"

        elif file_ext in ['.txt', '.md', '.doc', '.docx']:
            return "99_其他文件/05_文档文件"

        elif file_ext in ['.m4s', '.mp4', '.avi']:
            return "99_其他文件/06_视频文件"

        elif file_ext in ['.dot']:
            return "99_其他文件/07_模板文件"

        else:
            return "99_其他文件/99_未分类"
    
    def create_directory_structure(self):
        """创建分类目录结构，包括子目录"""
        base_dir = self.downloads_dir / "整理后的文件"
        base_dir.mkdir(exist_ok=True)

        for category, rules in self.categories.items():
            category_dir = base_dir / category
            category_dir.mkdir(exist_ok=True)

            # 创建子分类目录
            subcategories = rules.get("subcategories", {})
            for subcat_name in subcategories.keys():
                subcat_dir = category_dir / subcat_name
                subcat_dir.mkdir(exist_ok=True)

        # 创建其他文件的子目录
        other_dir = base_dir / "99_其他文件"
        other_dir.mkdir(exist_ok=True)

        other_subdirs = [
            "01_音频文件", "02_数据表格", "03_JSON配置", "04_网页文件",
            "05_文档文件", "06_视频文件", "07_模板文件", "99_未分类"
        ]

        for subdir in other_subdirs:
            (other_dir / subdir).mkdir(exist_ok=True)

        return base_dir
    
    def move_files(self, dry_run=False):
        """移动文件到对应分类目录"""
        base_dir = self.create_directory_structure()
        moved_files = defaultdict(list)
        
        for item in self.downloads_dir.iterdir():
            if item.is_file() and not item.name.startswith('.') and item.parent.name != "整理后的文件":
                category = self.categorize_file(item)
                target_dir = base_dir / category
                target_path = target_dir / item.name
                
                # 处理重名文件
                counter = 1
                original_target = target_path
                while target_path.exists():
                    stem = original_target.stem
                    suffix = original_target.suffix
                    target_path = target_dir / f"{stem}_{counter}{suffix}"
                    counter += 1
                
                if not dry_run:
                    try:
                        shutil.move(str(item), str(target_path))
                        moved_files[category].append(item.name)
                    except Exception as e:
                        print(f"移动文件失败: {item.name} -> {e}")
                else:
                    moved_files[category].append(f"{item.name} -> {target_path.name}")
        
        return moved_files

def main():
    organizer = FileOrganizer()
    
    print("🔍 正在分析文件...")
    file_stats, total_files, total_size = organizer.analyze_files()
    
    print(f"📊 发现 {total_files} 个文件，总大小: {total_size / (1024*1024*1024):.2f} GB")
    
    # 生成分析报告
    report = {
        "分析时间": datetime.now().isoformat(),
        "文件总数": total_files,
        "总大小_GB": round(total_size / (1024*1024*1024), 2),
        "分类统计": {}
    }
    
    for category, files in file_stats.items():
        report["分类统计"][category] = {
            "文件数量": len(files),
            "总大小_MB": round(sum(f['size'] for f in files) / (1024*1024), 2),
            "示例文件": [f['name'] for f in files[:5]]
        }
    
    # 保存分析报告
    report_path = Path("/Users/<USER>/Downloads/文件分类分析报告.json")
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"📋 分析报告已保存到: {report_path}")
    
    # 询问是否执行移动
    response = input("\n是否开始移动文件? (y/n): ")
    if response.lower() == 'y':
        print("📁 正在创建目录结构并移动文件...")
        moved_files = organizer.move_files(dry_run=False)
        
        # 生成移动报告
        move_report_path = Path("/Users/<USER>/Downloads/整理后的文件/文件移动报告.json")
        with open(move_report_path, 'w', encoding='utf-8') as f:
            json.dump(dict(moved_files), f, ensure_ascii=False, indent=2)
        
        print(f"✅ 文件移动完成! 移动报告保存到: {move_report_path}")
    else:
        print("🔍 执行预览模式...")
        moved_files = organizer.move_files(dry_run=True)
        for category, files in moved_files.items():
            print(f"\n{category}: {len(files)} 个文件")
            for file in files[:3]:
                print(f"  - {file}")

if __name__ == "__main__":
    main()
