# Chrome MCP 服务器配置指南

## 概述

Chrome MCP Server 是一个基于 Chrome 扩展的 Model Context Protocol (MCP) 服务器，它可以让 AI 助手（如 Claude Code）直接控制您的 Chrome 浏览器，实现复杂的浏览器自动化、内容分析和语义搜索。

## 安装步骤

### 1. 安装 mcp-chrome-bridge

```bash
npm install -g mcp-chrome-bridge
```

### 2. 下载并安装 Chrome 扩展

1. 访问 [GitHub Releases 页面](https://github.com/hangwin/mcp-chrome/releases)
2. 下载最新版本的 Chrome 扩展
3. 解压下载的文件
4. 在 Chrome 中打开 `chrome://extensions/`
5. 启用"开发者模式"
6. 点击"加载已解压的扩展程序"，选择解压后的文件夹
7. 点击扩展图标打开插件，然后点击连接按钮

### 3. 配置 Claude Code

#### 方式一：Streamable HTTP 连接（推荐）

在 Claude Code 的 MCP 配置中添加：

```json
{
  "mcpServers": {
    "chrome-mcp-server": {
      "type": "streamableHttp",
      "url": "http://127.0.0.1:12306/mcp"
    }
  }
}
```

#### 方式二：STDIO 连接（备选）

```json
{
  "mcpServers": {
    "chrome-mcp-stdio": {
      "command": "node",
      "args": [
        "/usr/local/lib/node_modules/mcp-chrome-bridge/dist/mcp/mcp-server-stdio.js"
      ]
    }
  }
}
```

## 主要功能

### 📊 浏览器管理 (6个工具)
- `get_windows_and_tabs` - 列出所有浏览器窗口和标签页
- `chrome_navigate` - 导航到URL并控制视口
- `chrome_close_tabs` - 关闭特定标签页或窗口
- `chrome_go_back_or_forward` - 浏览器导航控制
- `chrome_inject_script` - 向网页注入内容脚本
- `chrome_send_command_to_inject_script` - 向注入的内容脚本发送命令

### 📸 截图和视觉 (1个工具)
- `chrome_screenshot` - 高级截图功能，支持元素定位、全页面截图和自定义尺寸

### 🌐 网络监控 (4个工具)
- `chrome_network_capture_start/stop` - webRequest API 网络捕获
- `chrome_network_debugger_start/stop` - 调试器API，包含响应体
- `chrome_network_request` - 发送自定义HTTP请求

### 🔍 内容分析 (4个工具)
- `search_tabs_content` - AI驱动的跨浏览器标签页语义搜索
- `chrome_get_web_content` - 从页面提取HTML/文本内容
- `chrome_get_interactive_elements` - 查找可点击元素
- `chrome_console` - 从浏览器标签页捕获和检索控制台输出

### 🎯 交互 (3个工具)
- `chrome_click_element` - 使用CSS选择器点击元素
- `chrome_fill_or_select` - 填写表单和选择选项
- `chrome_keyboard` - 模拟键盘输入和快捷键

### 📚 数据管理 (5个工具)
- `chrome_history` - 使用时间过滤器搜索浏览器历史
- `chrome_bookmark_search` - 按关键词查找书签
- `chrome_bookmark_add` - 添加新书签，支持文件夹
- `chrome_bookmark_delete` - 删除书签

## 使用示例

### 基本操作
- "请帮我打开百度首页"
- "请截取当前页面的截图"
- "请帮我点击搜索框并输入'Chrome MCP'"

### 高级功能
- "请帮我分析当前页面内容并总结"
- "请帮我查看页面的网络请求"
- "请帮我管理书签，将当前页面添加到合适的文件夹"

## 故障排除

1. **扩展未连接**：确保 Chrome 扩展已正确安装并点击了连接按钮
2. **端口冲突**：确保端口 12306 没有被其他程序占用
3. **权限问题**：确保 Chrome 扩展有足够的权限访问网页

## 注意事项

- Chrome MCP Server 直接使用您的日常 Chrome 浏览器，保留现有的登录状态和配置
- 相比 Playwright 等工具，它不需要启动独立的浏览器进程
- 支持跨标签页操作和语义搜索功能
- 完全本地运行，确保用户隐私安全
