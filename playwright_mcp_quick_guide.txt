
# Playwright MCP 使用指南

## 基本操作示例

1. **网页导航**
   - "请帮我打开 https://www.baidu.com"
   - "请导航到 GitHub 首页"

2. **页面截图**
   - "请帮我截取当前页面的截图"
   - "请截取搜索框的截图"

3. **页面交互**
   - "请帮我点击搜索框"
   - "请在搜索框中输入 'Playwright MCP'"
   - "请点击搜索按钮"

4. **高级功能**
   - "请将当前页面保存为PDF"
   - "请查看页面的网络请求"
   - "请打开新标签页"

## 配置信息

当前 MCP 配置:
```json
{
  "mcpServers": {
    "playwright": {
      "command": "npx",
      "args": ["@playwright/mcp@latest"]
    }
  }
}
```

## 注意事项

- 确保已重启 Cursor 以加载 MCP 配置
- 使用自然语言描述操作
- 支持多种浏览器和设备模拟
- 可以生成自动化测试代码
