[{"taskId": 139, "intentionCode": "A01", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "是本人(告知账单)", "intentionTts": "您好啊，您在${loanProductName}的账单今天我们已经进行了多次扣款都没有成功。鉴于您历史上有过逾期记录，平台这边也比较重视您的还款情况。想问一下您现在是因为什么原因还没有处理这笔还款呢？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "E", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077143000, "modifier": "刘MM", "gmtModified": 1741077143000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "A02", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "非本人", "intentionTts": "那请问您认识${name}吗？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "E", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077143000, "modifier": "刘MM", "gmtModified": 1741077143000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "A03", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "二次确认是否本人", "intentionTts": "我这边是众安，请问是${name}吗？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "E", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077143000, "modifier": "刘MM", "gmtModified": 1741077143000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "B00", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "客户忙", "intentionTts": "好的，现在不方便没有关系，但请注意还款时效，避免后续遗忘还款导致错过划扣时间影响您的征信，建议您抽个把分钟处理下，好吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "three", "voiceId": 7861, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_B00.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=Nc4Yu9KDhv3YBQ5k9zEeRfblHW4%3D", "creator": "刘MM", "gmtCreated": 1741077143000, "modifier": "刘MM", "gmtModified": 1741077143000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "B001", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "客户忙", "intentionTts": "了解，但若超出还款时效，逾期产生的额外费用以及影响征信等相关风险提示到您，请您务必注意还款时效，那不打扰了，再见。", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "A", "isEnd": "Y", "stepNo": "", "voiceId": 7862, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_B001.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=CQ0X0I3l266ArwKd3UuIRq7zaaE%3D", "creator": "刘MM", "gmtCreated": 1741077143000, "modifier": "刘MM", "gmtModified": 1741077143000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "B002", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "在开车", "intentionTts": "好呀，那您注意开车安全，建议您待会停车后您抽个把分钟处理下，主要也怕您后续遗忘还款导致错过划扣时间影响您的征信，好吧", "includeVariable": "N", "nonInterrupt": 5, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": 7864, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_B002.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=APjQWxuHJenpSQ7WeqXU0lbCvlo%3D", "creator": "刘MM", "gmtCreated": 1741077143000, "modifier": "刘MM", "gmtModified": 1741077143000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "B01", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "认识本人", "intentionTts": "${name}在${loanProductName}有一笔金融业务需要在今天处理一下，麻烦您有空的话就转告他一下，谢谢您的配合哈，祝您生活愉快，再见。", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "E", "isEnd": "Y", "stepNo": "three", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077143000, "modifier": "刘MM", "gmtModified": 1741077143000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "B02", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "不认识本人", "intentionTts": "抱歉，我们这边是有查到本电话机主在${loanProductName}有一笔金融业务需要处理的，如果您认识他的话麻烦帮忙转告一下，我们后续也会再次致电的，祝您生活愉快，再见。", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "E", "isEnd": "Y", "stepNo": "", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077143000, "modifier": "刘MM", "gmtModified": 1741077143000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "B61", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "拖延时间", "intentionTts": "您这个真的不能再拖下去了，大概1个小时内系统就要核账了，今天不还的话明天就会逾期了，为了避免影响后续您在银行啊或者其他机构的借贷，麻烦您务必在今晚8点前通过资金周转啊或者其他方式处理到账，好吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "D", "isEnd": "N", "stepNo": "two", "voiceId": 7920, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_B61.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=sYJ53VLD8y3kU88dpbblg9AF2X4%3D", "creator": "刘MM", "gmtCreated": 1741077144000, "modifier": "刘MM", "gmtModified": 1741077144000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "B611", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "告知逾期风险", "intentionTts": "再次提醒您，您之前已经有过逾期记录，本次再逾期的话会新增平台上的逾期记录，也可能会移交到催收部门，请您尽快想想办法处理了，好吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "D", "isEnd": "N", "stepNo": "two", "voiceId": 7870, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_B611.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=oBXM9uWYQA2DpnMf%2F9AWfyhAHS4%3D", "creator": "刘MM", "gmtCreated": 1741077144000, "modifier": "刘MM", "gmtModified": 1741077144000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "B6111", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "三次拖延时间结束语", "intentionTts": "您要知道啊，过了今天再还的话都会产生逾期记录的，这个可能会影响您的征信评分，还请您遵守借款合同规定按时还款哈，相关逾期产生的额外费用以及影响征信等相关风险我已经提示到您了，那我先不打扰您了，再见。", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "D", "isEnd": "Y", "stepNo": "", "voiceId": 7923, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_B6111.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=92V3NXOLOwqpBBeAlOTwqKtMyhQ%3D", "creator": "刘MM", "gmtCreated": 1741077144000, "modifier": "刘MM", "gmtModified": 1741077144000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "B6121", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "三次资金困难结束语", "intentionTts": "了解，但是系统统一查账时间是1小时内，建议您通过资金周转等方式赶在此前还款，再见。", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "D", "isEnd": "Y", "stepNo": "", "voiceId": 7872, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_B6121.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=wdSEF6%2Bts8TCTuRFkLEH%2BZoo0tQ%3D", "creator": "刘MM", "gmtCreated": 1741077144000, "modifier": "刘MM", "gmtModified": 1741077144000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "B6131", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "三次拒绝还款结束语", "intentionTts": "电话是有录音的，情况我会如实记录，本次再逾期的话对您来说影响还是蛮大的，还请您今天务必处理到账，打扰了，再见。", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "D", "isEnd": "Y", "stepNo": "", "voiceId": 7873, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_B6131.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=go6OEXPEOP3zvfrnKTzp9PKFZL4%3D", "creator": "刘MM", "gmtCreated": 1741077144000, "modifier": "刘MM", "gmtModified": 1741077144000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "B62", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "资金困难", "intentionTts": "一时遇到困难我们是能理解的，但是遇到困难肯定得去想办法解决啊，建议您和家人朋友沟通一下，想办法周转一下解决本期账单，这样可以避免再次逾期可能影响您的征信记录，您看行吗？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "D", "isEnd": "N", "stepNo": "two", "voiceId": 7921, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_B62.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=PQ66vssUIQ8%2BYX2SOrZgUi6WvHg%3D", "creator": "刘MM", "gmtCreated": 1741077144000, "modifier": "刘MM", "gmtModified": 1741077144000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "B63", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "拒绝还款", "intentionTts": "您之前是有过历史逾期记录的，如果这次您还是坚决要逾期处理的话，后续平台也可能将您视为高风险用户，一旦列入高风险，可能会影响您的信用评分，不要因小失大，好吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "D", "isEnd": "N", "stepNo": "two", "voiceId": 7867, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_B63.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=ff%2BKm3Ig1IN%2BH98r77TcIWeRHO8%3D", "creator": "刘MM", "gmtCreated": 1741077144000, "modifier": "刘MM", "gmtModified": 1741077144000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "B64", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "钱到就还", "intentionTts": "了解，但为避免逾期您需要在今晚12点前还款到账，建议您先找亲戚朋友周转下，等您资金到账再还给他们，这样也不会因账单逾期给您产生后续影响，好吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "D", "isEnd": "N", "stepNo": "two", "voiceId": 7868, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_B64.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=o4Ks8710yFPqtNI6jMMPf4AEits%3D", "creator": "刘MM", "gmtCreated": 1741077144000, "modifier": "刘MM", "gmtModified": 1741077144000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "B65", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "未还款-没有原因", "intentionTts": "您如果没有特别的原因的话，那您看1个小时内能处理一下吗？您现在就还款的话是有利于提升您的信用评分的，这样后续申请也会更方便一些，不是吗？", "includeVariable": "N", "nonInterrupt": 5, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": 7922, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_B65.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=pVag%2Fd0TQY5XtKYMK6rGpe%2FFiM4%3D", "creator": "刘MM", "gmtCreated": 1741077145000, "modifier": "刘MM", "gmtModified": 1741077145000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "C13", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "重听一次", "intentionTts": "我是说您在${loanProductName}的账单今天到期，系统仍未划扣到，您还款上有什么困难吗？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "C", "isEnd": "N", "stepNo": "three", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077145000, "modifier": "刘MM", "gmtModified": 1741077145000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "C131", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "重听二次", "intentionTts": "抱歉，可能是信号不太好，为避免再次逾期影响你在我司的信用评分，我们建议您在1小时内完成还款，再见。", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "C", "isEnd": "Y", "stepNo": "", "voiceId": 7877, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_C131.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=lqDnW0SkqGScwJustk5UaPmTF%2Fs%3D", "creator": "刘MM", "gmtCreated": 1741077145000, "modifier": "刘MM", "gmtModified": 1741077145000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "C1311", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "重听三次", "intentionTts": "出于对您个人情况的考虑，还是建议您今天就把款项处理好。毕竟逾期对您个人信用的影响是不可逆的。请您一定要把握好时间及时处理好这笔还款哈。不好意思打扰您了，再见。", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "C", "isEnd": "Y", "stepNo": "", "voiceId": 7925, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/C1311.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=4i6fMRUVHnEkujmt0S%2BiDgL2dkA%3D", "creator": "刘MM", "gmtCreated": 1741077145000, "modifier": "刘MM", "gmtModified": 1741077643000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "C14", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "拒识/超时一次", "intentionTts": "不好意思，刚刚没有听清楚您说的。您在${loanProductName}的账单今天就到期了，我们系统已经进行了多次划扣都没有成功。请问您在还款上遇到什么问题了吗？想问一下您是什么原因还没有还款呢？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "C", "isEnd": "N", "stepNo": "three", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077145000, "modifier": "刘MM", "gmtModified": 1741077145000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "C141", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "拒识/超时二次", "intentionTts": "再次提醒您一下，您在${loanProductName}的账单${repaymentAmount}元今天如果再不处理的话。明天就会逾期了，这样可能会影响您的个人信用评分。希望您能重视一下您的还款情况。那我先不打扰您了，再见。", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "C", "isEnd": "Y", "stepNo": "three", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077145000, "modifier": "刘MM", "gmtModified": 1741077145000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "C1411", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "拒识/超时三次", "intentionTts": "这个问题我这边暂时无法给您答复，建议您咨询一下${channel}客服或者直接拨打客服热线，您的账单今天到期了，麻烦您挂机后去处理一下。请您务必重视不要忘记了哈，打扰您了，再见。", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "C", "isEnd": "Y", "stepNo": "", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077145000, "modifier": "刘MM", "gmtModified": 1741077145000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "C51", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "承诺可以", "intentionTts": "好的，本期账单还请您尽快处理下，避免再次逾期影响您的信用评分，好吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": 7874, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_C51.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=V1RxnA58KQOVZZkyy4I4W0LieoI%3D", "creator": "刘MM", "gmtCreated": 1741077145000, "modifier": "刘MM", "gmtModified": 1741077145000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "C511", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "二次同意结束语", "intentionTts": "你的情况我已经再次记录下来了，也明确一下，此通电话全程录音，系统稍后查账，请务必重视不要忘记了，打扰了，再见。", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "A", "isEnd": "Y", "stepNo": "", "voiceId": 7878, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_C511.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=3YHcKuQj%2B6VpzRsaahbg8v46LGk%3D", "creator": "刘MM", "gmtCreated": 1741077145000, "modifier": "刘MM", "gmtModified": 1741077145000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "C5111", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "三次同意结束语", "intentionTts": "好的，您的承诺那我这边记录下来了，我们会准时查账，请您在约定时间前处理，再见。", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "A", "isEnd": "Y", "stepNo": "", "voiceId": 7881, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_C5111.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=QckfC5N%2BC7KFkGt0jXohTsTr2Sc%3D", "creator": "刘MM", "gmtCreated": 1741077146000, "modifier": "刘MM", "gmtModified": 1741077146000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "C52", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "稍后晚点处理", "intentionTts": "那您注意时间，系统在1个小时内会再次核账重新进行信用评估，请务必重视本期账单，这次您可千万别再逾期了，好吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": 7875, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_C52.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=GWjGNXmVY5Rb5VKr0cxf%2BkIinbU%3D", "creator": "刘MM", "gmtCreated": 1741077146000, "modifier": "刘MM", "gmtModified": 1741077146000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "C54", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "不坚定承诺", "intentionTts": "信用借款有还款时效，因为您之前有过逾期，现在就还款有利于提升后续您的信用评分，建议您1小时内处理下好吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": 7876, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_C54.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=1i%2BqnOnRf0iV3oc6HPf%2FcdUdboo%3D", "creator": "刘MM", "gmtCreated": 1741077146000, "modifier": "刘MM", "gmtModified": 1741077146000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Err", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "兜底处理", "intentionTts": "出于对您个人的考虑，还是今天处理好，毕竟逾期对您个人的影响是不可逆的，请把握好时间及时处理好还款。打扰了，再见。", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "E", "isEnd": "Y", "stepNo": "two", "voiceId": 7882, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Err.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=zyfZCVXsqGBo%2FsOepwjiJyCZEB8%3D", "creator": "刘MM", "gmtCreated": 1741077146000, "modifier": "刘MM", "gmtModified": 1741077146000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "R", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "已还款", "intentionTts": "我这边暂时没查到这个信息，可能是系统有点延迟，您方便通过${channel}自行查询一下吗，如果没有的话，请您尽快处理一下，这样可以避免造成不必要的误会，打扰您了，祝您生活愉快，再见。", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "A", "isEnd": "Y", "stepNo": "", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077146000, "modifier": "刘MM", "gmtModified": 1741077146000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "sendLink", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "发送还款链接", "intentionTts": "我稍后给您发送一条还款链接，支持支付宝以及添加银行卡等多种还款方式，若您不方便也可以将链接转给家人朋友帮您操作，短信收到后请尽快点击还款，那您今天内处理到账，可以吗？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": 7884, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_sendLink.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=blxGDnvWlChW4K%2FUoK3ISgZvlA0%3D", "creator": "刘MM", "gmtCreated": 1741077146000, "modifier": "刘MM", "gmtModified": 1741077146000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Start", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "开场白", "intentionTts": "您好，请问是${name}吗？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "E", "isEnd": "N", "stepNo": "one", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077146000, "modifier": "刘MM", "gmtModified": 1741077146000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Y", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "语音信箱", "intentionTts": "抱歉啊，${name}在${loanProductName}有一笔金融业务需要在今天处理，请务必今天处理一下，因为无法有效沟通，那我们这边先不打扰了，后续会找时间再联系的，再见。", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "E", "isEnd": "Y", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077146000, "modifier": "刘MM", "gmtModified": 1741077146000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z1", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "咨询还款方式", "intentionTts": "您可以还款到目前绑定的银行卡中，我们系统会定时进行划扣的，如果绑定卡有其他扣款或者无法使用的话，也可以通过${channel}手动处理一下，为了避免超过还款时效，今天 1 小时内还款可以吧？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077147000, "modifier": "刘MM", "gmtModified": 1741077147000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z10", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "转人工", "intentionTts": "现在无法在线帮您转接人工客服，客服热线可以在${channel}或官网上查询一下，您看本期账单可以在今天处理到账吗？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "C", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077147000, "modifier": "刘MM", "gmtModified": 1741077147000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z11", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "咨询账单明细", "intentionTts": "关于您的账单详细情况建议您可以登录${channel}查询一下，也可以通过贷款合同查看，那请您在 1 小时内处理一下，好吧？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "B", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077147000, "modifier": "刘MM", "gmtModified": 1741077147000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z12", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "投诉解答1", "intentionTts": "您先冷静下，我们这里是预提醒服务，也希望你配合进行还款，逾期会产生额外费用，也可能影响到你的信用记录，还请你尽快处理。", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "C", "isEnd": "N", "stepNo": "two", "voiceId": 7892, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z12.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=0Snv2LSCbFPZLWXuGy31xacllqY%3D", "creator": "刘MM", "gmtCreated": 1741077147000, "modifier": "刘MM", "gmtModified": 1741077147000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z121", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "投诉解答2", "intentionTts": "我很理解您现在的心情，希望您也冷静下，相互理解下，对您造成的影响深表歉意，打扰您了，再见。", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "C", "isEnd": "Y", "stepNo": "two", "voiceId": 7916, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z121.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=Hx9uh4gexrQgQKw5CzqCnatyP08%3D", "creator": "刘MM", "gmtCreated": 1741077147000, "modifier": "刘MM", "gmtModified": 1741077147000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z13", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "号码是哪里来的", "intentionTts": "这是您借款的时候留下的手机号码，来电就是提醒您一下，您在${loanProductName}的账单今日到期了，您看方便现在处理一下吗？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "B", "isEnd": "N", "stepNo": "three", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077147000, "modifier": "刘MM", "gmtModified": 1741077147000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z14", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "明确表示来意", "intentionTts": "我这边是提醒专员，您在${loanProductName}的账单今日到期了，稍后方便在${channel}上手动处理一下吗？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "B", "isEnd": "N", "stepNo": "three", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077147000, "modifier": "刘MM", "gmtModified": 1741077147000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z15", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "询问客服电话", "intentionTts": "客服热线可以在${channel}或官网上查询一下，您看这个欠款什么时候可以处理一下呢？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "B", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077147000, "modifier": "刘MM", "gmtModified": 1741077147000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z16", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "不支持还款", "intentionTts": "是这样的，如果绑定银行卡冻结的话可以换绑其他卡片，如果都不支持的话可以尝试在${channel}上用支付宝还款，若都还款不了的话建议您咨询一下${channel}，那您尽快去处理一下，好吧？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "one", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077147000, "modifier": "刘MM", "gmtModified": 1741077147000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z17", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "抱怨还款时间短", "intentionTts": "是这样的哈，因为我们系统划扣是在固定时间进行的，怕您可能遗忘还款导致错过划扣时间影响您的征信记录，所以建议您及时还款，还请您今天务必确保还款到账，可以吧?", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "B", "isEnd": "N", "stepNo": "two", "voiceId": 7924, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z17.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=WMsoUxgfySA%2FBxgMVmAP5g3EWeE%3D", "creator": "刘MM", "gmtCreated": 1741077147000, "modifier": "刘MM", "gmtModified": 1741077147000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z18", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "抱怨频繁来电", "intentionTts": "不好意思，给您多次来电也是怕您忘记还款，今天不还明天就是逾期状态了，所以才提醒您尽快处理，那您看今天什么时候能处理呢？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "C", "isEnd": "N", "stepNo": "two", "voiceId": 7896, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z18.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=rWgQrJfa3ncYx4hlYTuTWj3X2W8%3D", "creator": "刘MM", "gmtCreated": 1741077148000, "modifier": "刘MM", "gmtModified": 1741077148000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z181", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "抱怨频繁来电2", "intentionTts": "非常理解您的心情哈，主要之前有很多客户反映我们提醒不及时导致账户逾期，我也是为避免您工作生活太忙导致忘了才多次提醒您，希望您能理解下，好吧？", "includeVariable": "N", "nonInterrupt": 5, "belongTag": "E", "isEnd": "N", "stepNo": "two", "voiceId": 7917, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z181.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=G7iL%2Bg5s4uC00XAKkJpweG4d0xo%3D", "creator": "刘MM", "gmtCreated": 1741077148000, "modifier": "刘MM", "gmtModified": 1741077148000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z2", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "咨询金额", "intentionTts": "本期是还剩${repaymentAmount}没有还清的，其中${situation}，那您现在处理一下可以吧？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "three", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077148000, "modifier": "刘MM", "gmtModified": 1741077148000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z20", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "咨询提前还款", "intentionTts": "您后续可以通过${channel}手动还款方式对当期账单提前还款，手动还款操作不了的话，那您可以提前还款到银行卡中，系统到账单日会进行划扣的，那请问您今天能处理一下吗？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "B", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077148000, "modifier": "刘MM", "gmtModified": 1741077148000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z21", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "质疑是否机器人", "intentionTts": "我是您的智能客服助手，来电是提醒您今天及时还款，为避免您遗忘还款造成逾期影响您的信用评分，那您现在处理下可以吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "B", "isEnd": "N", "stepNo": "two", "voiceId": 7899, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z21.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=H888u1nrp8KF%2Fvvhkl3m8yAliaU%3D", "creator": "刘MM", "gmtCreated": 1741077148000, "modifier": "刘MM", "gmtModified": 1741077148000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z22", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "咨询额度使用问题", "intentionTts": "您好，信用额度是跟您平时的还款行为息息相关的，具体关于额度问题建议您咨询一下${channel}客服，那您本期账单 1 个小时能处理完毕吗？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "B", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077148000, "modifier": "刘MM", "gmtModified": 1741077148000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z23", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "询问逾期利息", "intentionTts": "关于具体的逾期利息数额建议您咨询一下${channel}客服或者拨打客服热线，您本期账单在 1 个小时内处理一下，可以吧？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "B", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077148000, "modifier": "刘MM", "gmtModified": 1741077148000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z25", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "抱怨手续费太高", "intentionTts": "您的借款利率当初签订合同时就有告知的，提前还款可能有利于后续借款利率的优惠，那本期建议您在1小时内处理下，好吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "B", "isEnd": "N", "stepNo": "two", "voiceId": 7902, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z25.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=6U%2FifxI4hMzQBBrV1ZP7h03q%2BXk%3D", "creator": "刘MM", "gmtCreated": 1741077148000, "modifier": "刘MM", "gmtModified": 1741077148000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z26", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "协商还款", "intentionTts": "您的账单今天理应全额处理，如果您一期账单都处理不了，那平台后面怎么跟您合作，为避免影响后续资金使用和您的信用评分，请务必今天处理到账，好吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "D", "isEnd": "N", "stepNo": "two", "voiceId": 7903, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z26.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=jxKjH2RsAMIsswUfQFCstMdmQE8%3D", "creator": "刘MM", "gmtCreated": 1741077148000, "modifier": "刘MM", "gmtModified": 1741077148000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z3", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "咨询还款时间", "intentionTts": "今天就是您的还款日，您现在就可以处理您的账单了，现在处理一下可以吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": 7886, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z3.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=RpA5j42jEa78%2BpWV0OkxtquozjI%3D", "creator": "刘MM", "gmtCreated": 1741077149000, "modifier": "刘MM", "gmtModified": 1741077149000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z31", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "咨询是否支持支付宝", "intentionTts": "不好意思啊，现在没有开通支付宝还款功能，这边可以通过${channel}进行还款，您也可以咨询一下${channel}了解其他还款方式，那您挂机后就处理一下可以吧？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077149000, "modifier": "刘MM", "gmtModified": 1741077149000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z32", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "咨询是否支持微信", "intentionTts": "现在没有开通微信还款功能的，建议您通过${channel}手动还款的方式操作，或者还款到您绑定卡中，若绑定卡不支持的话也可以更换绑定其他银行卡，那您现在处理一下可以吧？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077149000, "modifier": "刘MM", "gmtModified": 1741077149000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z33", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "申请延期", "intentionTts": "我司没有延期还款政策，借款是签有合同的，是需要您按时还款的，继续逾期的话可能会被列入高风险客户并移交到催收部门，为了确保以后资金周转方便，您尽快资金周转下，好吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "D", "isEnd": "N", "stepNo": "two", "voiceId": 7906, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z33.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=3h%2F5ib35Gi2B5Z6nCDUshcSVEmM%3D", "creator": "刘MM", "gmtCreated": 1741077149000, "modifier": "刘MM", "gmtModified": 1741077149000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z34", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "询问是否听得见", "intentionTts": "在听呢，您账单今天到期，请务必处理到账好吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "C", "isEnd": "N", "stepNo": "two", "voiceId": 7907, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z34.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=QEM4Se9mnEtgMsYsPmII5I0vjZg%3D", "creator": "刘MM", "gmtCreated": 1741077149000, "modifier": "刘MM", "gmtModified": 1741077149000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z341", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "询问是否听得见2", "intentionTts": "嗯，你说？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "E", "isEnd": "N", "stepNo": "two", "voiceId": 7918, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z341.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=%2Bs4AlUT1lg8HRTgaq3okJ2sprP0%3D", "creator": "刘MM", "gmtCreated": 1741077149000, "modifier": "刘MM", "gmtModified": 1741077149000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z36", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "复杂咨询", "intentionTts": "关于这个问题现在无法给您解答的，建议您咨询一下${channel}客服或者拨打客服热线，您本期账单在 1 个小时内处理一下可以吧？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "C", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077149000, "modifier": "刘MM", "gmtModified": 1741077149000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z37", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "咨询对公还款", "intentionTts": "不好意思啊，目前是没有对公还款的方式，建议您还是通过${channel}手动还一下，若有疑问的话也可以联系${channel}咨询一下，那您现在处理一下可以吧？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077149000, "modifier": "刘MM", "gmtModified": 1741077149000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z371", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "咨询对公还款2", "intentionTts": "不好意思啊，目前是没有对公还款的方式，建议您还是通过${channel}手动还一下，若有疑问的话也可以联系${channel}咨询一下，那您现在处理一下可以吧？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077149000, "modifier": "刘MM", "gmtModified": 1741077149000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z38", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "已与客服协商", "intentionTts": "是这样，新一期账单又出来了，不是说你前期账单没钱新账单就可以不用处理了，我司也没有延期还款政策，我们没有看到任何人帮你做了延期，本期账单还是尽快处理下，好吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "D", "isEnd": "N", "stepNo": "two", "voiceId": 7910, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z38.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=iwC0OdcxTiv3q1SsYkk1yLEWAYE%3D", "creator": "刘MM", "gmtCreated": 1741077149000, "modifier": "刘MM", "gmtModified": 1741077149000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z39", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "App无法打开", "intentionTts": "如果${channel}无法打开的话，建议您上官网咨询人工客服。我们这边也及时记录了您反馈的情况。同时请您务必确保绑定卡余额充足，我们会定时划扣的，那您尽快去处理一下，好吧？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "B", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077150000, "modifier": "刘MM", "gmtModified": 1741077150000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z4", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "咨询银行卡号", "intentionTts": "为了保护您这边的信息安全，您${loanProductName}的扣款卡号请通过登录${channel}自行查看一下，那现在处理一下可以吧？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "three", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077150000, "modifier": "刘MM", "gmtModified": 1741077150000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z40", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "询问工号", "intentionTts": "不好意思，我这边是贷后管理工作人员，目前没有分配工号的哈，如果您这边有问题，可以稍后登录${loanInstitutions}APP找在线客服沟通，请问您账单今天能处理到位吗？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "B", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077150000, "modifier": "刘MM", "gmtModified": 1741077150000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z41", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "咨询更换绑定卡", "intentionTts": "这边需要您到${channel}上换绑一下银行卡片，您可以到\"我的\"，然后找到银行卡，直接添加新卡，设置为默认还款卡就行了，具体操作过程中有不懂的地方可以问一下${channel}客服，那您尽快操作一下，今天务必处理到账哈？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077150000, "modifier": "刘MM", "gmtModified": 1741077150000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z42", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "咨询逾期影响", "intentionTts": "是这样，逾期对您的影响是不可逆的，不仅可能影响您的征信记录，且可能也对您日常生活和工作产生影响，另外也有额外的罚息费用，那为避免逾期对您造成严重影响，请尽快处理下，好吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "B", "isEnd": "N", "stepNo": "two", "voiceId": 7913, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z42.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=ucMa6uYqz7%2BGKDIDYdID5C4usjg%3D", "creator": "刘MM", "gmtCreated": 1741077150000, "modifier": "刘MM", "gmtModified": 1741077150000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z43", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "遗忘还款时间", "intentionTts": "了解，您可能太忙了哈，那为了避免错过扣款时间造成逾期，建议您在1个小时内处理到账，你看呢？", "includeVariable": "N", "nonInterrupt": 5, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": 7914, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z43.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=E1hDooXI1L0Sl5dr70eUZf2c31o%3D", "creator": "刘MM", "gmtCreated": 1741077150000, "modifier": "刘MM", "gmtModified": 1741077150000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z44", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "到线下银行处理", "intentionTts": "但是等不了那么久，您今天必须要还的，这样，我稍后给你发送一条还款短链，您可以转给家人朋友让他们先帮您还下，等你的银行卡处理好了再把钱转给他们，这样既能减少逾期息费，也能避免对您账户产生持续影响，您觉得呢？", "includeVariable": "N", "nonInterrupt": 5, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": 7915, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z44.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=MFs%2Fy5rcGlGpDolBukrJhsPxPqg%3D", "creator": "刘MM", "gmtCreated": 1741077150000, "modifier": "刘MM", "gmtModified": 1741077150000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z45", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "质疑还款时间", "intentionTts": "你的还款时间在合同上面是明确约定好的，你如果对还款时间有疑问的话，可以到${channel}或者合同上进行查看，那为避免错过今日还款时间影响你的信用，建议你今天尽快处理下，你看好吧？", "includeVariable": "Y", "nonInterrupt": 5, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077150000, "modifier": "刘MM", "gmtModified": 1741077150000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z5", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "咨询征信", "intentionTts": "逾期的话是可能影响征信的，您欠款金额也不多，为避免错过还款时间造成逾期影响，现在处理一下可以吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "B", "isEnd": "N", "stepNo": "two", "voiceId": 7887, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z5.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=RCLhz60Fp7sw6CHzpvt5%2BtLmgew%3D", "creator": "刘MM", "gmtCreated": 1741077150000, "modifier": "刘MM", "gmtModified": 1741077150000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z6", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "询问身份", "intentionTts": "这里是${loanProductName}，您在${loanProductName}的账单今天到期了，金额是${repaymentAmount}元，麻烦您在 1 小时内处理一下，可以吧？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "B", "isEnd": "N", "stepNo": "three", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077150000, "modifier": "刘MM", "gmtModified": 1741077150000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z7", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "咨询金额来由", "intentionTts": "您的还款相关信息是当初签订合同就有告知的哈，本期确实是还剩${repaymentAmount}元需要您处理一下，建议您在 1 小时内还款，您看处理一下好吧？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "B", "isEnd": "N", "stepNo": "three", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077151000, "modifier": "刘MM", "gmtModified": 1741077151000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z8", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "要求短信", "intentionTts": "短信稍后发出，请您注意查收，请您在1小时内完成还款，好吧？", "includeVariable": "N", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": 7888, "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "http://fin-za-ivr-nlu-open-prd.oss-cn-hzfinance.aliyuncs.com/fin-ivr-nlu-web/voice/public/139_Z8.wav?Expires=1741171738&OSSAccessKeyId=LTAI5tHFxJWVKtokwUutJ5Sa&Signature=mj2rPOxmqItxSnvrGLhmtnr8%2BvE%3D", "creator": "刘MM", "gmtCreated": 1741077151000, "modifier": "刘MM", "gmtModified": 1741077151000, "isDeleted": "N", "taskIdList": []}, {"taskId": 139, "intentionCode": "Z9", "version": "1.0", "remarks": "", "enabled": "N", "intentionName": "卡片锁掉", "intentionTts": "银行卡问题请联系银行客服处理一下，您也可以通过${channel}手动还款的方式还款，如果有其他疑问的话可以致电${channel}上的客服电话，那请问您今天能处理到账吗？", "includeVariable": "Y", "nonInterrupt": 0, "belongTag": "A", "isEnd": "N", "stepNo": "two", "voiceId": "", "testVoicePathCode": "", "prdVoicePathCode": "", "ossUrl": "", "creator": "刘MM", "gmtCreated": 1741077151000, "modifier": "刘MM", "gmtModified": 1741077151000, "isDeleted": "N", "taskIdList": []}]