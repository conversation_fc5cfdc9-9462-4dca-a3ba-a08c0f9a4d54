{"title": "Playwright MCP 使用指南", "description": "如何在 Cursor 中使用 Playwright MCP 进行浏览器自动化", "prerequisites": ["已安装 Node.js", "已安装 @playwright/mcp", "已配置 ~/.cursor/mcp.json", "已重启 Cursor"], "basic_usage": {"navigation": {"description": "导航到网页", "example": "请帮我打开 https://www.baidu.com"}, "screenshot": {"description": "截取页面截图", "example": "请帮我截取当前页面的截图"}, "interaction": {"description": "与页面元素交互", "example": "请帮我点击搜索框并输入'Playwright'"}}, "advanced_features": {"pdf_export": {"description": "导出页面为PDF", "example": "请帮我将当前页面保存为PDF"}, "network_monitoring": {"description": "监控网络请求", "example": "请帮我查看页面的网络请求"}, "multi_tab": {"description": "多标签页操作", "example": "请帮我打开新标签页并导航到GitHub"}}, "tips": ["使用自然语言描述您想要的操作", "Playwright MCP 支持多种浏览器", "可以使用无头模式进行后台操作", "支持移动设备模拟", "可以生成 Playwright 测试代码"]}