# TechBlog - 现代化个人博客网站

一个美观、现代化的个人博客网站，采用纯 HTML、CSS 和 JavaScript 构建，具有科技感的深色主题设计。

## 🌟 特性

### 设计特色

- **现代化深色主题** - 科技感十足的 UI 设计
- **渐变效果** - 精美的渐变背景和文字效果
- **玻璃拟态设计** - 使用 backdrop-filter 实现的现代玻璃效果
- **动态粒子背景** - 交互式粒子系统营造科技氛围
- **浮动科技图标** - 动态浮动的技术栈图标

### 功能特性

- **响应式设计** - 完美适配桌面、平板和移动设备
- **平滑滚动** - 自定义缓动函数实现的平滑滚动效果
- **打字机动画** - 终端风格的打字机效果
- **数字计数动画** - 统计数据的动态计数效果
- **懒加载** - 图片懒加载优化性能
- **主题切换** - 支持深色/浅色主题切换
- **移动端优化** - 触摸手势、滑动导航等移动端特性

## 安装

无需安装额外的依赖，只需下载`script_manager.py`文件即可。

## 使用方法

### 1. 创建脚本索引

首次使用或添加新脚本后，需要创建或更新索引：

```bash
python script_manager.py index
```

### 2. 列出所有脚本

查看所有已索引的脚本：

```bash
python script_manager.py list
```

### 3. 搜索脚本

根据关键词搜索脚本：

```bash
python script_manager.py search <关键词>
```

例如：

```bash
python script_manager.py search tts
```

### 4. 运行脚本

通过 ID 运行脚本：

```bash
python script_manager.py run <脚本ID>
```

例如：

```bash
python script_manager.py run 5
```

### 5. 添加标签

为脚本添加标签，便于分类和搜索：

```bash
python script_manager.py tag <脚本ID> <标签1> <标签2> ...
```

例如：

```bash
python script_manager.py tag 5 音频处理 TTS 工具
```

### 6. 按标签搜索

通过标签搜索脚本：

```bash
python script_manager.py tag_search <标签>
```

例如：

```bash
python script_manager.py tag_search 音频处理
```

## 自定义配置

您可以在`script_manager.py`文件中修改`CONFIG`字典来自定义配置：

- `database_file`：数据库文件的路径
- `scan_directories`：要扫描的目录列表
- `exclude_dirs`：要排除的目录列表

## 提示

1. 为了更好地组织脚本，建议在每个 Python 脚本的开头添加文档字符串，描述脚本的功能和用法。
2. 定期运行`index`命令更新脚本数据库。
3. 使用标签系统对脚本进行分类，例如"数据处理"、"音频工具"、"网络爬虫"等。
4. 可以将此脚本管理器添加到系统 PATH 中，方便在任何位置使用。

## 进阶用法

### 添加到系统 PATH

在 macOS 或 Linux 上，您可以创建一个符号链接到`/usr/local/bin`：

```bash
chmod +x script_manager.py
ln -s "$(pwd)/script_manager.py" /usr/local/bin/pyscripts
```

然后就可以在任何位置使用`pyscripts`命令了。

### 标签管理最佳实践

为了更有效地使用标签系统，建议采用以下标签分类方法：

1. **功能类别**：如"音频处理"、"图像处理"、"数据分析"等
2. **技术领域**：如"机器学习"、"网络爬虫"、"自然语言处理"等
3. **使用频率**：如"常用"、"偶尔使用"、"归档"等
4. **项目相关**：与特定项目相关的标签

### 未来计划功能

- 导出脚本列表为 HTML 或 Markdown 格式
- 脚本依赖分析
- 脚本版本控制
- 图形用户界面

# AI 催收话术迭代提效 PPT 制作说明

## 文件说明

本项目包含以下文件：

- `AI催收话术迭代提效.md` - 基本 PPT 内容大纲
- `流程对比图.md` - 流程对比图的 Mermaid 代码
- `PPT设计建议.md` - PPT 视觉设计指南
- `AI催收话术迭代提效-完整PPT内容.md` - 详细的 PPT 内容（推荐使用）

## 如何将 Markdown 转换为 PPT

### 方法一：手动创建（推荐，效果最佳）

1. 打开 PowerPoint 创建新演示文稿
2. 参考`PPT设计建议.md`中的配色方案和设计原则
3. 复制`AI催收话术迭代提效-完整PPT内容.md`中的内容
4. 按页面分隔线(`---`)创建新幻灯片
5. 根据 Markdown 格式手动设计幻灯片布局
6. 对于流程图，可使用 PowerPoint 的 SmartArt 或形状工具重新绘制

### 方法二：使用在线工具转换

1. **Slidev** (https://sli.dev/)

   - 支持 Markdown 直接转 PPT
   - 复制`AI催收话术迭代提效-完整PPT内容.md`到 Slidev 编辑器
   - 导出为 PPT 或 PDF

2. **Marp** (https://marp.app/)

   - 专为 Markdown 转 PPT 设计的工具
   - 安装 Marp CLI 或使用 VSCode 插件
   - 导入 Markdown 文件转换

3. **GitPitch** (https://gitpitch.com/)
   - 基于 Git 仓库的演示文稿创建工具
   - 上传 Markdown 文件自动转换为演示文稿

### 方法三：使用脚本自动转换

使用以下命令通过 pandoc 将 Markdown 转换为 PowerPoint:

```bash
# 安装pandoc（macOS）
brew install pandoc

# 转换（基本转换）
pandoc -t pptx -o AI催收话术迭代提效.pptx AI催收话术迭代提效-完整PPT内容.md

# 转换（使用参考模板）
pandoc -t pptx --reference-doc=template.pptx -o AI催收话术迭代提效.pptx AI催收话术迭代提效-完整PPT内容.md
```

## 流程图转换

对于`流程对比图.md`中的 Mermaid 图表:

1. 访问 https://mermaid.live/
2. 复制 Mermaid 代码到编辑器
3. 导出为图片（PNG/SVG）
4. 将图片插入到 PPT 中

## 注意事项

- 手动创建 PPT 可以获得最佳视觉效果
- 自动转换后通常需要手动调整格式和布局
- HTML 样式在转换过程中可能会丢失，需要重新设置
- 确保使用`PPT设计建议.md`中推荐的字体和颜色方案

# 企业微信聊天记录表格化工具

这个工具可以帮助你将企业微信的聊天记录转换为结构化的 Excel 表格，便于整理和分析同事提出的问题和回答。

## 特点

- 自动识别问题和回答，按照时间顺序组织
- 智能提取问题关键词
- 自动对问题进行分类
- 生成美观的 Excel 表格
- 支持创建统计摘要
- 支持多种企业微信聊天记录格式

## 安装

1. 确保你已安装 Python 3.7 或更高版本
2. 克隆或下载此仓库
3. 使用清华源安装依赖项：

```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

## 使用方法

### 基础版本

基础版本提供了简单的问答对提取功能：

```bash
python wechat_work_qa_table.py 聊天记录.txt
```

这将创建一个名为`聊天记录_qa_table.xlsx`的 Excel 文件。

### 高级版本

高级版本提供了更多功能，包括关键词提取、问题分类和数据摘要：

```bash
python advanced_wechat_work_qa_table.py 聊天记录.txt --summary
```

参数说明：

- `--output` 或 `-o`: 指定输出文件名
- `--custom-format` 或 `-f`: 自定义聊天记录格式的正则表达式
- `--summary` 或 `-s`: 创建数据摘要工作表

例如：

```bash
python advanced_wechat_work_qa_table.py 聊天记录.txt -o 问答汇总.xlsx -s
```

## 企业微信聊天记录格式

该工具支持以下格式的企业微信聊天记录：

1. 格式 1: `[日期 时间] 用户名: 消息内容`  
   例如：`[2025/03/25 10:30:45] 张三: 请问如何处理客户投诉?`

2. 格式 2: `用户名 日期 时间: 消息内容`  
   例如：`张三 2025/03/25 10:30:45: 请问如何处理客户投诉?`

如果你的聊天记录格式与上述不同，可以使用`--custom-format`参数指定自定义的正则表达式。

## 导出企业微信聊天记录的方法

1. 在企业微信中，打开需要导出聊天记录的对话
2. 在聊天窗口右上角，点击"更多"按钮（三个点）
3. 选择"导出聊天记录"
4. 根据提示选择导出格式（通常选择文本格式）
5. 选择保存位置，完成导出

## 自定义问题分类

如果需要自定义问题分类，可以修改`advanced_wechat_work_qa_table.py`文件中的`categorize_question`函数：

```python
def categorize_question(question, categories=None):
    if categories is None:
        # 默认分类，可根据需要修改
        categories = {
            '技术问题': ['如何', '怎么', '问题', '错误', 'bug', '失败', '故障'],
            '业务流程': ['流程', '审批', '步骤', '申请', '提交'],
            '产品信息': ['产品', '功能', '特性', '价格', '费用', '收费'],
            '人事相关': ['休假', '薪资', '报销', '考勤', '绩效', '晋升'],
            '其他问题': []  # 兜底分类
        }

    # 对问题进行分类
    for category, keywords in categories.items():
        for keyword in keywords:
            if keyword in question:
                return category

    return "其他问题"
```

## 输出示例

生成的 Excel 表格包含以下列：

- 问题时间
- 提问人
- 问题内容
- 回答时间
- 回答人
- 回答内容
- 关键词（高级版本）
- 问题分类（高级版本）

如果启用了摘要功能，还会生成一个额外的工作表，显示问题分类统计和提问人统计。

# Python 项目目录结构

本项目采用模块化组织结构，便于管理和维护。

## 目录结构

```
.
├── scripts/              # 通用脚本工具
│   ├── audio/            # 音频处理相关脚本
│   ├── excel/            # Excel处理工具
│   └── utils/            # 通用工具函数
├── data/                 # 数据文件
│   ├── excel/            # Excel数据
│   ├── audio/            # 音频数据
│   └── text/             # 文本数据、话术等
├── docs/                 # 文档
│   ├── tech_research/    # 技术调研文档
│   ├── guides/           # 指南文档
│   └── reports/          # 报告文档
├── tests/                # 测试代码
├── web/                  # Web相关文件
│   ├── html/             # HTML文件
│   ├── css/              # CSS文件
│   └── js/               # JavaScript文件
└── projects/             # 各个子项目
    ├── 催收系统MVP/      # 催收系统项目
    ├── openai-agents-designer/ # OpenAI代理设计器
    └── poker/            # 扑克项目
```

## 使用说明

1. 所有脚本工具位于`scripts`目录下，按功能分类
2. 数据文件存放在`data`目录下，按类型分类
3. 文档和报告存放在`docs`目录下
4. 测试代码位于`tests`目录下
5. Web 相关文件位于`web`目录下
6. 各个独立子项目位于`projects`目录下

## 依赖安装

```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

## 注意事项

- 请在相应目录下添加新文件，保持目录结构清晰
- 更新 requirements.txt 时记得指定版本号
- 子项目可以有自己的 requirements.txt 文件
