#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import random
import re

def generate_json(text='', userPropertiesMap=None):
    # 为 userPropertiesMap 提供保护性空值
    if userPropertiesMap is None:
        userPropertiesMap = {}

    text = text.replace('，', ',')
    # 定义中断关键词列表
    interrupt_keywords = ['正在通话中', '留言', '超时', '机主', '无人接听',
                          '转达', '来电秘书', '爱莫能助', '助理', '感谢', '呼叫', '喂，你好，呃，你有什么事？', '不方便接电话', '总共需要还多少钱？', '逾期的话利息是多少来着', '请问您还有什么要补充的', '所需还款金额数是多少', '得还多少钱呀？', '请问逾期利息是多少呢？', '其他需要补充', '智能']
    # 非本人关键词列表
    not_self_keywords = ['不是', '不认', '错', '上面干活','出去','手机']

    # 检查文本是否包含任何中断关键词
    for keyword in interrupt_keywords:
        if re.search(keyword, text):
            return {'id': 'FAQ-026', 'flow': 'B1'}

    # 检查是否包含非本人关键词
    for keyword in not_self_keywords:
        if re.search(keyword, text):
            return {'id': 'A02', 'flow': 'B2'}

    # 默认返回 A01，但需要根据 userPropertiesMap.jdzad_overdue_status_type 进行特殊处理
    overdue_status_type = userPropertiesMap.get('jdzad_overdue_status_type', '')

    if overdue_status_type == "惯性逾期":
        # 从惯性逾期的5条话术中随机选择一条
        script_options = ['A01_Inertial_Overdue_01', 'A01_Inertial_Overdue_02', 'A01_Inertial_Overdue_03', 
                         'A01_Inertial_Overdue_04', 'A01_Inertial_Overdue_05']
        selected_script = random.choice(script_options)
        return {'id': selected_script, 'flow': 'B1'}
    elif overdue_status_type == "偶尔逾期":
        # 从偶尔逾期的5条话术中随机选择一条
        script_options = ['A01_Occasional_Overdue_01', 'A01_Occasional_Overdue_02', 'A01_Occasional_Overdue_03', 
                         'A01_Occasional_Overdue_04', 'A01_Occasional_Overdue_05']
        selected_script = random.choice(script_options)
        return {'id': selected_script, 'flow': 'B1'}
    elif overdue_status_type == "首次逾期":
        # 从首次逾期的8条话术中随机选择一条
        script_options = ['A01_First_Overdue_01', 'A01_First_Overdue_02', 'A01_First_Overdue_03', 
                         'A01_First_Overdue_04', 'A01_First_Overdue_05', 'A01_First_Overdue_06',
                         'A01_First_Overdue_07', 'A01_First_Overdue_08']
        selected_script = random.choice(script_options)
        return {'id': selected_script, 'flow': 'B1'}
    else:
        # 其他情况，正常输出 A01
        return {'id': 'A01', 'flow': 'B1'}


def test_script_selection():
    """测试脚本选择逻辑"""
    print("🧪 测试脚本随机选择逻辑")
    print("=" * 60)
    
    # 测试惯性逾期
    print("\n【惯性逾期测试】:")
    habitual_results = []
    for i in range(10):
        result = generate_json('', {'jdzad_overdue_status_type': '惯性逾期'})
        habitual_results.append(result['id'])
        print(f"第{i+1}次: {result['id']}")
    
    print(f"惯性逾期选择的话术ID: {set(habitual_results)}")
    
    # 测试偶尔逾期
    print("\n【偶尔逾期测试】:")
    occasional_results = []
    for i in range(10):
        result = generate_json('', {'jdzad_overdue_status_type': '偶尔逾期'})
        occasional_results.append(result['id'])
        print(f"第{i+1}次: {result['id']}")
    
    print(f"偶尔逾期选择的话术ID: {set(occasional_results)}")
    
    # 测试首次逾期
    print("\n【首次逾期测试】:")
    first_results = []
    for i in range(10):
        result = generate_json('', {'jdzad_overdue_status_type': '首次逾期'})
        first_results.append(result['id'])
        print(f"第{i+1}次: {result['id']}")
    
    print(f"首次逾期选择的话术ID: {set(first_results)}")
    
    # 测试其他情况
    print("\n【其他情况测试】:")
    other_result = generate_json('', {'jdzad_overdue_status_type': '其他'})
    print(f"其他情况: {other_result['id']}")
    
    # 测试中断关键词
    print("\n【中断关键词测试】:")
    interrupt_result = generate_json('正在通话中', {'jdzad_overdue_status_type': '首次逾期'})
    print(f"中断关键词: {interrupt_result['id']}")
    
    # 测试非本人关键词
    print("\n【非本人关键词测试】:")
    not_self_result = generate_json('不是我', {'jdzad_overdue_status_type': '首次逾期'})
    print(f"非本人关键词: {not_self_result['id']}")

if __name__ == "__main__":
    test_script_selection()
