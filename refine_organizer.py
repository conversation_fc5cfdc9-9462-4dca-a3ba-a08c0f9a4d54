#!/usr/bin/env python3
"""
文件细分整理工具 - 对已分类的文件进行进一步细分
"""

import os
import shutil
import json
from pathlib import Path
from collections import defaultdict
import re
from datetime import datetime

class RefineOrganizer:
    def __init__(self, organized_dir="/Users/<USER>/Downloads/整理后的文件"):
        self.organized_dir = Path(organized_dir)
        
        # 细分规则
        self.refine_rules = {
            "02_催收话术配置": {
                "01_灵犀平台": {
                    "keywords": ["灵犀", "M1"],
                    "patterns": [r".*灵犀.*", r".*M1.*"]
                },
                "02_金融机构": {
                    "keywords": ["众安", "众利", "金融"],
                    "patterns": [r".*众安.*", r".*众利.*", r".*金融.*"]
                },
                "03_意图分类": {
                    "keywords": ["intentions"],
                    "patterns": [r".*intentions.*"]
                },
                "04_话术配置": {
                    "keywords": ["话术", "催收"],
                    "patterns": [r".*话术.*", r".*催收.*"]
                },
                "99_其他催收": {}
            },
            "01_AI语音训练素材": {
                "01_参考音频": {
                    "keywords": ["参考音频", "参考", "周志豪", "于沛", "胡中银", "张帅", "litianwei"],
                    "patterns": [r".*参考.*", r".*周志豪.*", r".*于沛.*", r".*胡中银.*"]
                },
                "02_TTS训练素材": {
                    "keywords": ["TTS", "训练", "素材", "GPT", "SoVITS", "音色"],
                    "patterns": [r".*TTS.*", r".*训练.*", r".*素材.*", r".*音色.*"]
                },
                "03_生成音频": {
                    "keywords": ["generated", "output", "tts_preview"],
                    "patterns": [r".*generated.*", r".*output.*", r".*preview.*"]
                },
                "99_其他语音": {}
            },
            "06_录音文件": {
                "01_通话录音": {
                    "keywords": ["sip", "call", "通话"],
                    "patterns": [r".*sip.*", r".*call.*", r".*通话.*"]
                },
                "02_音频片段": {
                    "keywords": ["audio", "录音"],
                    "patterns": [r".*audio.*", r".*录音.*"]
                },
                "03_开场白": {
                    "keywords": ["开场", "开场白"],
                    "patterns": [r".*开场.*"]
                },
                "99_其他录音": {}
            },
            "11_配置文件": {
                "01_系统配置": {
                    "keywords": ["config", "设置", "配置"],
                    "patterns": [r".*config.*", r".*设置.*"]
                },
                "02_业务配置": {
                    "keywords": ["copy", "技能", "流程"],
                    "patterns": [r".*copy.*", r".*技能.*", r".*流程.*"]
                },
                "03_监控告警": {
                    "keywords": ["监工", "告警", "触达"],
                    "patterns": [r".*监工.*", r".*告警.*", r".*触达.*"]
                },
                "99_其他配置": {}
            },
            "99_其他文件": {
                "01_音频文件": {
                    "extensions": ['.wav', '.mp3', '.m4a']
                },
                "02_数据表格": {
                    "extensions": ['.xls', '.xlsx']
                },
                "03_JSON配置": {
                    "extensions": ['.json']
                },
                "04_网页文件": {
                    "extensions": ['.html', '.htm']
                },
                "05_文档文件": {
                    "extensions": ['.txt', '.md', '.doc', '.docx']
                },
                "06_视频文件": {
                    "extensions": ['.m4s', '.mp4', '.avi']
                },
                "07_模板文件": {
                    "extensions": ['.dot']
                },
                "99_未分类": {}
            }
        }
    
    def categorize_file_refined(self, file_path, category):
        """对文件进行细分分类"""
        file_name = file_path.name.lower()
        file_ext = file_path.suffix.lower()
        
        if category not in self.refine_rules:
            return None
        
        rules = self.refine_rules[category]
        
        for subcat_name, subcat_rules in rules.items():
            if subcat_name == "99_其他配置" or subcat_name == "99_其他催收" or subcat_name == "99_其他语音" or subcat_name == "99_其他录音":
                continue  # 跳过默认分类，最后处理
            
            # 检查扩展名
            if file_ext in subcat_rules.get("extensions", []):
                return subcat_name
            
            # 检查关键词
            for keyword in subcat_rules.get("keywords", []):
                if keyword.lower() in file_name:
                    return subcat_name
            
            # 检查正则表达式
            for pattern in subcat_rules.get("patterns", []):
                if re.search(pattern, file_name, re.IGNORECASE):
                    return subcat_name
        
        # 如果没有匹配，返回默认分类
        default_keys = [k for k in rules.keys() if k.startswith("99_")]
        return default_keys[0] if default_keys else None
    
    def create_subdirectories(self, category_dir, category):
        """为指定分类创建子目录"""
        if category not in self.refine_rules:
            return
        
        rules = self.refine_rules[category]
        for subcat_name in rules.keys():
            subcat_dir = category_dir / subcat_name
            subcat_dir.mkdir(exist_ok=True)
    
    def refine_category(self, category):
        """细分指定分类的文件"""
        category_dir = self.organized_dir / category
        if not category_dir.exists():
            print(f"分类目录不存在: {category}")
            return {}
        
        # 创建子目录
        self.create_subdirectories(category_dir, category)
        
        moved_files = defaultdict(list)
        
        # 遍历分类目录中的文件
        for item in category_dir.iterdir():
            if item.is_file():
                subcategory = self.categorize_file_refined(item, category)
                if subcategory:
                    target_dir = category_dir / subcategory
                    target_path = target_dir / item.name
                    
                    # 处理重名文件
                    counter = 1
                    original_target = target_path
                    while target_path.exists():
                        stem = original_target.stem
                        suffix = original_target.suffix
                        target_path = target_dir / f"{stem}_{counter}{suffix}"
                        counter += 1
                    
                    try:
                        shutil.move(str(item), str(target_path))
                        moved_files[subcategory].append(item.name)
                        print(f"移动: {item.name} -> {category}/{subcategory}/")
                    except Exception as e:
                        print(f"移动文件失败: {item.name} -> {e}")
        
        return moved_files
    
    def refine_all(self):
        """细分所有需要细分的分类"""
        all_moved = {}
        
        for category in self.refine_rules.keys():
            print(f"\n🔍 正在细分: {category}")
            moved = self.refine_category(category)
            if moved:
                all_moved[category] = moved
                print(f"✅ {category} 细分完成，移动了 {sum(len(files) for files in moved.values())} 个文件")
            else:
                print(f"⚠️ {category} 没有文件需要移动")
        
        return all_moved
    
    def generate_report(self, moved_files):
        """生成细分报告"""
        report = {
            "细分时间": datetime.now().isoformat(),
            "细分结果": moved_files,
            "统计": {}
        }
        
        for category, subcats in moved_files.items():
            report["统计"][category] = {
                "子分类数量": len(subcats),
                "文件总数": sum(len(files) for files in subcats.values()),
                "子分类详情": {subcat: len(files) for subcat, files in subcats.items()}
            }
        
        # 保存报告
        report_path = self.organized_dir / "文件细分报告.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📋 细分报告已保存到: {report_path}")
        return report

def main():
    organizer = RefineOrganizer()
    
    print("🔧 开始文件细分整理...")
    moved_files = organizer.refine_all()
    
    if moved_files:
        print("\n📊 生成细分报告...")
        report = organizer.generate_report(moved_files)
        
        print("\n✅ 文件细分完成!")
        print(f"总共细分了 {len(moved_files)} 个分类")
        
        for category, stats in report["统计"].items():
            print(f"  {category}: {stats['文件总数']} 个文件分为 {stats['子分类数量']} 个子分类")
    else:
        print("\n⚠️ 没有文件需要细分")

if __name__ == "__main__":
    main()
