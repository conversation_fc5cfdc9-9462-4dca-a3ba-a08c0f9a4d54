#!/usr/bin/env python3
"""
Playwright MCP 功能演示脚本
展示 Playwright MCP 的各种功能和用法示例
"""

import subprocess
import time
import json
import os
from pathlib import Path

class PlaywrightMCPDemo:
    def __init__(self):
        self.output_dir = Path("playwright_mcp_output")
        self.output_dir.mkdir(exist_ok=True)
        
    def show_available_tools(self):
        """显示可用的工具列表"""
        print("🛠️ Playwright MCP 可用工具:")
        print("="*50)
        
        tools = [
            ("browser_navigate", "导航到指定URL"),
            ("browser_snapshot", "获取页面可访问性快照"),
            ("browser_take_screenshot", "截取页面截图"),
            ("browser_click", "点击页面元素"),
            ("browser_type", "在输入框中输入文本"),
            ("browser_wait_for", "等待特定条件"),
            ("browser_pdf_save", "保存页面为PDF"),
            ("browser_tab_new", "打开新标签页"),
            ("browser_tab_list", "列出所有标签页"),
            ("browser_close", "关闭浏览器"),
            ("browser_resize", "调整浏览器窗口大小"),
            ("browser_network_requests", "获取网络请求"),
            ("browser_console_messages", "获取控制台消息"),
        ]
        
        for tool, description in tools:
            print(f"  📌 {tool}: {description}")
        
        print("\n🎯 特殊功能:")
        print("  🔍 Vision Mode: 使用 --vision 参数启用截图模式")
        print("  🤖 Headless Mode: 使用 --headless 参数启用无头模式")
        print("  🌐 多浏览器支持: Chrome, Firefox, WebKit, Edge")
        print("  📱 设备模拟: 支持移动设备模拟")
        
    def show_configuration_examples(self):
        """显示配置示例"""
        print("\n⚙️ 配置示例:")
        print("="*50)
        
        configs = {
            "基本配置": {
                "command": "npx",
                "args": ["@playwright/mcp@latest"]
            },
            "无头模式": {
                "command": "npx", 
                "args": ["@playwright/mcp@latest", "--headless"]
            },
            "指定浏览器": {
                "command": "npx",
                "args": ["@playwright/mcp@latest", "--browser", "firefox"]
            },
            "移动设备模拟": {
                "command": "npx",
                "args": ["@playwright/mcp@latest", "--device", "iPhone 15"]
            },
            "Vision模式": {
                "command": "npx",
                "args": ["@playwright/mcp@latest", "--vision"]
            },
            "独立服务器": {
                "command": "npx",
                "args": ["@playwright/mcp@latest", "--port", "8931"]
            }
        }
        
        for name, config in configs.items():
            print(f"\n📋 {name}:")
            print(f"   {json.dumps(config, indent=4, ensure_ascii=False)}")
    
    def show_usage_examples(self):
        """显示使用示例"""
        print("\n💡 使用示例:")
        print("="*50)
        
        examples = [
            {
                "场景": "网页截图",
                "步骤": [
                    "1. browser_navigate: 导航到目标网站",
                    "2. browser_take_screenshot: 截取页面截图",
                    "3. 截图会保存到输出目录"
                ]
            },
            {
                "场景": "表单填写",
                "步骤": [
                    "1. browser_navigate: 打开表单页面",
                    "2. browser_click: 点击输入框",
                    "3. browser_type: 输入文本内容",
                    "4. browser_click: 点击提交按钮"
                ]
            },
            {
                "场景": "网页内容提取",
                "步骤": [
                    "1. browser_navigate: 打开目标页面",
                    "2. browser_snapshot: 获取页面结构",
                    "3. 分析返回的可访问性树结构"
                ]
            },
            {
                "场景": "多标签页操作",
                "步骤": [
                    "1. browser_tab_new: 打开新标签页",
                    "2. browser_tab_list: 查看所有标签页",
                    "3. browser_tab_select: 切换标签页",
                    "4. browser_tab_close: 关闭标签页"
                ]
            }
        ]
        
        for example in examples:
            print(f"\n🎯 {example['场景']}:")
            for step in example['步骤']:
                print(f"   {step}")
    
    def show_integration_tips(self):
        """显示集成建议"""
        print("\n🔗 集成建议:")
        print("="*50)
        
        tips = [
            "🎭 与AI助手集成: 可以让AI助手自动执行网页操作",
            "📊 数据收集: 自动化网页数据抓取和分析",
            "🧪 自动化测试: 生成和执行网页测试用例",
            "📸 内容监控: 定期截图监控网页变化",
            "🔍 可访问性检查: 使用快照功能检查网页可访问性",
            "📱 跨设备测试: 模拟不同设备进行兼容性测试",
            "🌐 多浏览器测试: 在不同浏览器中验证功能",
            "📄 文档生成: 自动生成网页操作文档"
        ]
        
        for tip in tips:
            print(f"  {tip}")
    
    def create_sample_config(self):
        """创建示例配置文件"""
        print("\n📝 创建示例配置文件...")
        
        sample_configs = {
            "mcp_playwright_basic.json": {
                "mcpServers": {
                    "playwright": {
                        "command": "npx",
                        "args": ["@playwright/mcp@latest"]
                    }
                }
            },
            "mcp_playwright_advanced.json": {
                "mcpServers": {
                    "playwright-headless": {
                        "command": "npx",
                        "args": ["@playwright/mcp@latest", "--headless", "--browser", "chromium"]
                    },
                    "playwright-mobile": {
                        "command": "npx",
                        "args": ["@playwright/mcp@latest", "--device", "iPhone 15", "--headless"]
                    },
                    "playwright-vision": {
                        "command": "npx",
                        "args": ["@playwright/mcp@latest", "--vision", "--headless"]
                    }
                }
            }
        }
        
        for filename, config in sample_configs.items():
            config_path = self.output_dir / filename
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print(f"✅ 已创建: {config_path}")
    
    def show_troubleshooting(self):
        """显示故障排除指南"""
        print("\n🔧 故障排除:")
        print("="*50)
        
        issues = [
            {
                "问题": "浏览器未安装",
                "解决方案": "运行 'npx playwright install' 安装浏览器"
            },
            {
                "问题": "权限错误",
                "解决方案": "使用 --no-sandbox 参数或检查系统权限"
            },
            {
                "问题": "端口冲突",
                "解决方案": "使用 --port 参数指定不同端口"
            },
            {
                "问题": "网络问题",
                "解决方案": "配置代理或检查网络连接"
            },
            {
                "问题": "MCP连接失败",
                "解决方案": "检查配置文件格式和重启Cursor"
            }
        ]
        
        for issue in issues:
            print(f"\n❓ {issue['问题']}:")
            print(f"   💡 {issue['解决方案']}")

def main():
    """主函数"""
    print("🎭 Playwright MCP 功能演示")
    print("="*60)
    
    demo = PlaywrightMCPDemo()
    
    # 显示各种信息
    demo.show_available_tools()
    demo.show_configuration_examples()
    demo.show_usage_examples()
    demo.show_integration_tips()
    demo.show_troubleshooting()
    
    # 创建示例文件
    demo.create_sample_config()
    
    print("\n" + "="*60)
    print("🎉 Playwright MCP 演示完成！")
    print(f"📁 示例文件已保存到: {demo.output_dir}")
    print("\n🚀 现在您可以:")
    print("   1. 重启 Cursor 以加载 MCP 配置")
    print("   2. 在 Cursor 中使用 Playwright 功能")
    print("   3. 参考示例配置文件进行自定义")
    print("   4. 查看输出目录中的示例文件")

if __name__ == "__main__":
    main()
