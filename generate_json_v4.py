def generate_json(id, preId_list=None, customer_reply=None, faq_id="未匹配"):
    """
    意图识别函数 V4 - 适配二级分类意图识别系统
    支持A01/A02/A03、B01/B02/B03二级分类，优先匹配二级分类
    
    Args:
        id: 意图识别结果，可能是一级大类（A-H）或二级分类（A01/A02/A03, B01/B02/B03）
        preId_list: 历史意图ID列表
        customer_reply: 客户回复内容
        faq_id: FAQ匹配结果
    
    Returns:
        dict: 包含intent_id、preId_list、flow的字典
    """
    if preId_list is None:
        preId_list = []
    else:
        # 过滤掉 preId_list 中的 "None"
        preId_list = [x for x in preId_list if x != "None"]

    def check_repetition(intent_id, preId_list):
        """检查重复次数并决定是否返回End1或End2"""
        # 计算当前意图在历史记录中的总出现次数
        count = preId_list.count(intent_id)

        # 如果当前意图已经出现3次或以上
        if count >= 3:
            # 检查 preId_list 中是否已经有 End1
            if "End1" in preId_list:
                return "End2"
            else:
                return "End1"

        return intent_id

    def handle_h_series(intent_id, preId_list):
        """H系列的ID处理函数"""
        if intent_id == "H":
            h_progression = ["H", "H1", "H11", "H111", "H1111"]
            # 找到preId_list中最后一个H系列ID的位置
            last_h_index = -1
            for i, h_id in enumerate(h_progression):
                if h_id in preId_list:
                    last_h_index = i

            # 如果找到了H系列ID，返回下一级ID（除非已经是最后一级）
            if last_h_index >= 0 and last_h_index < len(h_progression) - 1:
                return h_progression[last_h_index + 1]
            return "H"
        return intent_id

    def get_primary_category(intent_id):
        """获取主要类别（一级分类）"""
        # 二级分类映射到一级分类
        secondary_to_primary = {
            "A01": "A", "A02": "A", "A03": "A",
            "B01": "B", "B02": "B", "B03": "B"
        }
        return secondary_to_primary.get(intent_id, intent_id)

    def get_fallback_id(intent_id):
        """根据意图ID获取对应的兜底ID"""
        # 所有A开头的意图都走SB6111
        if intent_id.startswith("A"):
            return "SB6111"
        # 所有B开头的意图都走SB6121  
        elif intent_id.startswith("B"):
            return "SB6121"
        # 其他意图的兜底ID映射
        elif intent_id == "C":
            return "SB6131"
        elif intent_id in ["F01", "F02", "D"]:
            return "SC5111"
        
        return None

    # 处理输入的意图ID
    new_id = id
    primary_id = get_primary_category(id)

    if "未匹配" not in faq_id:
        # FAQ匹配场景处理
        if faq_id == "SZ52":
            if "SZ52" in preId_list:
                intent_id = handle_h_series("H", preId_list)
            else:
                intent_id = "SZ52"
        elif id == "H" and faq_id == "H02":
            intent_id = handle_h_series("H", preId_list)
        else:
            intent_id = faq_id

            # 检查 faq_id 是否在特定的列表中并处理重复
            if intent_id in ["G01", "G02", 'G03'] and intent_id in preId_list:
                intent_id = f"{intent_id}11"
            elif intent_id in ["G02", "G03", "SZ12", "SB00", "SZ40", "SZ11", "SZ34", "SZ47","SZ37","SZ53","SZ27","SZ54"] and intent_id in preId_list:
                intent_id = f"{intent_id}1"

        # 检查重复次数
        intent_id = check_repetition(intent_id, preId_list)

        # FAQ匹配场景的flow控制
        flow = "D"

    else:
        # 未匹配FAQ场景处理
        if new_id == "H":
            # H系列特殊处理
            new_id = handle_h_series(new_id, preId_list)
        else:
            # 第一步：检查是否是特殊ID，如果是且重复则添加后缀
            if new_id in ["G01", "G02"] and new_id in preId_list:
                new_id = f"{new_id}11"  # G01/G02特殊处理，添加"11"后缀
            else:
                # 第二步：兜底ID处理 - 根据意图类型决定兜底ID
                fallback_id = get_fallback_id(new_id)
                if fallback_id:
                    new_id = fallback_id

        intent_id = new_id
        # 检查重复次数
        intent_id = check_repetition(intent_id, preId_list)

        flow = "D"
    
    # 直接添加到 preId_list
    preId_list.append(intent_id)

    return {
        "intent_id": intent_id,
        "preId_list": preId_list,
        "flow": flow
    }