<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深入理解现代前端架构 - TechBlog</title>
    <meta name="description" content="探索现代前端开发的最佳实践，从组件化设计到性能优化的完整指南">
    <meta name="keywords" content="前端开发, React, Vue, 性能优化, 组件化">
    
    <!-- Open Graph -->
    <meta property="og:title" content="深入理解现代前端架构">
    <meta property="og:description" content="探索现代前端开发的最佳实践，从组件化设计到性能优化的完整指南">
    <meta property="og:image" content="https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=1200&h=630&fit=crop">
    <meta property="og:type" content="article">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="article.css">
</head>
<body>
    <!-- 粒子背景 -->
    <div id="particles-js"></div>
    
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="index.html" class="brand-link">
                    <i class="fas fa-code"></i>
                    <span>TechBlog</span>
                </a>
            </div>
            
            <div class="nav-menu">
                <a href="index.html#home" class="nav-link">首页</a>
                <a href="index.html#articles" class="nav-link">文章</a>
                <a href="index.html#about" class="nav-link">关于</a>
                <a href="index.html#contact" class="nav-link">联系</a>
            </div>
            
            <div class="nav-actions">
                <button class="theme-toggle" aria-label="切换主题">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 文章头部 -->
    <header class="article-header">
        <div class="container">
            <div class="article-meta-top">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    <span>返回首页</span>
                </a>
                <div class="article-category">
                    <span class="category-tag frontend">前端开发</span>
                </div>
            </div>
            
            <h1 class="article-title">深入理解现代前端架构</h1>
            <p class="article-subtitle">探索现代前端开发的最佳实践，从组件化设计到性能优化的完整指南</p>
            
            <div class="article-meta">
                <div class="author-info">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face" alt="作者头像" class="author-avatar">
                    <div class="author-details">
                        <span class="author-name">张开发</span>
                        <span class="author-title">高级前端工程师</span>
                    </div>
                </div>
                
                <div class="article-stats">
                    <div class="stat-item">
                        <i class="fas fa-calendar"></i>
                        <span>2024年1月15日</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span>8分钟阅读</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-eye"></i>
                        <span>1,234 次浏览</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-heart"></i>
                        <span>89 点赞</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 文章内容 -->
    <main class="article-main">
        <div class="container">
            <div class="article-layout">
                <!-- 文章内容区 -->
                <article class="article-content">
                    <div class="article-image">
                        <img src="https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=1200&h=600&fit=crop" alt="现代前端架构" class="featured-image">
                    </div>
                    
                    <div class="article-body">
                        <div class="article-intro">
                            <p>在快速发展的前端技术生态中，构建可维护、可扩展的应用架构变得越来越重要。本文将深入探讨现代前端架构的核心概念和最佳实践。</p>
                        </div>

                        <h2>组件化设计原则</h2>
                        <p>组件化是现代前端开发的基石。通过将UI拆分为独立、可复用的组件，我们可以提高代码的可维护性和开发效率。</p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">JavaScript</span>
                                <button class="copy-btn" data-code="component-example">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <pre><code id="component-example">// React 函数组件示例
const Button = ({ variant, children, onClick }) => {
  return (
    &lt;button 
      className={`btn btn-${variant}`}
      onClick={onClick}
    &gt;
      {children}
    &lt;/button&gt;
  );
};

// 使用组件
&lt;Button variant="primary" onClick={handleClick}&gt;
  点击我
&lt;/Button&gt;</code></pre>
                        </div>

                        <h3>组件设计的关键原则</h3>
                        <ul>
                            <li><strong>单一职责</strong>：每个组件应该只负责一个功能</li>
                            <li><strong>可复用性</strong>：设计通用的、可配置的组件</li>
                            <li><strong>可组合性</strong>：组件应该能够灵活组合</li>
                            <li><strong>可测试性</strong>：组件应该易于单元测试</li>
                        </ul>

                        <h2>状态管理策略</h2>
                        <p>随着应用复杂度的增加，状态管理成为了一个关键挑战。选择合适的状态管理方案对应用的可维护性至关重要。</p>

                        <div class="info-box">
                            <div class="info-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="info-content">
                                <h4>最佳实践提示</h4>
                                <p>对于小型应用，使用组件内部状态和 Context API 通常就足够了。只有当状态管理变得复杂时，才考虑引入 Redux 或 Zustand 等状态管理库。</p>
                            </div>
                        </div>

                        <h2>性能优化技巧</h2>
                        <p>性能优化是前端开发中的重要环节。以下是一些关键的优化策略：</p>

                        <div class="performance-grid">
                            <div class="performance-item">
                                <div class="performance-icon">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <h4>代码分割</h4>
                                <p>使用动态导入和懒加载减少初始包大小</p>
                            </div>
                            <div class="performance-item">
                                <div class="performance-icon">
                                    <i class="fas fa-memory"></i>
                                </div>
                                <h4>内存优化</h4>
                                <p>避免内存泄漏，合理使用 useCallback 和 useMemo</p>
                            </div>
                            <div class="performance-item">
                                <div class="performance-icon">
                                    <i class="fas fa-image"></i>
                                </div>
                                <h4>资源优化</h4>
                                <p>图片懒加载、压缩和现代格式使用</p>
                            </div>
                        </div>

                        <h2>总结</h2>
                        <p>现代前端架构的核心在于平衡复杂性和可维护性。通过合理的组件设计、状态管理和性能优化，我们可以构建出既强大又易于维护的前端应用。</p>

                        <p>记住，架构设计没有银弹，最重要的是根据项目的具体需求选择合适的技术栈和架构模式。</p>
                    </div>

                    <!-- 文章标签 -->
                    <div class="article-tags">
                        <h4>相关标签</h4>
                        <div class="tags-list">
                            <span class="tag">React</span>
                            <span class="tag">Vue</span>
                            <span class="tag">组件化</span>
                            <span class="tag">性能优化</span>
                            <span class="tag">前端架构</span>
                            <span class="tag">状态管理</span>
                        </div>
                    </div>

                    <!-- 分享按钮 -->
                    <div class="article-share">
                        <h4>分享文章</h4>
                        <div class="share-buttons">
                            <button class="share-btn twitter" data-platform="twitter">
                                <i class="fab fa-twitter"></i>
                                <span>Twitter</span>
                            </button>
                            <button class="share-btn linkedin" data-platform="linkedin">
                                <i class="fab fa-linkedin"></i>
                                <span>LinkedIn</span>
                            </button>
                            <button class="share-btn wechat" data-platform="wechat">
                                <i class="fab fa-weixin"></i>
                                <span>微信</span>
                            </button>
                            <button class="share-btn copy" data-platform="copy">
                                <i class="fas fa-link"></i>
                                <span>复制链接</span>
                            </button>
                        </div>
                    </div>
                </article>

                <!-- 侧边栏 -->
                <aside class="article-sidebar">
                    <!-- 目录 -->
                    <div class="sidebar-section toc-section">
                        <h3>文章目录</h3>
                        <nav class="table-of-contents">
                            <ul>
                                <li><a href="#组件化设计原则">组件化设计原则</a>
                                    <ul>
                                        <li><a href="#组件设计的关键原则">组件设计的关键原则</a></li>
                                    </ul>
                                </li>
                                <li><a href="#状态管理策略">状态管理策略</a></li>
                                <li><a href="#性能优化技巧">性能优化技巧</a></li>
                                <li><a href="#总结">总结</a></li>
                            </ul>
                        </nav>
                    </div>

                    <!-- 作者信息 -->
                    <div class="sidebar-section author-section">
                        <h3>关于作者</h3>
                        <div class="author-card">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face" alt="作者头像" class="author-avatar-large">
                            <h4>张开发</h4>
                            <p class="author-bio">资深前端工程师，专注于现代Web技术和用户体验设计。拥有8年前端开发经验，热爱分享技术知识。</p>
                            <div class="author-social">
                                <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                            </div>
                        </div>
                    </div>

                    <!-- 相关文章 -->
                    <div class="sidebar-section related-section">
                        <h3>相关文章</h3>
                        <div class="related-articles">
                            <article class="related-article">
                                <img src="https://images.unsplash.com/photo-1518432031352-d6fc5c10da5a?w=100&h=60&fit=crop" alt="文章缩略图" class="related-thumbnail">
                                <div class="related-content">
                                    <h5><a href="#">Vue 3 Composition API 深度解析</a></h5>
                                    <span class="related-date">2024年1月10日</span>
                                </div>
                            </article>
                            <article class="related-article">
                                <img src="https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=100&h=60&fit=crop" alt="文章缩略图" class="related-thumbnail">
                                <div class="related-content">
                                    <h5><a href="#">TypeScript 最佳实践指南</a></h5>
                                    <span class="related-date">2024年1月5日</span>
                                </div>
                            </article>
                            <article class="related-article">
                                <img src="https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=100&h=60&fit=crop" alt="文章缩略图" class="related-thumbnail">
                                <div class="related-content">
                                    <h5><a href="#">Webpack 5 配置完全指南</a></h5>
                                    <span class="related-date">2023年12月28日</span>
                                </div>
                            </article>
                        </div>
                    </div>
                </aside>
            </div>
        </div>
    </main>

    <!-- 评论区 -->
    <section class="comments-section">
        <div class="container">
            <h3>评论 <span class="comment-count">(12)</span></h3>
            
            <!-- 评论表单 -->
            <div class="comment-form-wrapper">
                <form class="comment-form">
                    <div class="form-group">
                        <textarea placeholder="写下你的想法..." rows="4" required></textarea>
                    </div>
                    <div class="form-actions">
                        <div class="form-options">
                            <label class="checkbox-label">
                                <input type="checkbox" checked>
                                <span>接收回复通知</span>
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                            发表评论
                        </button>
                    </div>
                </form>
            </div>

            <!-- 评论列表 -->
            <div class="comments-list">
                <div class="comment">
                    <div class="comment-avatar">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face" alt="用户头像">
                    </div>
                    <div class="comment-content">
                        <div class="comment-header">
                            <span class="comment-author">李前端</span>
                            <span class="comment-date">2小时前</span>
                        </div>
                        <p class="comment-text">非常详细的文章！组件化设计的部分特别有用，正好解决了我项目中遇到的问题。期待更多关于性能优化的内容。</p>
                        <div class="comment-actions">
                            <button class="comment-action like">
                                <i class="fas fa-thumbs-up"></i>
                                <span>8</span>
                            </button>
                            <button class="comment-action reply">
                                <i class="fas fa-reply"></i>
                                <span>回复</span>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="comment">
                    <div class="comment-avatar">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face" alt="用户头像">
                    </div>
                    <div class="comment-content">
                        <div class="comment-header">
                            <span class="comment-author">王设计师</span>
                            <span class="comment-date">5小时前</span>
                        </div>
                        <p class="comment-text">作为设计师，我觉得这篇文章帮助我更好地理解了前端开发的复杂性。组件化的思维对设计系统也很有启发。</p>
                        <div class="comment-actions">
                            <button class="comment-action like">
                                <i class="fas fa-thumbs-up"></i>
                                <span>5</span>
                            </button>
                            <button class="comment-action reply">
                                <i class="fas fa-reply"></i>
                                <span>回复</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="load-more-comments">
                <button class="btn btn-outline">加载更多评论</button>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <i class="fas fa-code"></i>
                        <span>TechBlog</span>
                    </div>
                    <p>分享前沿技术，探索无限可能</p>
                </div>
                
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="index.html">首页</a></li>
                        <li><a href="index.html#articles">文章</a></li>
                        <li><a href="index.html#about">关于</a></li>
                        <li><a href="index.html#contact">联系</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>关注我们</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-github"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-weixin"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 TechBlog. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Scripts -->
    <script src="script.js"></script>
    <script src="article.js"></script>
</body>
</html>
