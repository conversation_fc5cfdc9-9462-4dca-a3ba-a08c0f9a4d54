@tool
def generate_json(text=''):
    text = text.replace('，', ',')
    # 定义中断关键词列表
    interrupt_keywords = ['正在通话中', '留言', '超时', '机主', '无人接听',"将录音",
            '转达', '来电秘书', '爱莫能助', '助理', '感谢', '呼叫','喂，你好，呃，你有什么事？',
'不方便接电话','总共需要还多少钱？','逾期的话利息是多少来着','请问您还有什么要补充的','所需还款金额数是多少','得还多少钱呀？','请问逾期利息是多少呢？','其他需要补充','的智能客服']
    # 非本人关键词列表 - 使用更精确的匹配模式
    not_self_keywords = ['不是', '不认']
    # 特殊的"错"字匹配 - 排除"没错"等肯定表述
    error_patterns = [
        r'(?<!没)错(?!的)',  # 匹配"错"但不匹配"没错"
        r'^错',              # 匹配开头的"错"
        r'错了',             # 匹配"错了"
        r'搞错',             # 匹配"搞错"
        r'弄错',             # 匹配"弄错"
    ]
    # 检查文本是否包含任何中断关键词
    for keyword in interrupt_keywords:
        if re.search(keyword, text):
            return 'SY'
    # 检查普通的非本人关键词
    for keyword in not_self_keywords:
        if re.search(keyword, text):
            return 'SA02'
    # 检查特殊的"错"字模式
    for pattern in error_patterns:
        if re.search(pattern, text):
            return 'SA02'
    return "SA01"