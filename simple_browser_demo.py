#!/usr/bin/env python3
"""
简单的浏览器操作演示
使用 Playwright MCP 进行实际的浏览器操作
"""

import subprocess
import time
import json
import requests
from pathlib import Path

def test_mcp_server_direct():
    """直接测试 MCP 服务器功能"""
    print("🎭 直接测试 Playwright MCP 服务器")
    print("="*50)
    
    # 创建输出目录
    output_dir = Path("browser_demo_output")
    output_dir.mkdir(exist_ok=True)
    
    try:
        # 启动 MCP 服务器
        print("🚀 启动 Playwright MCP 服务器...")
        
        # 使用无头模式启动服务器
        cmd = [
            "npx", "@playwright/mcp@latest",
            "--headless",
            "--output-dir", str(output_dir),
            "--port", "8932"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        # 启动服务器进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        time.sleep(10)
        
        # 检查进程状态
        if process.poll() is None:
            print("✅ MCP 服务器启动成功")
            print("🌐 服务器运行在端口 8932")
            
            # 让服务器运行一段时间
            print("⏱️ 让服务器运行 15 秒...")
            time.sleep(15)
            
            # 检查输出目录
            print(f"📁 检查输出目录: {output_dir}")
            if output_dir.exists():
                files = list(output_dir.glob("*"))
                if files:
                    print("📄 生成的文件:")
                    for file in files:
                        print(f"   - {file.name}")
                else:
                    print("   (暂无文件生成)")
            
            # 终止服务器
            print("🛑 终止服务器...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
            
            print("✅ 服务器已停止")
            return True
            
        else:
            # 获取错误信息
            stdout, stderr = process.communicate()
            print("❌ 服务器启动失败")
            print(f"stdout: {stdout}")
            print(f"stderr: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def test_playwright_installation():
    """测试 Playwright 浏览器安装"""
    print("\n🌐 测试 Playwright 浏览器安装")
    print("="*50)
    
    try:
        # 检查 Playwright 是否已安装
        print("🔍 检查 Playwright 安装状态...")
        
        result = subprocess.run(
            ["npx", "playwright", "--version"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Playwright 已安装: {version}")
            
            # 检查浏览器安装状态
            print("🔍 检查浏览器安装状态...")
            
            browser_check = subprocess.run(
                ["npx", "playwright", "install", "--dry-run"],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if "already installed" in browser_check.stdout.lower() or browser_check.returncode == 0:
                print("✅ 浏览器已安装或可用")
                return True
            else:
                print("⚠️ 浏览器可能需要安装")
                print("💡 运行以下命令安装浏览器:")
                print("   npx playwright install chromium")
                return False
                
        else:
            print("❌ Playwright 未安装")
            print("💡 运行以下命令安装 Playwright:")
            print("   npm install -g playwright")
            return False
            
    except Exception as e:
        print(f"❌ 检查安装时出错: {e}")
        return False

def create_usage_guide():
    """创建使用指南"""
    print("\n📖 创建使用指南")
    print("="*50)
    
    guide = {
        "title": "Playwright MCP 使用指南",
        "description": "如何在 Cursor 中使用 Playwright MCP 进行浏览器自动化",
        "prerequisites": [
            "已安装 Node.js",
            "已安装 @playwright/mcp",
            "已配置 ~/.cursor/mcp.json",
            "已重启 Cursor"
        ],
        "basic_usage": {
            "navigation": {
                "description": "导航到网页",
                "example": "请帮我打开 https://www.baidu.com"
            },
            "screenshot": {
                "description": "截取页面截图",
                "example": "请帮我截取当前页面的截图"
            },
            "interaction": {
                "description": "与页面元素交互",
                "example": "请帮我点击搜索框并输入'Playwright'"
            }
        },
        "advanced_features": {
            "pdf_export": {
                "description": "导出页面为PDF",
                "example": "请帮我将当前页面保存为PDF"
            },
            "network_monitoring": {
                "description": "监控网络请求",
                "example": "请帮我查看页面的网络请求"
            },
            "multi_tab": {
                "description": "多标签页操作",
                "example": "请帮我打开新标签页并导航到GitHub"
            }
        },
        "tips": [
            "使用自然语言描述您想要的操作",
            "Playwright MCP 支持多种浏览器",
            "可以使用无头模式进行后台操作",
            "支持移动设备模拟",
            "可以生成 Playwright 测试代码"
        ]
    }
    
    # 保存使用指南
    guide_file = Path("playwright_mcp_usage_guide.json")
    with open(guide_file, 'w', encoding='utf-8') as f:
        json.dump(guide, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 使用指南已保存到: {guide_file}")
    
    # 创建简化的文本版本
    text_guide = f"""
# Playwright MCP 使用指南

## 基本操作示例

1. **网页导航**
   - "请帮我打开 https://www.baidu.com"
   - "请导航到 GitHub 首页"

2. **页面截图**
   - "请帮我截取当前页面的截图"
   - "请截取搜索框的截图"

3. **页面交互**
   - "请帮我点击搜索框"
   - "请在搜索框中输入 'Playwright MCP'"
   - "请点击搜索按钮"

4. **高级功能**
   - "请将当前页面保存为PDF"
   - "请查看页面的网络请求"
   - "请打开新标签页"

## 配置信息

当前 MCP 配置:
```json
{{
  "mcpServers": {{
    "playwright": {{
      "command": "npx",
      "args": ["@playwright/mcp@latest"]
    }}
  }}
}}
```

## 注意事项

- 确保已重启 Cursor 以加载 MCP 配置
- 使用自然语言描述操作
- 支持多种浏览器和设备模拟
- 可以生成自动化测试代码
"""
    
    text_file = Path("playwright_mcp_quick_guide.txt")
    with open(text_file, 'w', encoding='utf-8') as f:
        f.write(text_guide)
    
    print(f"✅ 快速指南已保存到: {text_file}")
    return True

def main():
    """主函数"""
    print("🎭 Playwright MCP 简单演示")
    print("="*60)
    
    # 运行测试
    tests = [
        ("Playwright 安装检查", test_playwright_installation),
        ("MCP 服务器测试", test_mcp_server_direct),
        ("创建使用指南", create_usage_guide),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    # 显示结果
    print("\n" + "="*60)
    print("📊 测试结果总结")
    print("="*60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n📈 总体结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！Playwright MCP 可以使用了！")
        print("\n💡 下一步:")
        print("   1. 重启 Cursor")
        print("   2. 在 Cursor 中尝试说: '请帮我打开百度首页'")
        print("   3. 或者说: '请帮我截取页面截图'")
        print("   4. 参考生成的使用指南文件")
    else:
        print("\n⚠️ 部分测试失败，请检查上述错误信息")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    exit(main())
