#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys

def read_excel_file():
    """读取Excel文件并显示结构"""
    try:
        # 尝试不同的引擎读取Excel文件
        try:
            df = pd.read_excel('催收 2.0-YXL 男声-intentions.xls', engine='xlrd')
        except:
            try:
                df = pd.read_excel('催收 2.0-YXL 男声-intentions.xls', engine='openpyxl')
            except:
                # 如果还是不行，尝试重命名为xlsx
                import shutil
                shutil.copy('催收 2.0-YXL 男声-intentions.xls', '催收 2.0-YXL 男声-intentions.xlsx')
                df = pd.read_excel('催收 2.0-YXL 男声-intentions.xlsx', engine='openpyxl')
        print('Excel文件读取成功')
        print('列名:', df.columns.tolist())
        print('数据形状:', df.shape)
        print('\n前10行数据:')
        print(df.head(10))
        
        # 查找现有的开场白数据
        print('\n查找包含"告知账单"的行:')
        mask = df.astype(str).apply(lambda x: x.str.contains('告知账单', na=False)).any(axis=1)
        existing_data = df[mask]
        print(existing_data)
        
        return df
        
    except Exception as e:
        print('读取Excel文件时出错:', str(e))
        return None

def create_new_excel_with_openings(df):
    """创建包含新开场白的Excel文件"""
    if df is None:
        return None

    # 获取现有数据的最大任务ID
    max_task_id = df['任务ID'].max()

    # 首次逾期的新开场白
    first_overdue_scripts = [
        "你的账户已经严重逾期，欠款${overdueAmount}元,风险明显升高，持续拖欠，公司后续可能致电你的紧急联系人核实情况和上报征信，看你是第一次拖欠，也不多，请你今天务必两小时内还款，在内部把这个事情解决掉，好吧",
        "${gender_sisbro}打扰您几分钟，系统提示您第一次欠款，金额${overdueAmount}元，是不是遇到啥突发情况了？我们走个快速协商通道，你两小时内还掉，我帮你撤案，维护下平台信用，好吧",
        "你在${loanInstitutions}已经逾期拖欠，欠款金额${overdueAmount}元，账户风险已经升高，利息也在不断增加。我看欠款也不多，你两小时内赶紧处理下，避免催收流程升级，好吧",
        "${name}你的账户已经逾期欠款${overdueAmount}元了，系统可能要启动相关征信上报流程了，看你也是第一次逾期，没必要因为这点钱影响征信，你两小时内赶紧处理下，我这边去把上报流程撤掉，好吧",
        "你在${loanInstitutions}的账单已经逾期，欠款金额${overdueAmount}元，看你首次拖欠，请务必2小时内还掉，持续拖欠，我们只能升级催收流程，可能致电你的紧急联系人核实状况，到时候所有影响都要由你本人承担，好吧",
        "这边是${loanInstitutions}负责处理你${overdueAmount}元逾期欠款的，看您是头一回拖欠。请您务必2小时内还掉，我帮你实时恢复额度,维护平台信用，以便后期合作，没问题吧？",
        "这边是${loanInstitutions}负责处理你${overdueAmount}元逾期欠款的，看您是头一回拖欠。是不是手头有点紧，请你务必2小时内把欠款还掉，我这边帮你实时恢复额度你还是公司的优质客户，后续保持合作。没有问题吧？",
        "你的账户已经严重逾期，欠款${overdueAmount}元,风险明显升高，持续拖欠公司后续可能致电你的紧急联系人核实情况和上报征信，看你是第一次拖欠，也不多，请你今天务必两小时内还款，在内部把这个事情解决掉，好吧"
    ]

    # 偶尔逾期的新开场白
    occasional_overdue_scripts = [
        "您在${loanInstitutions}已逾期${overdueAmount}元，持续拖欠不仅产生利息，还会影响您的信用。为避免情况恶化，请您务必在2小时内处理下，好吧",
        "${name}，目前你已经逾期欠款金额${overdueAmount}元，看你一直是平台的优质客户，这次逾期是临时周转问题吗。你这边抓紧两小时内处理掉欠款吗，我可给你维护平台评分，保持后续的合作关系，好吧",
        "您的在${loanInstitutions}已逾期${overdueAmount}元，持续拖欠不仅产生利息，还会影响您的信用。为避免情况恶化，请您务必在2小时内前处理掉欠款，好吧",
        "你在${loanInstitutions}的账单已经逾期${overdueDates}天了，欠款金额${overdueAmount}元，持续拖欠，我们将升级催收流程，并可能致电给你的紧急联系人了解你的情况。为了避免影响升级，请你两小时务必把欠款处理掉，好吧",
        "${gender_sisbro}，您也是老客户，还款一直很准时。您也知道逾期会影响在平台的信用记录，对额度的使用是很不利的，请务必两小内处理下，我们会帮您维护平台信用记录，后续对您的额度需求可能会有极大帮助，好吧"
    ]

    # 惯性逾期的新开场白
    habitual_overdue_scripts = [
        "${name}，你已经逾期，欠款金额${overdueAmount}元，系统提示你历史有过多次逾期，这次是有什么特殊情况吗，正常流程和影响你应该也清楚了，请你务必想办法2小时内还掉，我们后续也继续保持合作，好吧",
        "${name}，你已经逾期${overdueDates}天了，欠款金额${overdueAmount}元，系统提示你历史有过多次逾期，这次是有什么情况吗，正常流程和影响你应该也清楚了，请你务必想办法2小时内还掉，好吧",
        "${gender_sisbro}我看你以前多次逾期，后来都还了，也保持合作关系，这次欠款${overdueAmount}元，也不多，你去周转下，两小时内处理掉，我给你争取恢复额度，避免影响后续合作好吧",
        "你的${loanInstitutions}现在逾期欠款${overdueAmount}元，系统提示你多次逾期，整个案件性质和风险已经发生变化，要求今天直接升级催收流程处理，你的欠款也不多，为了避免影响，你想想办法两小时内赶紧还掉好吧",
        "你的账户已经逾期，欠款${overdueAmount}元，鉴于你的风险表现公司可能随时会升级处理。后续可能联系你的紧急联系人核实情况，请你今天必须拿出诚意来，2小时内把这个欠款还掉，把事情在内部解决掉，好吧"
    ]

    # 创建新的数据行
    new_rows = []

    # 添加首次逾期的新开场白
    for i, script in enumerate(first_overdue_scripts, 1):
        new_row = {
            '任务ID': 181,
            '意图Code': f'A01_First_Overdue_{i:02d}',
            '版本号': '1.0',
            '备注': None,
            '是否启用 (Y: 是, N: 否)': 'N',
            '意图名称': '告知账单_首次逾期',
            '意图话术': script,
            '是否包含参数': 'Y',
            '打断配置': 0,
            '归属标签': None,
            '是否结束': 'N',
            '拨打策略id': 'one',
            'voiceId': None
        }
        new_rows.append(new_row)

    # 添加偶尔逾期的新开场白
    for i, script in enumerate(occasional_overdue_scripts, 1):
        new_row = {
            '任务ID': 181,
            '意图Code': f'A01_Occasional_Overdue_{i:02d}',
            '版本号': '1.0',
            '备注': None,
            '是否启用 (Y: 是, N: 否)': 'N',
            '意图名称': '告知账单_偶尔逾期',
            '意图话术': script,
            '是否包含参数': 'Y',
            '打断配置': 0,
            '归属标签': None,
            '是否结束': 'N',
            '拨打策略id': 'one',
            'voiceId': None
        }
        new_rows.append(new_row)

    # 添加惯性逾期的新开场白
    for i, script in enumerate(habitual_overdue_scripts, 1):
        new_row = {
            '任务ID': 181,
            '意图Code': f'A01_Inertial_Overdue_{i:02d}',
            '版本号': '1.0',
            '备注': None,
            '是否启用 (Y: 是, N: 否)': 'N',
            '意图名称': '告知账单_惯性逾期',
            '意图话术': script,
            '是否包含参数': 'Y',
            '打断配置': 0,
            '归属标签': None,
            '是否结束': 'N',
            '拨打策略id': 'one',
            'voiceId': None
        }
        new_rows.append(new_row)

    # 将新行添加到原始DataFrame
    new_df = pd.DataFrame(new_rows)
    combined_df = pd.concat([df, new_df], ignore_index=True)

    return combined_df

if __name__ == "__main__":
    df = read_excel_file()
    if df is not None:
        print("\n开始创建包含新开场白的Excel文件...")
        new_df = create_new_excel_with_openings(df)

        if new_df is not None:
            # 保存新的Excel文件
            output_filename = '催收 2.0-YXL 男声-intentions-新增开场白.xlsx'
            new_df.to_excel(output_filename, index=False, engine='openpyxl')

            print(f"✅ 新Excel文件已保存: {output_filename}")
            print(f"原始数据行数: {len(df)}")
            print(f"新增数据行数: {len(new_df) - len(df)}")
            print(f"总数据行数: {len(new_df)}")

            # 显示新增的开场白数据
            print("\n新增的开场白数据预览:")
            new_data = new_df.tail(18)  # 显示最后18行（新增的数据）
            for idx, row in new_data.iterrows():
                print(f"意图Code: {row['意图Code']}")
                print(f"意图名称: {row['意图名称']}")
                print(f"意图话术: {row['意图话术'][:50]}...")
                print("-" * 50)
        else:
            print("❌ 创建新Excel文件失败")
