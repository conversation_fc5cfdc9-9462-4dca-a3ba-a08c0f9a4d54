#!/bin/bash

echo "🔍 Gemini CLI 诊断工具"
echo "===================="
echo ""

# 检查基本配置
echo "📋 基本配置检查"
echo "==============="
echo "Gemini CLI版本: $(gemini --version)"
echo "Node.js版本: $(node --version)"
echo "操作系统: $(uname -s)"
echo ""

# 检查配置文件
echo "⚙️ 配置文件检查"
echo "==============="
if [ -f ~/.gemini/settings.json ]; then
    echo "✅ 配置文件存在: ~/.gemini/settings.json"
    echo "配置内容:"
    cat ~/.gemini/settings.json | jq '.' 2>/dev/null || cat ~/.gemini/settings.json
else
    echo "❌ 配置文件不存在"
fi
echo ""

# 检查环境变量
echo "🌍 环境变量检查"
echo "==============="
if [ -n "$GEMINI_API_KEY" ]; then
    echo "✅ GEMINI_API_KEY 已设置: ${GEMINI_API_KEY:0:10}..."
else
    echo "⚠️ GEMINI_API_KEY 环境变量未设置"
fi
echo ""

# 网络连接测试
echo "🌐 网络连接测试"
echo "==============="

# 测试基本网络连接
echo "测试Google DNS..."
if ping -c 1 8.8.8.8 >/dev/null 2>&1; then
    echo "✅ 基本网络连接正常"
else
    echo "❌ 基本网络连接失败"
fi

# 测试HTTPS连接
echo "测试HTTPS连接到Google..."
if curl -s --connect-timeout 5 https://www.google.com >/dev/null; then
    echo "✅ HTTPS连接正常"
else
    echo "❌ HTTPS连接失败"
fi

# 测试Gemini API端点
echo "测试Gemini API端点..."
API_RESPONSE=$(curl -s --connect-timeout 10 -w "%{http_code}" -o /dev/null "https://generativelanguage.googleapis.com/v1beta/models?key=AIzaSyCBCqPRVvKaTLE9wXkg0dUtlgr2gHk5ULk")
if [ "$API_RESPONSE" = "200" ]; then
    echo "✅ Gemini API连接正常"
elif [ "$API_RESPONSE" = "403" ]; then
    echo "⚠️ API密钥可能有问题 (HTTP 403)"
elif [ -z "$API_RESPONSE" ]; then
    echo "❌ 无法连接到Gemini API (网络问题)"
else
    echo "⚠️ Gemini API返回状态码: $API_RESPONSE"
fi
echo ""

# 代理检查
echo "🔄 代理设置检查"
echo "==============="
if [ -n "$HTTP_PROXY" ] || [ -n "$HTTPS_PROXY" ] || [ -n "$http_proxy" ] || [ -n "$https_proxy" ]; then
    echo "⚠️ 检测到代理设置:"
    [ -n "$HTTP_PROXY" ] && echo "  HTTP_PROXY: $HTTP_PROXY"
    [ -n "$HTTPS_PROXY" ] && echo "  HTTPS_PROXY: $HTTPS_PROXY"
    [ -n "$http_proxy" ] && echo "  http_proxy: $http_proxy"
    [ -n "$https_proxy" ] && echo "  https_proxy: $https_proxy"
else
    echo "✅ 未检测到代理设置"
fi
echo ""

# DNS检查
echo "🔍 DNS解析检查"
echo "==============="
if nslookup generativelanguage.googleapis.com >/dev/null 2>&1; then
    echo "✅ DNS解析正常"
    echo "解析结果:"
    nslookup generativelanguage.googleapis.com | grep "Address:" | head -2
else
    echo "❌ DNS解析失败"
fi
echo ""

# 防火墙检查建议
echo "🛡️ 防火墙检查建议"
echo "=================="
echo "如果上述测试失败，请检查:"
echo "1. 防火墙设置是否阻止了HTTPS连接"
echo "2. 公司网络是否有代理或限制"
echo "3. DNS设置是否正确"
echo "4. API密钥是否有效且有权限"
echo ""

# 解决方案建议
echo "💡 解决方案建议"
echo "==============="
echo "如果遇到网络问题，可以尝试:"
echo "1. 使用不同的网络环境"
echo "2. 配置代理设置"
echo "3. 检查防火墙规则"
echo "4. 联系网络管理员"
echo ""

# 简单测试
echo "🧪 简单功能测试"
echo "==============="
echo "尝试简单的Gemini CLI调用..."
echo "Hello" | timeout 10 gemini 2>&1 || echo "测试超时或失败"
