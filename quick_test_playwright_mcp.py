#!/usr/bin/env python3
"""
Playwright MCP 快速测试脚本
用于快速验证 Playwright MCP 的基本可用性
"""

import subprocess
import json
import sys
from pathlib import Path

def test_mcp_installation():
    """测试 MCP 安装"""
    print("🔍 测试 Playwright MCP 安装...")
    try:
        result = subprocess.run(
            ["npx", "@playwright/mcp@latest", "--version"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Playwright MCP 已安装，版本: {version}")
            return True
        else:
            print(f"❌ Playwright MCP 安装检查失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 检查安装时出错: {e}")
        return False

def test_mcp_config():
    """测试 MCP 配置"""
    print("\n⚙️ 检查 MCP 配置...")
    try:
        config_path = Path.home() / ".cursor" / "mcp.json"
        if not config_path.exists():
            print("❌ MCP 配置文件不存在")
            return False
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if "mcpServers" in config and "playwright" in config["mcpServers"]:
            print("✅ Playwright MCP 配置已添加到 Cursor")
            print(f"   配置: {config['mcpServers']['playwright']}")
            return True
        else:
            print("❌ 配置文件中未找到 Playwright MCP 配置")
            return False
    except Exception as e:
        print(f"❌ 检查配置时出错: {e}")
        return False

def test_help_command():
    """测试帮助命令"""
    print("\n📋 测试帮助命令...")
    try:
        result = subprocess.run(
            ["npx", "@playwright/mcp@latest", "--help"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0 and "Usage:" in result.stdout:
            print("✅ 帮助命令正常工作")
            # 显示一些关键功能
            if "browser" in result.stdout:
                print("   ✓ 支持浏览器选择")
            if "headless" in result.stdout:
                print("   ✓ 支持无头模式")
            if "port" in result.stdout:
                print("   ✓ 支持端口配置")
            return True
        else:
            print(f"❌ 帮助命令失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 测试帮助命令时出错: {e}")
        return False

def main():
    """主函数"""
    print("🎭 Playwright MCP 快速验证测试")
    print("="*50)
    
    tests = [
        ("安装检查", test_mcp_installation),
        ("配置检查", test_mcp_config),
        ("功能检查", test_help_command),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed += 1
    
    print("\n" + "="*50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 Playwright MCP 配置成功！")
        print("\n💡 下一步:")
        print("   1. 重启 Cursor 以加载新的 MCP 配置")
        print("   2. 在 Cursor 中尝试使用 Playwright 相关功能")
        print("   3. 可以使用浏览器自动化、截图、网页交互等功能")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查上述错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
