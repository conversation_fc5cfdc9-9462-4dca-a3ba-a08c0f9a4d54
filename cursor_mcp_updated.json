{"mcpServers": {"browser-tools": {"command": "npx", "args": ["@agentdeskai/browser-tools-mcp@1.0.11"], "env": {}}, "neon": {"command": "npx", "args": ["-y", "@neondatabase/mcp-server-neon", "start", "napi_j5zlhdtx38dhi0lsrqz7o4e4iuujzh3z2k7qo2pvj2z7n4se6u29xk76oycmi1ot"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {"DEFAULT_MINIMUM_TOKENS": "10000"}}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "chrome-mcp-server": {"command": "node", "args": ["/usr/local/lib/node_modules/mcp-chrome-bridge/dist/mcp/mcp-server-stdio.js"], "env": {}}}}