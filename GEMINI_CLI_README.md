# 🤖 Gemini CLI 部署完成指南

## ✅ 安装状态
- **Gemini CLI版本**: 0.1.9 ✅
- **Node.js版本**: v20.19.2 ✅  
- **Python版本**: 3.13.0 ✅
- **配置目录**: ~/.gemini ✅

## 🔑 API密钥配置

### 步骤1：获取API密钥
1. 访问 [Google AI Studio](https://aistudio.google.com/app/apikey)
2. 登录您的Google账户
3. 点击 "Create API Key"
4. 复制生成的API密钥

### 步骤2：设置API密钥

#### 方法A：环境变量（推荐）
```bash
# 临时设置（当前会话）
export GEMINI_API_KEY='your-api-key-here'

# 永久设置（添加到shell配置文件）
echo 'export GEMINI_API_KEY="your-api-key-here"' >> ~/.zshrc
source ~/.zshrc
```

#### 方法B：配置文件
```bash
# 复制模板到配置目录
cp gemini_settings_template.json ~/.gemini/settings.json

# 编辑配置文件，替换YOUR_API_KEY_HERE
nano ~/.gemini/settings.json
```

## 🚀 快速开始

### 基本用法
```bash
# 简单对话
echo "Hello, how are you?" | gemini

# 直接提问
gemini -p "Explain quantum computing"

# 分析文件
gemini -p "Review this code" your_file.py

# 使用不同模型
gemini -m "gemini-2.5-flash" -p "Quick question"
```

### 高级功能
```bash
# 沙盒模式（安全执行代码）
gemini -s -p "Write and run a Python script"

# 包含所有文件作为上下文
gemini -a -p "Analyze my entire project"

# 调试模式
gemini -d -p "Debug this issue"

# YOLO模式（自动接受所有操作）
gemini -y -p "Fix all issues in my code"
```

## 📁 项目文件

- `setup_gemini_api.sh` - API密钥配置脚本
- `gemini_examples.sh` - 使用示例演示
- `gemini_settings_template.json` - 配置文件模板
- `GEMINI_CLI_README.md` - 本指南

## 🧪 测试安装

运行配置脚本检查状态：
```bash
./setup_gemini_api.sh
```

运行示例演示（需要先设置API密钥）：
```bash
./gemini_examples.sh
```

## 🔧 常用命令

```bash
# 查看版本
gemini --version

# 查看帮助
gemini --help

# 查看可用模型
gemini -p "List available models"

# 检查配置
cat ~/.gemini/settings.json
```

## 🎯 使用场景

1. **代码审查**: `gemini -p "Review this code for bugs" file.py`
2. **文档生成**: `gemini -p "Generate documentation for" *.py`
3. **问题解答**: `echo "How to optimize this function?" | gemini`
4. **代码重构**: `gemini -p "Refactor this code to be more efficient" old_code.py`
5. **学习助手**: `gemini -p "Explain this concept in simple terms"`

## 🛠️ 故障排除

### 常见问题

1. **API密钥错误**
   ```bash
   # 检查环境变量
   echo $GEMINI_API_KEY
   
   # 重新设置
   export GEMINI_API_KEY='your-correct-api-key'
   ```

2. **网络连接问题**
   ```bash
   # 测试网络连接
   curl -s https://generativelanguage.googleapis.com/
   ```

3. **权限问题**
   ```bash
   # 检查配置文件权限
   ls -la ~/.gemini/
   
   # 修复权限
   chmod 600 ~/.gemini/settings.json
   ```

## 📚 更多资源

- [官方GitHub仓库](https://github.com/google-gemini/gemini-cli)
- [Google AI Studio](https://aistudio.google.com/)
- [Gemini API文档](https://ai.google.dev/docs)

---

**🎉 恭喜！Gemini CLI已成功部署，请设置API密钥后开始使用。**
