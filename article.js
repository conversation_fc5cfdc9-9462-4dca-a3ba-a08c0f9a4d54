// ===== 文章页面专用JavaScript =====

document.addEventListener('DOMContentLoaded', function() {
    initializeArticlePage();
});

function initializeArticlePage() {
    setupTableOfContents();
    setupCodeCopyButtons();
    setupShareButtons();
    setupCommentForm();
    setupBackToTop();
    setupReadingProgress();
    setupImageModal();
    setupCommentActions();
}

// ===== 目录功能 =====
function setupTableOfContents() {
    const tocLinks = document.querySelectorAll('.table-of-contents a');
    const headings = document.querySelectorAll('.article-body h2, .article-body h3');
    
    // 为标题添加ID
    headings.forEach((heading, index) => {
        if (!heading.id) {
            heading.id = heading.textContent.replace(/\s+/g, '-').toLowerCase();
        }
    });
    
    // 目录点击事件
    tocLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 100;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
                
                // 更新活动状态
                updateActiveTocLink(link);
            }
        });
    });
    
    // 滚动时更新目录活动状态
    window.addEventListener('scroll', throttle(() => {
        updateTocOnScroll();
    }, 100));
}

function updateActiveTocLink(activeLink) {
    document.querySelectorAll('.table-of-contents a').forEach(link => {
        link.classList.remove('active');
    });
    activeLink.classList.add('active');
}

function updateTocOnScroll() {
    const headings = document.querySelectorAll('.article-body h2, .article-body h3');
    const scrollPosition = window.pageYOffset + 150;
    
    let currentHeading = null;
    
    headings.forEach(heading => {
        if (heading.offsetTop <= scrollPosition) {
            currentHeading = heading;
        }
    });
    
    if (currentHeading) {
        const activeLink = document.querySelector(`.table-of-contents a[href="#${currentHeading.id}"]`);
        if (activeLink) {
            updateActiveTocLink(activeLink);
        }
    }
}

// ===== 代码复制功能 =====
function setupCodeCopyButtons() {
    const copyButtons = document.querySelectorAll('.copy-btn');
    
    copyButtons.forEach(button => {
        button.addEventListener('click', async () => {
            const codeId = button.getAttribute('data-code');
            const codeElement = document.getElementById(codeId);
            
            if (codeElement) {
                try {
                    await navigator.clipboard.writeText(codeElement.textContent);
                    
                    // 显示复制成功反馈
                    const originalIcon = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-check"></i>';
                    button.style.color = '#10b981';
                    
                    setTimeout(() => {
                        button.innerHTML = originalIcon;
                        button.style.color = '';
                    }, 2000);
                    
                    showNotification('代码已复制到剪贴板', 'success');
                } catch (err) {
                    showNotification('复制失败，请手动复制', 'error');
                }
            }
        });
    });
}

// ===== 分享功能 =====
function setupShareButtons() {
    const shareButtons = document.querySelectorAll('.share-btn');
    
    shareButtons.forEach(button => {
        button.addEventListener('click', () => {
            const platform = button.getAttribute('data-platform');
            const url = window.location.href;
            const title = document.querySelector('.article-title').textContent;
            const description = document.querySelector('.article-subtitle').textContent;
            
            switch (platform) {
                case 'twitter':
                    shareToTwitter(url, title);
                    break;
                case 'linkedin':
                    shareToLinkedIn(url, title, description);
                    break;
                case 'wechat':
                    showWeChatQR(url);
                    break;
                case 'copy':
                    copyToClipboard(url);
                    break;
            }
        });
    });
}

function shareToTwitter(url, title) {
    const twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
    window.open(twitterUrl, '_blank', 'width=600,height=400');
}

function shareToLinkedIn(url, title, description) {
    const linkedinUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
    window.open(linkedinUrl, '_blank', 'width=600,height=400');
}

function showWeChatQR(url) {
    // 这里可以集成二维码生成库
    showNotification('微信分享功能开发中', 'info');
}

async function copyToClipboard(url) {
    try {
        await navigator.clipboard.writeText(url);
        showNotification('链接已复制到剪贴板', 'success');
    } catch (err) {
        showNotification('复制失败', 'error');
    }
}

// ===== 评论表单 =====
function setupCommentForm() {
    const commentForm = document.querySelector('.comment-form');
    
    if (commentForm) {
        commentForm.addEventListener('submit', (e) => {
            e.preventDefault();
            
            const textarea = commentForm.querySelector('textarea');
            const content = textarea.value.trim();
            
            if (!content) {
                showNotification('请输入评论内容', 'error');
                return;
            }
            
            // 模拟提交评论
            const submitBtn = commentForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发表中...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                // 添加新评论到列表
                addNewComment(content);
                
                // 重置表单
                textarea.value = '';
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                
                showNotification('评论发表成功！', 'success');
            }, 1500);
        });
    }
}

function addNewComment(content) {
    const commentsList = document.querySelector('.comments-list');
    const newComment = createCommentElement(content);
    commentsList.insertBefore(newComment, commentsList.firstChild);
    
    // 更新评论数量
    const commentCount = document.querySelector('.comment-count');
    const currentCount = parseInt(commentCount.textContent.match(/\d+/)[0]);
    commentCount.textContent = `(${currentCount + 1})`;
}

function createCommentElement(content) {
    const comment = document.createElement('div');
    comment.className = 'comment';
    comment.innerHTML = `
        <div class="comment-avatar">
            <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=50&h=50&fit=crop&crop=face" alt="用户头像">
        </div>
        <div class="comment-content">
            <div class="comment-header">
                <span class="comment-author">我</span>
                <span class="comment-date">刚刚</span>
            </div>
            <p class="comment-text">${content}</p>
            <div class="comment-actions">
                <button class="comment-action like">
                    <i class="fas fa-thumbs-up"></i>
                    <span>0</span>
                </button>
                <button class="comment-action reply">
                    <i class="fas fa-reply"></i>
                    <span>回复</span>
                </button>
            </div>
        </div>
    `;
    
    // 添加动画效果
    comment.style.opacity = '0';
    comment.style.transform = 'translateY(-20px)';
    
    setTimeout(() => {
        comment.style.transition = 'all 0.3s ease';
        comment.style.opacity = '1';
        comment.style.transform = 'translateY(0)';
    }, 100);
    
    return comment;
}

// ===== 评论操作 =====
function setupCommentActions() {
    document.addEventListener('click', (e) => {
        if (e.target.closest('.comment-action.like')) {
            handleLikeComment(e.target.closest('.comment-action.like'));
        }
        
        if (e.target.closest('.comment-action.reply')) {
            handleReplyComment(e.target.closest('.comment-action.reply'));
        }
    });
}

function handleLikeComment(likeBtn) {
    const countSpan = likeBtn.querySelector('span');
    const currentCount = parseInt(countSpan.textContent);
    
    if (likeBtn.classList.contains('active')) {
        likeBtn.classList.remove('active');
        countSpan.textContent = currentCount - 1;
    } else {
        likeBtn.classList.add('active');
        countSpan.textContent = currentCount + 1;
    }
}

function handleReplyComment(replyBtn) {
    const comment = replyBtn.closest('.comment');
    const authorName = comment.querySelector('.comment-author').textContent;
    const textarea = document.querySelector('.comment-form textarea');
    
    textarea.value = `@${authorName} `;
    textarea.focus();
    
    // 滚动到评论表单
    const commentForm = document.querySelector('.comment-form-wrapper');
    commentForm.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// ===== 返回顶部 =====
function setupBackToTop() {
    const backToTopBtn = document.getElementById('backToTop');
    
    if (backToTopBtn) {
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        });
        
        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

// ===== 阅读进度 =====
function setupReadingProgress() {
    const progressBar = document.createElement('div');
    progressBar.className = 'reading-progress';
    progressBar.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 3px;
        background: var(--primary-color);
        z-index: 9999;
        transition: width 0.1s ease;
    `;
    document.body.appendChild(progressBar);
    
    window.addEventListener('scroll', () => {
        const article = document.querySelector('.article-body');
        if (!article) return;
        
        const articleTop = article.offsetTop;
        const articleHeight = article.offsetHeight;
        const windowHeight = window.innerHeight;
        const scrollTop = window.pageYOffset;
        
        const progress = Math.min(
            Math.max((scrollTop - articleTop + windowHeight) / articleHeight, 0),
            1
        );
        
        progressBar.style.width = `${progress * 100}%`;
    });
}

// ===== 图片模态框 =====
function setupImageModal() {
    const images = document.querySelectorAll('.article-body img');
    
    images.forEach(img => {
        img.style.cursor = 'pointer';
        img.addEventListener('click', () => {
            showImageModal(img.src, img.alt);
        });
    });
}

function showImageModal(src, alt) {
    const modal = document.createElement('div');
    modal.className = 'image-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    
    modal.innerHTML = `
        <div class="modal-content" style="max-width: 90%; max-height: 90%; position: relative;">
            <img src="${src}" alt="${alt}" style="width: 100%; height: auto; border-radius: 8px;">
            <button class="modal-close" style="
                position: absolute;
                top: -40px;
                right: 0;
                background: none;
                border: none;
                color: white;
                font-size: 24px;
                cursor: pointer;
                padding: 8px;
            ">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 显示动画
    setTimeout(() => {
        modal.style.opacity = '1';
    }, 10);
    
    // 关闭事件
    const closeBtn = modal.querySelector('.modal-close');
    const closeModal = () => {
        modal.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(modal);
        }, 300);
    };
    
    closeBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal();
        }
    });
    
    // ESC键关闭
    const handleEsc = (e) => {
        if (e.key === 'Escape') {
            closeModal();
            document.removeEventListener('keydown', handleEsc);
        }
    };
    document.addEventListener('keydown', handleEsc);
}

// ===== 通知功能 =====
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 400px;
    `;
    
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // 自动关闭
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// ===== 工具函数 =====
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}
