/* ===== 文章页面专用样式 ===== */

/* 文章头部 */
.article-header {
  padding: calc(var(--navbar-height) + var(--spacing-3xl)) 0 var(--spacing-3xl);
  background: linear-gradient(
    135deg,
    var(--bg-primary) 0%,
    var(--bg-secondary) 100%
  );
  position: relative;
  overflow: hidden;
}

.article-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(0,212,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.article-meta-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  position: relative;
  z-index: 2;
}

.back-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.9rem;
  transition: var(--transition-normal);
}

.back-link:hover {
  color: var(--primary-color);
  transform: translateX(-5px);
}

.article-title {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-lg);
  background: var(--gradient-text);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 2;
}

.article-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-xl);
  max-width: 800px;
  position: relative;
  z-index: 2;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  position: relative;
  z-index: 2;
}

.author-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.author-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid var(--primary-color);
  object-fit: cover;
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.author-title {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.article-stats {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.stat-item i {
  color: var(--primary-color);
}

/* 文章布局 */
.article-main {
  padding: var(--spacing-3xl) 0;
}

.article-layout {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: var(--spacing-3xl);
  align-items: start;
}

/* 文章内容 */
.article-content {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
}

.article-image {
  position: relative;
  overflow: hidden;
}

.featured-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
  transition: var(--transition-slow);
}

.article-body {
  padding: var(--spacing-2xl);
}

.article-intro {
  font-size: 1.1rem;
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: rgba(0, 212, 255, 0.05);
  border-left: 4px solid var(--primary-color);
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.article-body h2 {
  font-size: 2rem;
  font-weight: 600;
  margin: var(--spacing-2xl) 0 var(--spacing-lg);
  color: var(--text-primary);
  position: relative;
}

.article-body h2::before {
  content: "";
  position: absolute;
  left: -var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 60%;
  background: var(--gradient-primary);
  border-radius: 2px;
}

.article-body h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: var(--spacing-xl) 0 var(--spacing-md);
  color: var(--text-primary);
}

.article-body p {
  line-height: 1.7;
  margin-bottom: var(--spacing-lg);
  color: var(--text-secondary);
}

.article-body ul,
.article-body ol {
  margin: var(--spacing-lg) 0;
  padding-left: var(--spacing-xl);
}

.article-body li {
  line-height: 1.7;
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
}

.article-body li strong {
  color: var(--text-primary);
}

/* 代码块 */
.code-block {
  margin: var(--spacing-xl) 0;
  border-radius: var(--border-radius);
  overflow: hidden;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid var(--border-color);
}

.code-language {
  font-size: 0.8rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.copy-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-normal);
}

.copy-btn:hover {
  color: var(--primary-color);
  background: rgba(0, 212, 255, 0.1);
}

.code-block pre {
  margin: 0;
  padding: var(--spacing-lg);
  overflow-x: auto;
  font-family: var(--font-mono);
  font-size: 0.9rem;
  line-height: 1.6;
}

.code-block code {
  color: var(--text-primary);
}

/* 信息框 */
.info-box {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: var(--border-radius);
}

.info-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 193, 7, 0.2);
  border-radius: 50%;
  color: #ffc107;
}

.info-content h4 {
  margin: 0 0 var(--spacing-sm);
  color: var(--text-primary);
  font-size: 1.1rem;
}

.info-content p {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 性能网格 */
.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
}

.performance-item {
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  text-align: center;
  transition: var(--transition-normal);
}

.performance-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.performance-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gradient-primary);
  border-radius: 50%;
  color: white;
  font-size: 1.5rem;
}

.performance-item h4 {
  margin: 0 0 var(--spacing-sm);
  color: var(--text-primary);
}

.performance-item p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 文章标签 */
.article-tags {
  padding: var(--spacing-xl) var(--spacing-2xl);
  border-top: 1px solid var(--border-color);
}

.article-tags h4 {
  margin: 0 0 var(--spacing-md);
  color: var(--text-primary);
  font-size: 1.1rem;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.tag {
  padding: var(--spacing-xs) var(--spacing-md);
  background: var(--bg-primary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-full);
  font-size: 0.8rem;
  transition: var(--transition-normal);
  cursor: pointer;
}

.tag:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* 分享按钮 */
.article-share {
  padding: var(--spacing-xl) var(--spacing-2xl);
  border-top: 1px solid var(--border-color);
}

.article-share h4 {
  margin: 0 0 var(--spacing-md);
  color: var(--text-primary);
  font-size: 1.1rem;
}

.share-buttons {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.share-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition-normal);
  text-decoration: none;
}

.share-btn.twitter {
  background: #1da1f2;
  color: white;
}

.share-btn.linkedin {
  background: #0077b5;
  color: white;
}

.share-btn.wechat {
  background: #07c160;
  color: white;
}

.share-btn.copy {
  background: var(--bg-primary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.share-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* 侧边栏 */
.article-sidebar {
  position: sticky;
  top: calc(var(--navbar-height) + var(--spacing-lg));
  height: fit-content;
}

.sidebar-section {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
}

.sidebar-section h3 {
  margin: 0;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

/* 目录 */
.table-of-contents {
  padding: var(--spacing-lg);
}

.table-of-contents ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.table-of-contents li {
  margin-bottom: var(--spacing-sm);
}

.table-of-contents a {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.9rem;
  line-height: 1.5;
  transition: var(--transition-normal);
  display: block;
  padding: var(--spacing-xs) 0;
}

.table-of-contents a:hover {
  color: var(--primary-color);
  padding-left: var(--spacing-sm);
}

.table-of-contents ul ul {
  margin-left: var(--spacing-md);
  margin-top: var(--spacing-xs);
}

.table-of-contents ul ul a {
  font-size: 0.8rem;
  color: var(--text-muted);
}

/* 作者卡片 */
.author-card {
  padding: var(--spacing-lg);
  text-align: center;
}

.author-avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid var(--primary-color);
  object-fit: cover;
  margin-bottom: var(--spacing-md);
}

.author-card h4 {
  margin: 0 0 var(--spacing-sm);
  color: var(--text-primary);
  font-size: 1.2rem;
}

.author-bio {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
}

.author-social {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.social-link {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-primary);
  color: var(--text-secondary);
  border-radius: 50%;
  text-decoration: none;
  transition: var(--transition-normal);
  border: 1px solid var(--border-color);
}

.social-link:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

/* 相关文章 */
.related-articles {
  padding: var(--spacing-lg);
}

.related-article {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.related-article:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.related-thumbnail {
  width: 100px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--border-radius);
  flex-shrink: 0;
}

.related-content {
  flex: 1;
}

.related-content h5 {
  margin: 0 0 var(--spacing-xs);
  font-size: 0.9rem;
  line-height: 1.4;
}

.related-content a {
  color: var(--text-primary);
  text-decoration: none;
  transition: var(--transition-normal);
}

.related-content a:hover {
  color: var(--primary-color);
}

.related-date {
  color: var(--text-muted);
  font-size: 0.8rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .article-layout {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .article-sidebar {
    position: static;
    order: -1;
  }

  .sidebar-section {
    display: none;
  }

  .toc-section {
    display: block;
  }
}

@media (max-width: 768px) {
  .article-title {
    font-size: 2rem;
  }

  .article-subtitle {
    font-size: 1.1rem;
  }

  .article-meta {
    flex-direction: column;
    align-items: flex-start;
  }

  .article-stats {
    flex-wrap: wrap;
    gap: var(--spacing-md);
  }

  .article-body {
    padding: var(--spacing-lg);
  }

  .performance-grid {
    grid-template-columns: 1fr;
  }

  .share-buttons {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .article-header {
    padding: calc(var(--navbar-height) + var(--spacing-xl)) 0 var(--spacing-xl);
  }

  .article-title {
    font-size: 1.8rem;
  }

  .article-body {
    padding: var(--spacing-md);
  }

  .article-tags,
  .article-share {
    padding: var(--spacing-lg) var(--spacing-md);
  }
}

/* ===== 评论区样式 ===== */
.comments-section {
  padding: var(--spacing-3xl) 0;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
}

.comments-section h3 {
  margin-bottom: var(--spacing-xl);
  color: var(--text-primary);
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.comment-count {
  color: var(--text-secondary);
  font-weight: normal;
  font-size: 1rem;
}

/* 评论表单 */
.comment-form-wrapper {
  margin-bottom: var(--spacing-2xl);
  padding: var(--spacing-xl);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.comment-form .form-group {
  margin-bottom: var(--spacing-lg);
}

.comment-form textarea {
  width: 100%;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.6;
  resize: vertical;
  min-height: 120px;
  transition: var(--transition-normal);
}

.comment-form textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.comment-form textarea::placeholder {
  color: var(--text-muted);
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.form-options {
  display: flex;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  font-size: 0.9rem;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-color);
}

/* 评论列表 */
.comments-list {
  margin-bottom: var(--spacing-xl);
}

.comment {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-xl);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: var(--transition-normal);
}

.comment:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.comment-avatar {
  flex-shrink: 0;
}

.comment-avatar img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--border-color);
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.comment-author {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.95rem;
}

.comment-date {
  color: var(--text-muted);
  font-size: 0.8rem;
}

.comment-text {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
}

.comment-actions {
  display: flex;
  gap: var(--spacing-lg);
}

.comment-action {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 0.8rem;
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-normal);
}

.comment-action:hover {
  color: var(--primary-color);
  background: rgba(0, 212, 255, 0.1);
}

.comment-action.like.active {
  color: var(--primary-color);
}

.load-more-comments {
  text-align: center;
}

/* 返回顶部按钮 */
.back-to-top {
  position: fixed;
  bottom: var(--spacing-xl);
  right: var(--spacing-xl);
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-normal);
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  z-index: 1000;
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.back-to-top:hover {
  background: var(--secondary-color);
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

/* 页脚样式 */
.footer {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-3xl) 0 var(--spacing-xl);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-xl);
}

.footer-section h4 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-size: 1.1rem;
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.footer-section p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: var(--spacing-sm);
}

.footer-section a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition-normal);
}

.footer-section a:hover {
  color: var(--primary-color);
}

.social-links {
  display: flex;
  gap: var(--spacing-md);
}

.social-links a {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border-radius: 50%;
  text-decoration: none;
  transition: var(--transition-normal);
  border: 1px solid var(--border-color);
}

.social-links a:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.footer-bottom {
  text-align: center;
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--border-color);
  color: var(--text-muted);
  font-size: 0.9rem;
}

/* 评论区响应式 */
@media (max-width: 768px) {
  .comment {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .comment-avatar {
    align-self: flex-start;
  }

  .comment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .social-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .comment-form-wrapper {
    padding: var(--spacing-md);
  }

  .comment {
    padding: var(--spacing-md);
  }

  .back-to-top {
    bottom: var(--spacing-md);
    right: var(--spacing-md);
    width: 45px;
    height: 45px;
  }
}

/* ===== 触摸设备优化 ===== */
@media (hover: none) and (pointer: coarse) {
  /* 移除悬浮效果，改为点击效果 */
  .related-article:hover {
    transform: none;
  }

  .related-article:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  .share-btn:hover {
    transform: none;
  }

  .share-btn:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  .tag:hover {
    background: var(--bg-primary);
    color: var(--text-secondary);
    border-color: var(--border-color);
  }

  .tag:active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }

  /* 增大触摸目标 */
  .table-of-contents a {
    padding: var(--spacing-sm) var(--spacing-md);
    margin: var(--spacing-xs) 0;
    border-radius: var(--border-radius-sm);
  }

  .comment-action {
    padding: var(--spacing-sm) var(--spacing-md);
    min-height: 44px;
  }

  .copy-btn {
    padding: var(--spacing-sm);
    min-width: 44px;
    min-height: 44px;
  }
}

/* ===== 超小屏幕优化 (320px) ===== */
@media (max-width: 320px) {
  .article-title {
    font-size: 1.6rem;
    line-height: 1.2;
  }

  .article-subtitle {
    font-size: 1rem;
  }

  .article-meta {
    gap: var(--spacing-md);
  }

  .article-stats {
    gap: var(--spacing-sm);
  }

  .stat-item {
    font-size: 0.8rem;
  }

  .article-body h2 {
    font-size: 1.6rem;
  }

  .article-body h3 {
    font-size: 1.3rem;
  }

  .code-block {
    margin: var(--spacing-md) -var(--spacing-md);
    border-radius: 0;
  }

  .code-block pre {
    padding: var(--spacing-md);
    font-size: 0.8rem;
  }

  .performance-grid {
    gap: var(--spacing-md);
  }

  .performance-item {
    padding: var(--spacing-md);
  }

  .performance-icon {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .share-buttons {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
  }

  .share-btn {
    padding: var(--spacing-sm);
    font-size: 0.8rem;
  }

  .comment-form textarea {
    min-height: 100px;
    font-size: 0.9rem;
  }

  .author-avatar {
    width: 50px;
    height: 50px;
  }

  .author-avatar-large {
    width: 70px;
    height: 70px;
  }
}

/* ===== 横屏手机优化 ===== */
@media (max-width: 768px) and (orientation: landscape) {
  .article-header {
    padding: calc(var(--navbar-height) + var(--spacing-xl)) 0 var(--spacing-xl);
  }

  .article-title {
    font-size: 2.2rem;
  }

  .article-layout {
    gap: var(--spacing-xl);
  }

  .sidebar-section {
    display: none;
  }

  .toc-section {
    display: block;
    position: sticky;
    top: var(--spacing-md);
    max-height: 300px;
    overflow-y: auto;
  }
}

/* ===== 平板优化 ===== */
@media (min-width: 769px) and (max-width: 1024px) {
  .article-layout {
    grid-template-columns: 1fr 250px;
    gap: var(--spacing-2xl);
  }

  .article-title {
    font-size: 2.5rem;
  }

  .article-subtitle {
    font-size: 1.2rem;
  }

  .performance-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .comments-section {
    padding: var(--spacing-2xl) 0;
  }

  .comment-form-wrapper {
    padding: var(--spacing-lg);
  }

  .comment {
    padding: var(--spacing-lg);
  }
}

/* ===== 大屏幕优化 ===== */
@media (min-width: 1440px) {
  .article-layout {
    grid-template-columns: 1fr 350px;
    gap: var(--spacing-4xl);
  }

  .article-title {
    font-size: 3.5rem;
  }

  .article-subtitle {
    font-size: 1.4rem;
    max-width: 900px;
  }

  .article-body {
    padding: var(--spacing-3xl);
  }

  .article-body h2 {
    font-size: 2.2rem;
  }

  .article-body h3 {
    font-size: 1.7rem;
  }

  .article-body p {
    font-size: 1.1rem;
    line-height: 1.8;
  }

  .performance-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .code-block pre {
    font-size: 1rem;
    padding: var(--spacing-xl);
  }

  .info-box {
    padding: var(--spacing-xl);
  }
}

/* ===== 超大屏幕优化 ===== */
@media (min-width: 1920px) {
  .article-layout {
    grid-template-columns: 1fr 400px;
    gap: var(--spacing-5xl);
  }

  .article-title {
    font-size: 4rem;
  }

  .article-subtitle {
    font-size: 1.5rem;
    max-width: 1000px;
  }

  .article-body {
    padding: var(--spacing-4xl);
  }

  .article-body h2 {
    font-size: 2.5rem;
  }

  .article-body h3 {
    font-size: 2rem;
  }

  .article-body p {
    font-size: 1.2rem;
    line-height: 1.9;
  }
}

/* ===== 减少动画偏好 ===== */
@media (prefers-reduced-motion: reduce) {
  .article-content,
  .comment,
  .share-btn,
  .back-to-top,
  .related-article {
    transition: none !important;
  }

  .back-to-top {
    opacity: 1;
    visibility: visible;
    transform: none;
  }

  .reading-progress {
    transition: none !important;
  }
}

/* ===== 高对比度模式 ===== */
@media (prefers-contrast: high) {
  .article-content {
    border: 2px solid var(--border-color);
  }

  .sidebar-section {
    border: 2px solid var(--border-color);
  }

  .comment {
    border: 2px solid var(--border-color);
  }

  .code-block {
    border: 2px solid var(--border-color);
  }

  .info-box {
    border: 2px solid rgba(255, 193, 7, 0.8);
  }

  .performance-item {
    border: 2px solid var(--border-color);
  }
}

/* ===== 打印样式 ===== */
@media print {
  .navbar,
  .article-sidebar,
  .comments-section,
  .back-to-top,
  .share-buttons,
  .comment-actions,
  .reading-progress {
    display: none !important;
  }

  .article-layout {
    grid-template-columns: 1fr;
  }

  .article-header {
    padding: 20pt 0;
    background: white !important;
  }

  .article-title {
    font-size: 24pt;
    color: black !important;
    background: none !important;
    -webkit-text-fill-color: black !important;
  }

  .article-subtitle {
    font-size: 14pt;
    color: #666 !important;
  }

  .article-body {
    padding: 0;
  }

  .article-body h2 {
    font-size: 18pt;
    color: black !important;
    page-break-after: avoid;
  }

  .article-body h3 {
    font-size: 16pt;
    color: black !important;
    page-break-after: avoid;
  }

  .article-body p {
    font-size: 12pt;
    line-height: 1.6;
    color: black !important;
  }

  .code-block {
    border: 1pt solid #ccc;
    page-break-inside: avoid;
    background: #f5f5f5 !important;
  }

  .code-block pre {
    font-size: 10pt;
    color: black !important;
  }

  .info-box {
    border: 1pt solid #ccc;
    background: #f9f9f9 !important;
    page-break-inside: avoid;
  }

  .performance-grid {
    display: block;
  }

  .performance-item {
    border: 1pt solid #ccc;
    margin-bottom: 10pt;
    page-break-inside: avoid;
  }

  .article-tags {
    border-top: 1pt solid #ccc;
    page-break-before: avoid;
  }

  .tag {
    border: 1pt solid #ccc;
    background: white !important;
    color: black !important;
  }

  .featured-image {
    max-width: 100%;
    height: auto;
    page-break-inside: avoid;
  }
}
