<!-- Generated by SpecStory -->

# FreeMarker JSON Output for Arrays (2025-04-17 17:13:20)

_**User**_

freemarker 中。 如何直接把数组直接 json 输出。 用表达式写法。

---

_**Assistant**_

在 FreeMarker（freemarker） 模板中，如果你想把一个数组（或列表、Map等）直接以 JSON 格式输出，可以使用内置的 json_string 内建函数（built-in），写法如下：

```freemarker
${yourArray?json_string}
```

其中，yourArray 是你要输出的数组变量名。

举例说明：  
假设你在数据模型中有如下数组：

```json
{
  "arr": [1, 2, 3, 4, 5]
}
```

在 FreeMarker 模板中直接输出 JSON 字符串：

```freemarker
${arr?json_string}
```

输出结果为：

```json
[1,2,3,4,5]
```

如果是对象或更复杂的数据结构也同理：

```freemarker
${yourObject?json_string}
```

**注意事项：**
- 需要 FreeMarker 2.3.24 及以上版本才支持 ?json_string。
- 这种写法是表达式写法，适合你在模板中直接插值输出。

如果你的 FreeMarker 版本较老，没有 ?json_string，可以用 ?string('json')：

```freemarker
${arr?string('json')}
```

效果一样。

---

总结：  
直接用 `${变量名?json_string}` 或 `${变量名?string('json')}` 即可把数组/对象以 JSON 格式输出。

---

_**User**_

<#list 组合节点.result.list as res>${res.id}:${res.content}</#list>

这是当前的 freemarker 的写法。 我需要在 content 后面加上一个换行

---

_**Assistant**_

你可以直接在 `${res.content}` 后面加上 `换行符`，在 FreeMarker 中常用的换行符有两种写法：

1. 直接写 `\n`，但要用 `?interpret` 让其生效
2. 使用 FreeMarker 的内置变量 `line_separator`

推荐写法如下：

```freemarker
<#list 组合节点.result.list as res>
${res.id}:${res.content}<#lt>
</#list>
```

或者：

```freemarker
<#list 组合节点.result.list as res>
${res.id}:${res.content}${"\n"?interpret}
</#list>
```

或者用 `line_separator`：

```freemarker
<#list 组合节点.result.list as res>
${res.id}:${res.content}${line_separator}
</#list>
```

**总结：**  
最简洁推荐写法是：

```freemarker
<#list 组合节点.result.list as res>
${res.id}:${res.content}<#lt>
</#list>
```

这样每个内容后面都会有一个换行。

---

_**User**_

现在有一个值："2.用户输入的内容：${inputs_content}"
你通过 freemarker 写法给我添加一个默认值



---

_**Assistant**_



---



---

