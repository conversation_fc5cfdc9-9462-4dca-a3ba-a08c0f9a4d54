<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TechBlog - 探索科技前沿</title>
    <link rel="stylesheet" href="styles.css" />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
  </head>
  <body>
    <!-- 粒子背景 -->
    <div id="particles-js"></div>

    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <div class="logo-icon">
            <i class="fas fa-terminal"></i>
          </div>
          <span class="logo-text">TechBlog</span>
        </div>
        <ul class="nav-menu">
          <li class="nav-item">
            <a href="#home" class="nav-link">首页</a>
          </li>
          <li class="nav-item">
            <a href="#articles" class="nav-link">文章</a>
          </li>
          <li class="nav-item">
            <a href="#about" class="nav-link">关于</a>
          </li>
          <li class="nav-item">
            <a href="#contact" class="nav-link">联系</a>
          </li>
        </ul>
        <div class="theme-toggle">
          <i class="fas fa-moon"></i>
        </div>
        <div class="hamburger">
          <span class="bar"></span>
          <span class="bar"></span>
          <span class="bar"></span>
        </div>
      </div>
    </nav>

    <!-- 英雄区域 -->
    <section id="home" class="hero">
      <div class="hero-container">
        <div class="hero-content">
          <div class="hero-badge">
            <span class="badge-text">🚀 欢迎来到未来</span>
          </div>
          <h1 class="hero-title">
            <span class="title-line-1">探索</span>
            <span class="title-line-2 gradient-text">科技前沿</span>
            <span class="title-line-3">分享技术洞察</span>
          </h1>
          <p class="hero-description">
            在这里，我们深入探讨人工智能、云计算、区块链等前沿技术，
            分享最新的开发经验和技术见解，与你一起构建数字化未来。
          </p>
          <div class="hero-buttons">
            <a href="#articles" class="btn btn-primary">
              <span class="btn-text">开始探索</span>
              <i class="fas fa-rocket"></i>
            </a>
            <a href="#about" class="btn btn-glass">
              <span class="btn-text">了解更多</span>
              <i class="fas fa-arrow-right"></i>
            </a>
          </div>
          <div class="hero-stats">
            <div class="stat-item">
              <div class="stat-number" data-count="150">0</div>
              <div class="stat-label">技术文章</div>
            </div>
            <div class="stat-item">
              <div class="stat-number" data-count="50">0</div>
              <div class="stat-label">开源项目</div>
            </div>
            <div class="stat-item">
              <div class="stat-number" data-count="10000">0</div>
              <div class="stat-label">代码提交</div>
            </div>
          </div>
        </div>
        <div class="hero-visual">
          <div class="terminal-window">
            <div class="terminal-header">
              <div class="terminal-controls">
                <span class="control red"></span>
                <span class="control yellow"></span>
                <span class="control green"></span>
              </div>
              <div class="terminal-title">~/techblog</div>
            </div>
            <div class="terminal-body">
              <div class="terminal-line">
                <span class="prompt">➜</span>
                <span class="path">~</span>
                <span class="command typing">npm run dev</span>
              </div>
              <div class="terminal-line">
                <span class="output">🚀 Starting development server...</span>
              </div>
              <div class="terminal-line">
                <span class="output success"
                  >✓ Server running on http://localhost:3000</span
                >
              </div>
              <div class="terminal-line">
                <span class="output">📦 Building for production...</span>
              </div>
              <div class="terminal-line">
                <span class="output success"
                  >✓ Build completed successfully!</span
                >
              </div>
              <div class="terminal-line">
                <span class="prompt">➜</span>
                <span class="path">~</span>
                <span class="cursor">|</span>
              </div>
            </div>
          </div>
          <div class="floating-tech">
            <div class="tech-icon" style="--delay: 0s">
              <i class="fab fa-react"></i>
            </div>
            <div class="tech-icon" style="--delay: 0.5s">
              <i class="fab fa-python"></i>
            </div>
            <div class="tech-icon" style="--delay: 1s">
              <i class="fab fa-js-square"></i>
            </div>
            <div class="tech-icon" style="--delay: 1.5s">
              <i class="fab fa-docker"></i>
            </div>
            <div class="tech-icon" style="--delay: 2s">
              <i class="fas fa-brain"></i>
            </div>
          </div>
        </div>
      </div>
      <div class="scroll-indicator">
        <div class="scroll-text">向下滚动</div>
        <div class="scroll-arrow">
          <i class="fas fa-chevron-down"></i>
        </div>
      </div>
    </section>

    <!-- 关于我 -->
    <section id="about" class="about">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">关于我</h2>
          <p class="section-subtitle">了解我的技术背景和专业经验</p>
        </div>
        <div class="about-content">
          <div class="about-text">
            <p>
              我是一名充满热情的全栈开发工程师，拥有5年以上的软件开发经验。专注于使用现代技术栈构建高性能、可扩展的Web应用程序。
            </p>
            <p>
              在前端开发方面，我精通React、Vue.js和现代JavaScript框架。在后端开发中，我熟练使用Node.js、Python和云服务架构。
            </p>
            <div class="skills-grid">
              <div class="skill-item">
                <i class="fab fa-js-square"></i>
                <span>JavaScript</span>
              </div>
              <div class="skill-item">
                <i class="fab fa-python"></i>
                <span>Python</span>
              </div>
              <div class="skill-item">
                <i class="fab fa-react"></i>
                <span>React</span>
              </div>
              <div class="skill-item">
                <i class="fab fa-node-js"></i>
                <span>Node.js</span>
              </div>
              <div class="skill-item">
                <i class="fab fa-docker"></i>
                <span>Docker</span>
              </div>
              <div class="skill-item">
                <i class="fas fa-cloud"></i>
                <span>AWS</span>
              </div>
            </div>
          </div>
          <div class="about-stats">
            <div class="stat-item">
              <div class="stat-number">5+</div>
              <div class="stat-label">年工作经验</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">50+</div>
              <div class="stat-label">完成项目</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">100+</div>
              <div class="stat-label">技术文章</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 精选文章区域 -->
    <section id="articles" class="featured-articles">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">最新博客</h2>
          <p class="section-subtitle">分享我的技术见解和开发经验</p>
        </div>
        <div class="articles-grid">
          <article class="article-card">
            <div class="article-image">
              <div class="article-category">前端开发</div>
            </div>
            <div class="article-content">
              <h3 class="article-title">React 18 新特性深度解析</h3>
              <p class="article-excerpt">
                探索React
                18的并发特性，Suspense边界，以及如何优化大型应用的性能...
              </p>
              <div class="article-meta">
                <span class="article-date"
                  ><i class="fas fa-calendar"></i> 2024-03-15</span
                >
                <span class="article-read-time"
                  ><i class="fas fa-clock"></i> 8分钟阅读</span
                >
              </div>
            </div>
          </article>
          <article class="article-card">
            <div class="article-image">
              <div class="article-category">后端开发</div>
            </div>
            <div class="article-content">
              <h3 class="article-title">Node.js 微服务架构实战</h3>
              <p class="article-excerpt">
                如何使用Node.js构建可扩展的微服务架构，包含API网关、服务发现等关键组件...
              </p>
              <div class="article-meta">
                <span class="article-date"
                  ><i class="fas fa-calendar"></i> 2024-03-10</span
                >
                <span class="article-read-time"
                  ><i class="fas fa-clock"></i> 12分钟阅读</span
                >
              </div>
            </div>
          </article>
          <article class="article-card">
            <div class="article-image">
              <div class="article-category">AI/ML</div>
            </div>
            <div class="article-content">
              <h3 class="article-title">将ChatGPT集成到Web应用中</h3>
              <p class="article-excerpt">
                详细介绍如何将OpenAI的GPT模型集成到现代Web应用中，包含最佳实践和安全考虑...
              </p>
              <div class="article-meta">
                <span class="article-date"
                  ><i class="fas fa-calendar"></i> 2024-03-05</span
                >
                <span class="article-read-time"
                  ><i class="fas fa-clock"></i> 10分钟阅读</span
                >
              </div>
            </div>
          </article>
          <article class="article-card">
            <div class="article-image">
              <div class="article-category">DevOps</div>
            </div>
            <div class="article-content">
              <h3 class="article-title">Docker容器化最佳实践</h3>
              <p class="article-excerpt">
                从零开始学习Docker容器化技术，包含多阶段构建、安全配置和生产环境部署...
              </p>
              <div class="article-meta">
                <span class="article-date"
                  ><i class="fas fa-calendar"></i> 2024-02-28</span
                >
                <span class="article-read-time"
                  ><i class="fas fa-clock"></i> 15分钟阅读</span
                >
              </div>
            </div>
          </article>
        </div>
      </div>
    </section>

    <!-- 项目展示 -->
    <section id="projects" class="projects">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">精选项目</h2>
          <p class="section-subtitle">展示我的技术能力和创新思维</p>
        </div>
        <div class="projects-grid">
          <div class="project-card">
            <div class="project-image">
              <div class="project-overlay">
                <div class="project-links">
                  <a href="#" class="project-link"
                    ><i class="fas fa-external-link-alt"></i
                  ></a>
                  <a href="#" class="project-link"
                    ><i class="fab fa-github"></i
                  ></a>
                </div>
              </div>
            </div>
            <div class="project-content">
              <h3 class="project-title">智能任务管理系统</h3>
              <p class="project-description">
                基于React和Node.js的智能任务管理平台，集成AI助手功能，支持团队协作和项目跟踪。
              </p>
              <div class="project-tech">
                <span class="tech-tag">React</span>
                <span class="tech-tag">Node.js</span>
                <span class="tech-tag">MongoDB</span>
                <span class="tech-tag">OpenAI</span>
              </div>
            </div>
          </div>
          <div class="project-card">
            <div class="project-image">
              <div class="project-overlay">
                <div class="project-links">
                  <a href="#" class="project-link"
                    ><i class="fas fa-external-link-alt"></i
                  ></a>
                  <a href="#" class="project-link"
                    ><i class="fab fa-github"></i
                  ></a>
                </div>
              </div>
            </div>
            <div class="project-content">
              <h3 class="project-title">实时数据分析仪表板</h3>
              <p class="project-description">
                使用Vue.js和Python构建的实时数据可视化平台，支持多数据源集成和自定义图表。
              </p>
              <div class="project-tech">
                <span class="tech-tag">Vue.js</span>
                <span class="tech-tag">Python</span>
                <span class="tech-tag">WebSocket</span>
                <span class="tech-tag">Chart.js</span>
              </div>
            </div>
          </div>
          <div class="project-card">
            <div class="project-image">
              <div class="project-overlay">
                <div class="project-links">
                  <a href="#" class="project-link"
                    ><i class="fas fa-external-link-alt"></i
                  ></a>
                  <a href="#" class="project-link"
                    ><i class="fab fa-github"></i
                  ></a>
                </div>
              </div>
            </div>
            <div class="project-content">
              <h3 class="project-title">云原生电商平台</h3>
              <p class="project-description">
                基于微服务架构的电商平台，采用Docker容器化部署，支持高并发和弹性扩展。
              </p>
              <div class="project-tech">
                <span class="tech-tag">Microservices</span>
                <span class="tech-tag">Docker</span>
                <span class="tech-tag">Kubernetes</span>
                <span class="tech-tag">Redis</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 联系我 -->
    <section id="contact" class="contact">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">联系我</h2>
          <p class="section-subtitle">让我们一起探讨技术，共同成长</p>
        </div>
        <div class="contact-content">
          <div class="contact-info">
            <div class="contact-item">
              <i class="fas fa-envelope"></i>
              <div class="contact-details">
                <h4>邮箱</h4>
                <p><EMAIL></p>
              </div>
            </div>
            <div class="contact-item">
              <i class="fas fa-phone"></i>
              <div class="contact-details">
                <h4>电话</h4>
                <p>+86 138 0000 0000</p>
              </div>
            </div>
            <div class="contact-item">
              <i class="fas fa-map-marker-alt"></i>
              <div class="contact-details">
                <h4>位置</h4>
                <p>上海，中国</p>
              </div>
            </div>
          </div>
          <div class="contact-form">
            <form class="form">
              <div class="form-group">
                <input
                  type="text"
                  class="form-input"
                  placeholder="您的姓名"
                  required
                />
              </div>
              <div class="form-group">
                <input
                  type="email"
                  class="form-input"
                  placeholder="您的邮箱"
                  required
                />
              </div>
              <div class="form-group">
                <textarea
                  class="form-input"
                  rows="5"
                  placeholder="您的消息"
                  required
                ></textarea>
              </div>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-paper-plane"></i>
                发送消息
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-text">
            <p>&copy; 2024 Alex Chen. 保留所有权利。</p>
          </div>
          <div class="footer-social">
            <a href="#" class="social-link"><i class="fab fa-github"></i></a>
            <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
            <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
            <a href="#" class="social-link"><i class="fab fa-weibo"></i></a>
          </div>
        </div>
      </div>
    </footer>

    <script src="script.js"></script>
  </body>
</html>
