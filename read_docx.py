#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import docx
import sys

def read_docx_file(file_path):
    """读取Word文档内容"""
    try:
        doc = docx.Document(file_path)
        content = []
        
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                content.append(paragraph.text.strip())
        
        # 也尝试读取表格内容
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_text.append(cell.text.strip())
                if row_text:
                    content.append(" | ".join(row_text))
        
        return "\n".join(content)
    
    except Exception as e:
        return f"读取文档时出错: {str(e)}"

if __name__ == "__main__":
    file_path = "【智能创新】王泽龙工作周报.docx"
    content = read_docx_file(file_path)
    print(content)
