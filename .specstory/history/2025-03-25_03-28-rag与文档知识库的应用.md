<!-- Generated by SpecStory -->

# RAG与文档知识库的应用 (2025-03-25 11:28:40)

_**User**_

你知道RAG 中的， 类似文档知识库的相关用法吗？

---

_**Assistant**_



---

RAG（检索增强生成）是一种结合文档检索和生成式AI的技术，用于构建基于知识的问答系统。以下是文档知识库在RAG中的基本用法：

1. **构建知识库**：
   - 文档收集与处理
   - 文本分块
   - 向量化存储

2. **常用Python框架**：
   - LangChain
   - LlamaIndex
   - Haystack

3. **基本实现示例**（使用LangChain）：

```python
# 安装依赖
pip install langchain langchain-openai faiss-cpu tiktoken -i https://pypi.tuna.tsinghua.edu.cn/simple

# 基本代码
from langchain_openai import OpenAIEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.vectorstores import FAISS
from langchain_openai import ChatOpenAI
from langchain.chains import RetrievalQA
from langchain.document_loaders import DirectoryLoader

# 加载文档
loader = DirectoryLoader('./docs', glob='**/*.pdf')
documents = loader.load()

# 文本分块
text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
texts = text_splitter.split_documents(documents)

# 创建向量库
embeddings = OpenAIEmbeddings()
vectorstore = FAISS.from_documents(texts, embeddings)

# 构建RAG系统
llm = ChatOpenAI(model="gpt-3.5-turbo")
qa_chain = RetrievalQA.from_chain_type(
    llm=llm,
    chain_type="stuff",
    retriever=vectorstore.as_retriever()
)

# 查询
response = qa_chain.invoke("你的问题")
```

4. **向量数据库选择**：
   - Faiss（内存型）
   - Chroma
   - Pinecone
   - Weaviate
   - Milvus

需要根据具体应用场景和数据规模选择合适的组件。

---

_**User**_

我们灵犀平台目前实现了 问答知识库、 文档知识库、和结构化知识库。 这里做知识库维护。
编排的时候有专门的组件可以做向量查询。

现在同事有一些疑问，询问我相关 RAG 的文档知识库怎么使用。 但从他的表述来看， 他对 RAG 的理解有些误解， 你帮我分析一下

---

_**Assistant**_



---

根据截图和您提供的信息，您同事对RAG和文档知识库的理解存在以下几个误解：

1. **检索机制误解**：同事似乎将文档知识库当作简单的关键词搜索工具，而不是理解RAG基于语义的检索机制。从"怎么从文档知识库直查出来建表语句"的提问可以看出，他期望直接精确提取特定内容。

2. **提问方式不当**：截图中的问题如"这个文档查询怎么知道我要查的是什么"表明同事对如何有效提问缺乏理解。RAG需要明确、具体的问题才能返回相关内容。

3. **系统能力边界认知不清**：同事期望系统能自动理解他"要查的是什么"，这超出了基础RAG系统的能力范围。

4. **向量检索原理不了解**：没有理解文档是如何被向量化和检索的，因此对系统返回结果的方式存在误解。

5. **交互模式混淆**：可能将传统数据库查询与知识库语义检索混为一谈，认为可以用类似SQL的精确方式"直查"出结构化信息。

建议向同事解释RAG的正确使用方法：
- 清晰表述完整问题
- 提供足够上下文
- 理解RAG是基于语义相似度的模糊匹配，非精确查询
- 对于结构化信息提取，可能需要结合结构化知识库功能

截图中的系统界面看起来已经分类了不同知识库类型，可以引导同事正确选择和使用合适的知识库类型。

---

_**User**_

所以我应该如何回复他。 用比较日常的语言

---

_**Assistant**_



---

嗯，看了你的问题，我觉得咱们可能对文档知识库的使用方式有些不同理解。

文档知识库不太像传统数据库那样可以直接"查询"出特定内容，比如建表语句。它更像是一个聪明的助手，通过理解你问题的意思，从文档中找到相关内容。

你可以这样用：
- 尽量问完整的问题，比如"数据库用户表的创建语句是什么"比"给表名"会得到更好的结果
- 如果找不到想要的内容，可以换个问法，添加更多细节
- 系统是按照语义相似度找内容的，不是精确匹配关键词

如果你确实需要精确查找像建表语句这样的技术内容，可以：
1. 先描述你要找的具体是什么文档
2. 然后说明你需要的是什么部分的内容
3. 如果不行，我们也可以用结构化知识库，那个对技术内容查询会更精准些

要不你再试试看？如果还有问题我们可以一起看看~

---

_**User**_

你先完整描述文档知识库的知识背景。 RAG 相关的。
比如检索是根据语句 找文档里相似语义的 chunk， 解释完这个再说他的理解有误是哪方面

---

_**Assistant**_



---



---

