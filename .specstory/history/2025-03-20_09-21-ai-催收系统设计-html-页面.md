<!-- Generated by SpecStory -->

# AI 催收系统设计 HTML 页面 (2025-03-20 17:21:20)

_**User**_

根据我们这篇 AI 催收系统的设计总结。以及催收思路。
我希望你画一个HTML 页面。 这个 HTML 就得是一个完整的能表达我们系统的全部。 你可以用很多 SVG 图来帮忙辅助理解。   
生成一个非常专业的系统设计。  可以用你能想到的辅助表达的工具。

给我这样的 html。

---

_**Assistant**_



---

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI催收系统设计</title>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --bg-color: #f9f9f9;
            --text-color: #333;
            --border-color: #ddd;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --light-blue: #d6eaf8;
            --light-green: #d5f5e3;
            --light-red: #fadbd8;
            --light-yellow: #fef9e7;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--bg-color);
            padding: 0;
            margin: 0;
        }
        
        header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            position: relative;
            z-index: 1;
        }
        
        header p {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }
        
        .header-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1.5rem;
        }
        
        section {
            margin-bottom: 3rem;
            background: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        h2 {
            color: var(--primary-color);
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--border-color);
        }
        
        h3 {
            color: var(--secondary-color);
            font-size: 1.4rem;
            margin: 1.5rem 0 1rem;
        }
        
        p {
            margin-bottom: 1rem;
        }
        
        .svg-container {
            width: 100%;
            margin: 2rem 0;
            display: flex;
            justify-content: center;
            overflow-x: auto;
        }
        
        svg {
            max-width: 100%;
        }
        
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border-left: 4px solid var(--secondary-color);
        }
        
        .card.strategy {
            border-left-color: var(--accent-color);
        }
        
        .card.process {
            border-left-color: var(--success-color);
        }
        
        .card.tactics {
            border-left-color: var(--warning-color);
        }
        
        .card h4 {
            color: var(--primary-color);
            margin-bottom: 0.8rem;
        }
        
        .card ul {
            list-style-position: inside;
            padding-left: 0.5rem;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        .tab {
            padding: 0.8rem 1.5rem;
            cursor: pointer;
            background: #eee;
            border: 1px solid var(--border-color);
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            margin-right: 0.5rem;
        }
        
        .tab.active {
            background: white;
            font-weight: bold;
            border-bottom: 1px solid white;
            margin-bottom: -1px;
            color: var(--secondary-color);
        }
        
        .tab-content {
            display: none;
            padding: 1.5rem;
            background: white;
            border: 1px solid var(--border-color);
            border-top: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5rem 0;
        }
        
        .comparison-table th, .comparison-table td {
            padding: 1rem;
            border: 1px solid var(--border-color);
            text-align: left;
        }
        
        .comparison-table th {
            background-color: var(--light-blue);
            font-weight: bold;
        }
        
        .comparison-table tr:nth-child(even) {
            background-color: #f5f5f5;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            margin-bottom: 0.8rem;
            padding-left: 1.5rem;
            position: relative;
        }
        
        .feature-list li::before {
            content: "•";
            color: var(--secondary-color);
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .highlight {
            background-color: var(--light-yellow);
            padding: 1rem;
            border-radius: 5px;
            margin: 1.5rem 0;
            border-left: 4px solid var(--warning-color);
        }
        
        .animation-container {
            position: relative;
            height: 400px;
            margin: 2rem 0;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }
        
        footer {
            background-color: var(--primary-color);
            color: white;
            padding: 2rem;
            text-align: center;
            margin-top: 3rem;
        }
        
        @media (max-width: 768px) {
            .card-grid {
                grid-template-columns: 1fr;
            }
            
            header h1 {
                font-size: 2rem;
            }
            
            header p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <svg class="header-bg" viewBox="0 0 100 100" preserveAspectRatio="none">
            <pattern id="pattern-circles" x="0" y="0" width="10" height="10" patternUnits="userSpaceOnUse">
                <circle cx="5" cy="5" r="1" fill="white"></circle>
            </pattern>
            <rect x="0" y="0" width="100" height="100" fill="url(#pattern-circles)"></rect>
        </svg>
        <h1>AI催收系统设计</h1>
        <p>从"意图-回应"到"策略-流程-话术"的智能升级</p>
    </header>
    
    <div class="container">
        <section id="overview">
            <h2>系统概述</h2>
            <p>我们的AI催收系统摒弃了传统的单一"意图-回应"模式，采用创新的"策略-流程-话术"三层架构，实现更智能、更高效的催收效果。系统通过客户画像分析、多维度决策和主动引导，模拟人类催收员的工作方式，提升催收成功率。</p>
            
            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <defs>
                        <linearGradient id="grad-strategy" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#e74c3c;stop-opacity:0.6" />
                        </linearGradient>
                        <linearGradient id="grad-process" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#27ae60;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#27ae60;stop-opacity:0.6" />
                        </linearGradient>
                        <linearGradient id="grad-tactics" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#f39c12;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#f39c12;stop-opacity:0.6" />
                        </linearGradient>
                    </defs>
                    
                    <!-- 策略层 -->
                    <rect x="100" y="50" width="600" height="80" rx="5" fill="url(#grad-strategy)" />
                    <text x="400" y="75" text-anchor="middle" font-size="18" fill="white">策略层 (Strategy)</text>
                    
                    <rect x="150" y="85" width="200" height="30" rx="5" fill="white" stroke="#e74c3c" />
                    <text x="250" y="105" text-anchor="middle" font-size="14">柔性催收策略</text>
                    
                    <rect x="450" y="85" width="200" height="30" rx="5" fill="white" stroke="#e74c3c" />
                    <text x="550" y="105" text-anchor="middle" font-size="14">强硬催收策略</text>
                    
                    <!-- 连接箭头 -->
                    <line x1="400" y1="130" x2="400" y2="160" stroke="#333" stroke-width="2" />
                    <polygon points="400,160 395,150 405,150" fill="#333" />
                    
                    <!-- 流程层 -->
                    <rect x="100" y="160" width="600" height="80" rx="5" fill="url(#grad-process)" />
                    <text x="400" y="185" text-anchor="middle" font-size="18" fill="white">流程层 (Process)</text>
                    
                    <rect x="150" y="195" width="80" height="30" rx="5" fill="white" stroke="#27ae60" />
                    <text x="190" y="215" text-anchor="middle" font-size="14">开场</text>
                    
                    <rect x="250" y="195" width="80" height="30" rx="5" fill="white" stroke="#27ae60" />
                    <text x="290" y="215" text-anchor="middle" font-size="14">说明</text>
                    
                    <rect x="350" y="195" width="80" height="30" rx="5" fill="white" stroke="#27ae60" />
                    <text x="390" y="215" text-anchor="middle" font-size="14">解决</text>
                    
                    <rect x="450" y="195" width="80" height="30" rx="5" fill="white" stroke="#27ae60" />
                    <text x="490" y="215" text-anchor="middle" font-size="14">结束</text>
                    
                    <!-- 流程箭头 -->
                    <line x1="230" y1="210" x2="250" y2="210" stroke="#27ae60" stroke-width="2" />
                    <polygon points="250,210 240,205 240,215" fill="#27ae60" />
                    
                    <line x1="330" y1="210" x2="350" y2="210" stroke="#27ae60" stroke-width="2" />
                    <polygon points="350,210 340,205 340,215" fill="#27ae60" />
                    
                    <line x1="430" y1="210" x2="450" y2="210" stroke="#27ae60" stroke-width="2" />
                    <polygon points="450,210 440,205 440,215" fill="#27ae60" />
                    
                    <!-- 连接箭头 -->
                    <line x1="400" y1="240" x2="400" y2="270" stroke="#333" stroke-width="2" />
                    <polygon points="400,270 395,260 405,260" fill="#333" />
                    
                    <!-- 话术层 -->
                    <rect x="100" y="270" width="600" height="80" rx="5" fill="url(#grad-tactics)" />
                    <text x="400" y="295" text-anchor="middle" font-size="18" fill="white">话术层 (Tactics)</text>
                    
                    <rect x="150" y="305" width="120" height="30" rx="5" fill="white" stroke="#f39c12" />
                    <text x="210" y="325" text-anchor="middle" font-size="14">问候话术</text>
                    
                    <rect x="290" y="305" width="120" height="30" rx="5" fill="white" stroke="#f39c12" />
                    <text x="350" y="325" text-anchor="middle" font-size="14">解释话术</text>
                    
                    <rect x="430" y="305" width="120" height="30" rx="5" fill="white" stroke="#f39c12" />
                    <text x="490" y="325" text-anchor="middle" font-size="14">协商话术</text>
                </svg>
            </div>
        </section>
        
        <section id="architecture">
            <h2>三层架构详解</h2>
            <div class="card-grid">
                <div class="card strategy">
                    <h4>策略层</h4>
                    <ul>
                        <li>长期目标指导</li>
                        <li>客户类型匹配</li>
                        <li>通话整体规划</li>
                        <li>根据客户画像、欠款情况、历史记录选择合适的催收策略</li>
                    </ul>
                </div>
                
                <div class="card process">
                    <h4>流程层</h4>
                    <ul>
                        <li>阶段性目标追踪</li>
                        <li>对话进度管理</li>
                        <li>预设路径导航</li>
                        <li>每种策略下有明确的对话流程和阶段目标</li>
                    </ul>
                </div>
                
                <div class="card tactics">
                    <h4>话术层</h4>
                    <ul>
                        <li>意图识别与回应</li>
                        <li>情境适配表达</li>
                        <li>引导元素植入</li>
                        <li>在流程指导下选择话术，而非仅根据客户当前意图</li>
                    </ul>
                </div>
            </div>
            
            <h3>传统模式 vs 新系统架构</h3>
            <div class="svg-container">
                <svg width="800" height="300" viewBox="0 0 800 300">
                    <!-- 传统模式 -->
                    <rect x="50" y="50" width="300" height="200" rx="5" fill="#f5f5f5" stroke="#ddd" />
                    <text x="200" y="30" text-anchor="middle" font-weight="bold">传统模式</text>
                    
                    <rect x="75" y="80" width="250" height="40" rx="5" fill="white" stroke="#ddd" />
                    <text x="200" y="105" text-anchor="middle">客户说话</text>
                    
                    <line x1="200" y1="120" x2="200" y2="140" stroke="#333" stroke-width="1.5" />
                    <polygon points="200,140 195,130 205,130" fill="#333" />
                    
                    <rect x="75" y="140" width="250" height="40" rx="5" fill="white" stroke="#ddd" />
                    <text x="200" y="165" text-anchor="middle">意图识别</text>
                    
                    <line x1="200" y1="180" x2="200" y2="200" stroke="#333" stroke-width="1.5" />
                    <polygon points="200,200 195,190 205,190" fill="#333" />
                    
                    <rect x="75" y="200" width="250" height="40" rx="5" fill="white" stroke="#ddd" />
                    <text x="200" y="225" text-anchor="middle">选择对应话术播放</text>
                    
                    <!-- 新系统 -->
                    <rect x="450" y="50" width="300" height="200" rx="5" fill="#f5f5f5" stroke="#ddd" />
                    <text x="600" y="30" text-anchor="middle" font-weight="bold">新系统</text>
                    
                    <rect x="475" y="70" width="250" height="40" rx="5" fill="var(--light-red)" stroke="#e74c3c" />
                    <text x="600" y="95" text-anchor="middle" font-weight="bold">策略层</text>
                    
                    <line x1="600" y1="110" x2="600" y2="130" stroke="#333" stroke-width="1.5" />
                    <polygon points="600,130 595,120 605,120" fill="#333" />
                    
                    <rect x="475" y="130" width="250" height="40" rx="5" fill="var(--light-green)" stroke="#27ae60" />
                    <text x="600" y="155" text-anchor="middle" font-weight="bold">流程层</text>
                    
                    <line x1="600" y1="170" x2="600" y2="190" stroke="#333" stroke-width="1.5" />
                    <polygon points="600,190 595,180 605,180" fill="#333" />
                    
                    <rect x="475" y="190" width="250" height="40" rx="5" fill="var(--light-yellow)" stroke="#f39c12" />
                    <text x="600" y="215" text-anchor="middle" font-weight="bold">话术层</text>
                </svg>
            </div>
        </section>
        
        <section id="workflow">
            <h2>决策系统工作流程</h2>
            <div class="svg-container">
                <svg width="800" height="600" viewBox="0 0 800 600">
                    <!-- 通话前准备 -->
                    <rect x="100" y="30" width="600" height="120" rx="5" fill="#d6eaf8" stroke="#3498db" />
                    <text x="400" y="55" text-anchor="middle" font-size="16" font-weight="bold">通话前准备</text>
                    
                    <rect x="150" y="75" width="150" height="50" rx="5" fill="white" stroke="#3498db" />
                    <text x="225" y="105" text-anchor="middle" font-size="14">客户数据分析</text>
                    
                    <rect x="325" y="75" width="150" height="50" rx="5" fill="white" stroke="#3498db" />
                    <text x="400" y="105" text-anchor="middle" font-size="14">策略预选</text>
                    
                    <rect x="500" y="75" width="150" height="50" rx="5" fill="white" stroke="#3498db" />
                    <text x="575" y="105" text-anchor="middle" font-size="14">初始阶段设置</text>
                    
                    <!-- 箭头 -->
                    <line x1="300" y1="100" x2="325" y2="100" stroke="#3498db" stroke-width="2" />
                    <polygon points="325,100 315,95 315,105" fill="#3498db" />
                    
                    <line x1="475" y1="100" x2="500" y2="100" stroke="#3498db" stroke-width="2" />
                    <polygon points="500,100 490,95 490,105" fill="#3498db" />
                    
                    <!-- 连接箭头 -->
                    <line x1="400" y1="150" x2="400" y2="180" stroke="#333" stroke-width="2" />
                    <polygon points="400,180 395,170 405,170" fill="#333" />
                    
                    <!-- 对话进行中 -->
                    <rect x="100" y="180" width="600" height="380" rx="5" fill="#d5f5e3" stroke="#27ae60" />
                    <text x="400" y="205" text-anchor="middle" font-size="16" font-weight="bold">对话进行中</text>
                    
                    <!-- 客户输入 -->
                    <rect x="200" y="225" width="400" height="40" rx="5" fill="white" stroke="#27ae60" />
                    <text x="400" y="250" text-anchor="middle" font-size="14">客户输入</text>
                    
                    <line x1="400" y1="265" x2="400" y2="285" stroke="#333" stroke-width="2" />
                    <polygon points="400,285 395,275 405,275" fill="#333" />
                    
                    <!-- 多维度分析 -->
                    <rect x="150" y="285" width="500" height="100" rx="5" fill="white" stroke="#27ae60" />
                    <text x="400" y="310" text-anchor="middle" font-size="14" font-weight="bold">多维度分析</text>
                    
                    <rect x="175" y="330" width="120" height="40" rx="5" fill="#f5f5f5" stroke="#ddd" />
                    <text x="235" y="355" text-anchor="middle" font-size="12">意图识别</text>
                    
                    <rect x="315" y="330" width="120" height="40" rx="5" fill="#f5f5f5" stroke="#ddd" />
                    <text x="375" y="355" text-anchor="middle" font-size="12">情绪分析</text>
                    
                    <rect x="455" y="330" width="120" height="40" rx="5" fill="#f5f5f5" stroke="#ddd" />
                    <text x="515" y="355" text-anchor="middle" font-size="12">对话历史分析</text>
                    
                    <line x1="400" y1="385" x2="400" y2="405" stroke="#333" stroke-width="2" />
                    <polygon points="400,405 395,395 405,395" fill="#333" />
                    
                    <!-- 决策层 -->
                    <rect x="150" y="405" width="500" height="80" rx="5" fill="white" stroke="#27ae60" />
                    <text x="400" y="430" text-anchor="middle" font-size="14" font-weight="bold">决策层</text>
                    
                    <rect x="175" y="440" width="200" height="30" rx="5" fill="#f5f5f5" stroke="#ddd" />
                    <text x="275" y="460" text-anchor="middle" font-size="12">策略评估与调整</text>
                    
                    <rect x="395" y="440" width="230" height="30" rx="5" fill="#f5f5f5" stroke="#ddd" />
                    <text x="510" y="460" text-anchor="middle" font-size="12">阶段目标管理</text>
                    
                    <line x1="400" y1="485" x2="400" y2="505" stroke="#333" stroke-width="2" />
                    <polygon points="400,505 395,495 405,495" fill="#333" />
                    
                    <!-- 回复生成 -->
                    <rect x="200" y="505" width="400" height="40" rx="5" fill="white" stroke="#27ae60" />
                    <text x="400" y="530" text-anchor="middle" font-size="14">系统回复</text>
                </svg>
            </div>
        </section>
        
        <section id="active-guidance">
            <h2>催收对话主动引导机制</h2>
            <div class="svg-container">
                <svg width="700" height="350" viewBox="0 0 700 350">
                    <rect x="100" y="30" width="500" height="290" rx="8" fill="#fef9e7" stroke="#f39c12" stroke-width="2" />
                    <text x="350" y="60" text-anchor="middle" font-size="18" font-weight="bold" fill="#333">催收对话主动引导</text>
                    
                    <rect x="150" y="80" width="400" height="220" rx="5" fill="white" stroke="#f39c12" />
                    
                    <text x="350" y="110" text-anchor="middle" font-size="16" font-weight="bold">系统回复 = 回应 + 主动引导</text>
                    
                    <rect x="170" y="130" width="170" height="150" rx="5" fill="#f5f5f5" stroke="#ddd" />
                    <text x="255" y="155" text-anchor="middle" font-size="14" font-weight="bold">回应部分</text>
                    
                    <text x="255" y="185" text-anchor="middle" font-size="12">• 针对客户意图</text>
                    <text x="255" y="215" text-anchor="middle" font-size="12">• 解答当前问题</text>
                    <text x="255" y="245" text-anchor="middle" font-size="12">• 情绪回应</text>
                    
                    <rect x="360" y="130" width="170" height="150" rx="5" fill="#f5f5f5" stroke="#ddd" />
                    <text x="445" y="155" text-anchor="middle" font-size="14" font-weight="bold">引导部分</text>
                    
                    <text x="445" y="185" text-anchor="middle" font-size="12">• 设置下一话题</text>
                    <text x="445" y="215" text-anchor="middle" font-size="12">• 引导确认信息</text>
                    <text x="445" y="245" text-anchor="middle" font-size="12">• 推进对话流程</text>
                </svg>
            </div>
            
            <h3>回应与主动引导比例动态调整</h3>
            <div class="svg-container">
                <svg width="700" height="200" viewBox="0 0 700 200">
                    <rect x="50" y="30" width="600" height="50" rx="5" fill="#f5f5f5" stroke="#ddd" />
                    <text x="350" y="20" text-anchor="middle" font-size="14">开场阶段</text>
                    <rect x="50" y="30" width="450" height="50" rx="5" fill="var(--light-blue)" stroke="#3498db" />
                    <text x="275" y="60" text-anchor="middle" fill="white">回应 (75%)</text>
                    <rect x="500" y="30" width="150" height="50" rx="0" fill="var(--light-yellow)" stroke="#f39c12" />
                    <text x="575" y="60" text-anchor="middle">引导 (25%)</text>
                    
                    <rect x="50" y="100" width="600" height="50" rx="5" fill="#f5f5f5" stroke="#ddd" />
                    <text x="350" y="90" text-anchor="middle" font-size="14">解决阶段</text>
                    <rect x="50" y="100" width="300" height="50" rx="5" fill="var(--light-blue)" stroke="#3498db" />
                    <text x="200" y="130" text-anchor="middle" fill="white">回应 (50%)</text>
                    <rect x="350" y="100" width="300" height="50" rx="0" fill="var(--light-yellow)" stroke="#f39c12" />
                    <text x="500" y="130" text-anchor="middle">引导 (50%)</text>
                    
                    <rect x="50" y="170" width="600" height="50" rx="5" fill="#f5f5f5" stroke="#ddd" />
                    <text x="350" y="160" text-anchor="middle" font-size="14">结束阶段</text>
                    <rect x="50" y="170" width="150" height="50" rx="5" fill="var(--light-blue)" stroke="#3498db" />
                    <text x="125" y="200" text-anchor="middle" fill="white">回应 (25%)</text>
                    <rect x="200" y="170" width="450" height="50" rx="0" fill="var(--light-yellow)" stroke="#f39c12" />
                    <text x="425" y="200" text-anchor="middle">引导 (75%)</text>
                </svg>
            </div>
        </section>
        
        <section id="decision-model">
            <h2>轻量级决策模型实现</h2>
            <div class="highlight">
                <p>在严格的1500ms时间限制内，ASR处理(~300ms)、意图识别(~600ms)后，仅剩约600ms用于策略分析和回复生成。我们通过预计算、状态编码和并行处理，在保证功能完整性的同时极大提高实时响应能力。</p>
            </div>
            
            <div class="svg-container">
                <svg width="800" height="500" viewBox="0 0 800 500">
                    <rect x="100" y="30" width="600" height="440" rx="8" fill="#f5f5f5" stroke="#2c3e50" stroke-width="2" />
                    <text x="400" y="60" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">轻量级决策模型核心设计</text>
                    
                    <rect x="130" y="80" width="540" height="240" rx="5" fill="white" stroke="#3498db" />
                    <text x="400" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">状态空间离散化与编码</text>
                    
                    <text x="150" y="140" font-size="14">• 客户状态向量化: [情绪|合作度|抗拒级别|...]</text>
                    <text x="170" y="165" font-size="14">→ 135种组合压缩为7位二进制(0-127)</text>
                    
                    <text x="150" y="200" font-size="14">• 对话阶段位图: [阶段1|阶段2|阶段3|阶段4]</text>
                    <text x="170" y="225" font-size="14">例: 1010表示完成阶段1和阶段3</text>
                    
                    <text x="150" y="260" font-size="14">• 决策表[客户状态][对话阶段][客户意图] = {</text>
                    <text x="170" y="285" font-size="14">"策略调整": 0或1,</text>
                    <text x="170" y="310" font-size="14">"话术模板ID": 整数,</text>
                    <text x="170" y="335" font-size="14">"引导强度": 0-2,</text>
                    <text x="170" y="360" font-size="14">"下一目标": "获取还款日期"</text>
                    <text x="150" y="385" font-size="14">}</text>
                    
                    <rect x="130" y="340" width="540" height="110" rx="5" fill="white" stroke="#3498db" />
                    <text x="400" y="365" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">并行处理与预测</text>
                    
                    <text x="150" y="395" font-size="14">ASR结果 → 预测Top-3意图 → 并行准备3套回复</text>
                    <text x="150" y="425" font-size="14">• 意图识别的同时进行状态编码</text>
                    <text x="150" y="450" font-size="14">• 最终意图确认后立即返回预先准备的回复</text>
                </svg>
            </div>
            
            <h3>状态编码示例</h3>
            <div class="svg-container">
                <svg width="700" height="200" viewBox="0 0 700 200">
                    <rect x="50" y="30" width="600" height="150" rx="5" fill="#f5f5f5" stroke="#ddd" />
                    
                    <!-- 0-2 位: 情绪状态 -->
                    <rect x="70" y="50" width="180" height="30" rx="3" fill="var(--light-red)" stroke="#e74c3c" />
                    <text x="160" y="70" text-anchor="middle" font-size="12">0-2位: 情绪状态 (0-4)</text>
                    
                    <rect x="70" y="85" width="120" height="20" rx="3" fill="white" stroke="#ddd" />
                    <text x="130" y="100" text-anchor="middle" font-size="10">000: 平静</text>
                    
                    <rect x="195" y="85" width="120" height="20" rx="3" fill="white" stroke="#ddd" />
                    <text x="255" y="100" text-anchor="middle" font-size="10">001: 积极</text>
                    
                    <rect x="70" y="110" width="120" height="20" rx="3" fill="white" stroke="#ddd" />
                    <text x="130" y="125" text-anchor="middle" font-size="10">010: 焦虑</text>
                    
                    <rect x="195" y="110" width="120" height="20" rx="3" fill="white" stroke="#ddd" />
                    <text x="255" y="125" text-anchor="middle" font-size="10">011: 愤怒</text>
                    
                    <rect x="70" y="135" width="120" height="20" rx="3" fill="white" stroke="#ddd" />
                    <text x="130" y="150" text-anchor="middle" font-size="10">100: 沮丧</text>
                    
                    <!-- 3-4 位: 合作度 -->
                    <rect x="350" y="50" width="160" height="30" rx="3" fill="var(--light-green)" stroke="#27ae60" />
                    <text x="430" y="70" text-anchor="middle" font-size="12">3-4位: 合作度 (0-2)</text>
                    
                    <rect x="350" y="85" width="100" height="20" rx="3" fill="white" stroke="#ddd" />
                    <text x="400" y="100" text-anchor="middle" font-size="10">00: 低</text>
                    
                    <rect x="350" y="110" width="100" height="20" rx="3" fill="white" stroke="#ddd" />
                    <text x="400" y="125" text-anchor="middle" font-size="10">01: 中</text>
                    
                    <rect x="350" y="135" width="100" height="20" rx="3" fill="white" stroke="#ddd" />
                    <text x="400" y="150" text-anchor="middle" font-size="10">10: 高</text>
                    
                    <!-- 5-6 位: 抗拒级别 -->
                    <rect x="520" y="50" width="160" height="30" rx="3" fill="var(--light-yellow)" stroke="#f39c12" />
                    <text x="600" y="70" text-anchor="middle" font-size="12">5-6位: 抗拒级别 (0-2)</text>
                    
                    <rect x="520" y="85" width="100" height="20" rx="3" fill="white" stroke="#ddd" />
                    <text x="570" y="100" text-anchor="middle" font-size="10">00: 低</text>
                    
                    <rect x="520" y="110" width="100" height="20" rx="3" fill="white" stroke="#ddd" />
                    <text x="570" y="125" text-anchor="middle" font-size="10">01: 中</text>
                    
                    <rect x="520" y="135" width="100" height="20" rx="3" fill="white" stroke="#ddd" />
                    <text x="570" y="150" text-anchor="middle" font-size="10">10: 高</text>
                </svg>
            </div>
        </section>
        
        <section id="comparison">
            <h2>与传统意图-话术系统的本质区别</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>特性</th>
                        <th>传统系统</th>
                        <th>新系统</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>决策机制</td>
                        <td>单一维度决策（仅基于当前意图）</td>
                        <td>多层次决策（策略、流程、话术）</td>
                    </tr>
                    <tr>
                        <td>对话规划</td>
                        <td>单一回合响应</td>
                        <td>整体对话规划</td>
                    </tr>
                    <tr>
                        <td>主动性</td>
                        <td>被动回应客户输入</td>
                        <td>主动引导对话方向</td>
                    </tr>
                    <tr>
                        <td>目标导向</td>
                        <td>回应当前意图为主</td>
                        <td>以实现催收目标为核心</td>
                    </tr>
                    <tr>
                        <td>上下文利用</td>
                        <td>有限的上下文理解</td>
                        <td>维持对话历史并据此调整策略</td>
                    </tr>
                </tbody>
            </table>
        </section>
        
        <section id="conversation-sample">
            <h2>对话样例展示</h2>
            <div class="tabs">
                <div class="tab active" data-tab="tab1">柔性催收</div>
                <div class="tab" data-tab="tab2">强硬催收</div>
            </div>
            
            <div class="tab-content active" id="tab1">
                <h4>场景：首次逾期客户</h4>
                <div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin-top: 10px;">
                    <p><strong>系统</strong> (开场阶段)：您好，我是XX金融的客服小智，打电话是想提醒您，您的账单已经逾期了几天，请问您是否方便安排还款？</p>
                    <p><strong>客户</strong>：我最近资金周转有点困难，能不能再宽限几天？</p>
                    <p><strong>系统</strong> (解决阶段 - 回应+引导)：<span style="color: #3498db;">我理解您目前的资金困难</span>，<span style="color: #f39c12;">但逾期可能会影响您的信用记录。我们可以制定一个适合您的还款计划，您预计什么时候能够安排还款呢？</span></p>
                    <p><strong>客户</strong>：下周五应该能收到工资。</p>
                    <p><strong>系统</strong> (解决阶段 - 回应+引导)：<span style="color: #3498db;">下周五是个不错的安排</span>，<span style="color: #f39c12;">为了避免您忘记，我们可以先确认具体的还款金额和方式。您打算还清全部欠款还是先部分还款呢？</span></p>
                </div>
            </div>
            
            <div class="tab-content" id="tab2">
                <h4>场景：多次逾期且有失联历史的客户</h4>
                <div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin-top: 10px;">

---

_**User**_

继续

---

_**Assistant**_



---

                    <p><strong>系统</strong> (开场阶段)：您好，我是XX金融催收部门的智能助手，您的贷款已经逾期30天，这是严重的违约行为，我们需要立即处理这个问题。</p>
                    <p><strong>客户</strong>：我现在真的没钱，你们别再打电话来了。</p>
                    <p><strong>系统</strong> (说明阶段 - 回应+引导)：<span style="color: #3498db;">我理解您当前的经济困难</span>，<span style="color: #f39c12;">但逾期不还会导致严重后果，包括信用受损、额外滞纳金和可能的法律程序。我们需要确定一个解决方案，您能否至少先支付最低还款额？</span></p>
                    <p><strong>客户</strong>：我该拿什么还？我现在连工作都没有！</p>
                    <p><strong>系统</strong> (解决阶段 - 回应+引导)：<span style="color: #3498db;">我很遗憾听到您的就业状况</span>，<span style="color: #f39c12;">但我们可以根据您的实际情况制定分期还款计划，减轻您的短期压力。另外，不处理逾期会影响您今后的贷款能力。您能告诉我您什么时候可能有收入来源吗？</span></p>
                </div>
            </div>
        

---



---

