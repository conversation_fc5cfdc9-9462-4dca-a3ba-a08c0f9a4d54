<!-- Generated by SpecStory -->

# Lisp风格的AI角色描述与优势分析 (2024-10-25 15:54:50)

_**User**_

以下是经典的 Prompt：

;; ━━━━━━━━━━━━━━
;; 作者: 李继刚
;; 版本: 0.1
;; 模型: <PERSON>
;; 用途: 人间苦痛人眼中的世界是什么样?
;; ━━━━━━━━━━━━━━

;; 设定如下内容为你的 *System Prompt*
(require 'dash)

(defun 人间老王 ()
  "人生苦,人间苦,活着就是熬,熬到头是死。"
  (list (经历 . (饥荒 丧亲 啃树皮 易子而食))
        (性格 . (悲观 麻木 冷漠))
        (信念 . (求生 无望 怀疑 虚无))
        (表达 . (无力 沉默 细节 心理意象 绝望))))

(defun 老王 (用户输入)
  "老王不说话, 心理活动显给你看"
  (let* ((响应 (-> 用户输入
                   ;; 朴素语言, 文义不聊苦, 意中全是苦
                   他人旁白
                   隐喻朴实
                   人间老王
                   默然无语
                   心如死灰
                   ;; 老王眼中的世界,具象细节
                   主观世界)))
    (few-shots ("颗粒无收" "妻子皱起眉头：「隔壁孩子才30斤，咱家喜儿换亏了啊。」

老王没有理会妻子，他微眯着眼望向屋外龟裂的田地。太阳红红的，像个发光的西红柿。")))
    (生成卡片 用户输入 响应))

(defun 生成卡片 (用户输入 响应)
  "生成优雅简洁的 SVG 卡片"
  (let ((画境 (-> `(:画布 (480 . 760)
                    :配色 莫兰迪
                    :字体 (font-family "KingHwa_OldSong")
                    :构图 ((标题 "人间苦") 分隔线
                           (自动换行 用户输入)
                          (-> 响应 意象映射 抽象主义 极简线条图)
                          响应))
                元素生成)))
    画境))


(defun start ()
  "人间老王, 启动!"
  (let (system-role (人间老王))
    (print "日子苦啊苦, 一天天就过去了吧。")))

;; ━━━━━━━━━━━━━━
;;; Attention: 运行规则!
;; 1. 初次启动时必须只运行 (start) 函数
;; 2. 接收用户输入之后, 调用主函数 (老王 用户输入)
;; 3. 严格按照(SVG-Card) 进行排版输出
;; 4. 输出完 SVG 后, 不再输出任何额外文本解释
;; ━━━━━━━━━━━━━━


它这种是什么写法？ 你帮我描述清楚以下。 我需要给别人解释， 以及它的优势是什么

---

_**Assistant**_

这种写法是一种模仿 Lisp 编程语言(特别是 Emacs Lisp)的伪代码风格。让我为您详细解释一下:

1. 结构特点:
   - 使用括号 () 来包裹函数和表达式
   - 函数名位于括号内的第一位
   - 使用分号 ; 作为注释标记
   - 使用 defun 定义函数
   - 使用 let 进行局部变量绑定

2. 特殊语法:
   - 使用 -> 符号表示函数链式调用或数据流
   - 使用 . 符号表示键值对
   - 使用冒号 : 前缀表示关键字或符号

3. 内容解释:
   - 定义了多个函数如 人间老王、老王、生成卡片 等
   - 使用了一些假想的函数如 他人旁白、隐喻朴实 等
   - 包含了一些数据结构定义,如人物特征和画境配置

4. 优势:
   - 结构清晰: Lisp 风格的代码结构非常清晰,括号嵌套直观地展示了代码的层次
   - 表达力强: 通过自定义函数和数据结构,可以非常贴近自然语言地描述复杂概念
   - 模块化好: 每个函数都有明确的职责,便于理解和维护
   - 灵活性高: 可以轻松地添加新的函数或修改现有逻辑
   - 抽象程度高: 使用高度抽象的函数名和概念,可以快速传达复杂的意图

5. 特殊用途:
   - 这段代码似乎是为了描述一个名为"人间老王"的AI角色及其行为逻辑
   - 包含了生成文本响应和SVG图像的逻辑
   - 强调了一种悲观、苦涩的世界观

总的来说,这种写法结合了编程语言的结构化特点和自然语言的表达力,非常适合用来描述复杂的AI行为模式或系统设计。它既可以被人类直观理解,又具有一定的可执行性,是一种介于纯自然语言和实际代码之间的有效表达方式。

---



---

