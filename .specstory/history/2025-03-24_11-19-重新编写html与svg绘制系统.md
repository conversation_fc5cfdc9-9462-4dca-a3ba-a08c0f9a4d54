<!-- Generated by SpecStory -->

# 重新编写HTML与SVG绘制系统 (2025-03-24 19:19:42)

_**User**_

基于当前系统， 我希望你重新写一个 html。 里面就用 SVG 画一个这个系统的完完整整的html 图， 或者 canvas 都行

---

_**Assistant**_



---

# AI催收系统图解

以下是基于您提供的文档，使用SVG重新设计的AI催收系统图解：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI催收系统架构图</title>
  <style>
    :root {
      --primary-color: #2c3e50;
      --secondary-color: #3498db;
      --accent-color: #e74c3c;
      --success-color: #27ae60;
      --warning-color: #f39c12;
      --light-blue: #d6eaf8;
      --light-green: #d5f5e3;
      --light-red: #fadbd8;
      --light-yellow: #fef9e7;
      --border-color: #ddd;
      --bg-color: #f9f9f9;
      --text-color: #333;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
      line-height: 1.6;
      color: var(--text-color);
      background-color: var(--bg-color);
      padding: 20px;
    }
    
    h1 {
      color: var(--primary-color);
      text-align: center;
      margin-bottom: 20px;
    }
    
    .system-container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    svg {
      display: block;
      margin: 0 auto;
    }
    
    .legend {
      display: flex;
      justify-content: center;
      margin-top: 20px;
      flex-wrap: wrap;
      gap: 15px;
    }
    
    .legend-item {
      display: flex;
      align-items: center;
      margin-right: 15px;
    }
    
    .legend-color {
      width: 20px;
      height: 20px;
      margin-right: 5px;
      border-radius: 3px;
    }
    
    footer {
      text-align: center;
      margin-top: 30px;
      color: var(--primary-color);
      font-size: 14px;
    }
    
    @media (max-width: 768px) {
      .system-container {
        padding: 10px;
      }
      svg {
        width: 100%;
        height: auto;
      }
    }
  </style>
</head>
<body>
  <h1>AI催收系统架构图</h1>
  
  <div class="system-container">
    <svg width="1100" height="1800" viewBox="0 0 1100 1800">
      <!-- 背景和标题区域 -->
      <rect x="0" y="0" width="1100" height="1800" fill="#f8f9fa" rx="10" ry="10" />
      <rect x="50" y="20" width="1000" height="80" rx="10" ry="10" fill="#2c3e50" />
      <text x="550" y="70" text-anchor="middle" fill="white" font-size="30" font-weight="bold">AI催收系统 - 从"意图-回应"到"策略-流程-话术"的智能升级</text>
      
      <!-- 第一部分：系统架构概览 -->
      <rect x="50" y="120" width="1000" height="400" rx="10" ry="10" fill="white" stroke="#ddd" />
      <text x="550" y="150" text-anchor="middle" font-size="24" font-weight="bold" fill="#2c3e50">系统三层架构</text>
      
      <!-- 策略层 -->
      <rect x="100" y="180" width="900" height="100" rx="5" fill="url(#grad-strategy)" />
      <text x="550" y="230" text-anchor="middle" font-size="22" fill="white">策略层 (Strategy)</text>
      <rect x="150" y="240" width="375" height="30" rx="5" fill="white" stroke="#e74c3c" />
      <text x="337.5" y="260" text-anchor="middle" font-size="16">柔性催收策略</text>
      <rect x="575" y="240" width="375" height="30" rx="5" fill="white" stroke="#e74c3c" />
      <text x="762.5" y="260" text-anchor="middle" font-size="16">强硬催收策略</text>
      
      <!-- 连接箭头 -->
      <line x1="550" y1="280" x2="550" y2="310" stroke="#333" stroke-width="2" />
      <polygon points="550,310 545,300 555,300" fill="#333" />
      
      <!-- 流程层 -->
      <rect x="100" y="310" width="900" height="100" rx="5" fill="url(#grad-process)" />
      <text x="550" y="360" text-anchor="middle" font-size="22" fill="white">流程层 (Process)</text>
      
      <rect x="150" y="370" width="170" height="30" rx="5" fill="white" stroke="#27ae60" />
      <text x="235" y="390" text-anchor="middle" font-size="16">开场</text>
      
      <rect x="360" y="370" width="170" height="30" rx="5" fill="white" stroke="#27ae60" />
      <text x="445" y="390" text-anchor="middle" font-size="16">说明</text>
      
      <rect x="570" y="370" width="170" height="30" rx="5" fill="white" stroke="#27ae60" />
      <text x="655" y="390" text-anchor="middle" font-size="16">解

---



---

