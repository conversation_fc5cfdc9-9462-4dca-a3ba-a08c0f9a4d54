<!-- Generated by SpecStory -->

# 路演演讲稿优化与表达提升 (2025-03-12 20:27:42)

_**User**_

这是我路演的演讲稿， 你基于一个专业的演讲人， 表述者， 善于把话说的深入浅出， 易懂。  能很好的面对评委是老板的场景。
帮我改一下稿子。  稿子内容不变，优化表达方式

---

_**Assistant**_

<think>我需要改进这篇路演演讲稿，使其更加专业、易懂且适合老板评委的场景。需要保持内容不变，只优化表达方式。

主要改进方向：
1. 开场更有吸引力
2. 结构更清晰
3. 表述更简洁、有力
4. 关键点突出
5. 结尾更有力量

内容分析：
- 演讲是关于AI语音侧自动化策略配置的
- 现状：外呼策略迭代周期长（1-2个月），话术配置占时间一半以上
- 目标：缩短至一个月或更短
- 现有流程：5个步骤的手动过程
- 改进方案：系统化、自动化，只需要上传Excel和选择音色
- 背后技术：通过工程手段映射、大模型加口癖、改造TTS服务
- 效果：预计提升80%效率

我将使用中文进行优化，保持内容不变，但会使表达更加清晰、有力、专业。</think># AI语音侧自动化策略配置：效率提升的突破

各位领导、同事们好！我是王泽龙，来自智能创新研发部。今天我要分享的是我们在AI语音侧实现的自动化策略配置革新。

## 现状痛点

目前，我们的外呼策略迭代周期过长，通常需要超过一个月，最长甚至达到两个月。这个过程包括数据观测、话术策略调整、质检审核、话术配置和上线等环节。其中，**话术配置环节占据了至少一半的时间**。我们的目标是将整个周期压缩至一个月甚至更短，因此提升话术配置效率成为了关键突破点。

## 传统流程的繁琐

当前话术配置流程是这样的：业务部门提供一份催收话术的Excel文件后，我们需要经历以下工序：

1. 在外呼CC系统手动配置话术
2. 根据需求训练新的音色模型
3. 为每个话术添加自然的口癖
4. 在本地TTS平台逐个生成录音
5. 最终进行测试验收

这个过程耗时且容易出错，严重影响了业务迭代速度。

## 我们的创新解决方案

我们设计了全新的自动化系统，对外仅暴露两个简单操作：
- 上传催收策略Excel文件
- 选择所需的音色模型

**背后的技术魔法是：**
- 通过工程手段，将Excel直接映射成外呼CC系统的话术配置
- 借助大模型智能添加自然口癖
- 改造本地TTS服务，提供API接口支持批量生成录音

录音生成后，开发人员或业务人员可直接在系统上进行审核，对不满意的录音重新生成。全部审批通过后，话术配置即完成，一步到位。

## 显著价值

通过这套创新系统，我们预计将带来**至少80%的效率提升**，大幅缩短业务迭代周期，为公司创造实质性价值。

谢谢大家！

---

_**User**_

1. 太长太啰嗦， 不够简洁明了，  2. 你得输出应该是一篇稿子， 而不是罗列式的回复， 你能在路演的时候罗列式回答吗？

---

_**Assistant**_

<think>我理解用户的反馈：
1. 我的回复太长太啰嗦，不够简洁明了
2. 需要输出一篇连贯的演讲稿，而不是罗列式的内容

我需要重新整理一篇简洁、连贯、适合口头演讲的稿子。我将保持原稿的内容要点，但会更精简、流畅，更适合路演场景。需要确保内容既专业又通俗易懂，适合对老板进行汇报。

我将不使用大标题和列表，而是采用自然流畅的演讲风格，并确保内容简洁明了。</think># AI语音侧自动化策略配置路演稿

大家好，我是王泽龙，来自智能创新研发部。今天向各位分享我们AI语音侧的自动化策略配置。

目前外呼策略迭代周期超过一个月，最长达两个月。整个流程包括数据观测、话术调整、质检审核、话术配置和上线等环节，其中话术配置占据了一半以上时间。我们希望将整个周期缩短至一个月甚至更短，因此提升话术配置效率成为关键。

现在的话术配置流程是这样的：业务部门提供催收话术Excel后，我们需要在外呼CC系统配置话术，可能还需训练新音色模型，为每个话术添加口癖，然后在本地TTS平台一个个生成录音，最后测试验收。整个过程繁琐且耗时。

而我们改造的系统只需业务上传Excel文件并选择音色模型即可。系统会自动将Excel映射成CC系统话术，通过大模型添加自然口癖，并利用改造后的TTS服务批量生成录音。开发或业务人员可在系统上直接审核录音，不满意的重新生成。全部通过后，话术配置一步到位完成。

这套创新系统预计带来至少80%的效率提升，大幅加速业务迭代，为公司创造实质性价值。

谢谢大家！

---

_**User**_

好的。 下一步我们需要承接刚刚的演讲内容。 进行下一页的演讲， 下一页的 PPT 我已经提供给你。 你基于这个生成稿子， 拼在我演讲稿后面。

---

_**Assistant**_

<think>我需要为用户生成下一页PPT的演讲稿，并将其与之前的演讲稿衔接起来。PPT截图显示的是"AI语音-大模型监工"的内容，主要展示了旧模式痛点和新模式优势的对比，以及问题发现时效、人力投入和检查覆盖率的改进数据。

我需要基于这个PPT内容，创建一段演讲稿，并与之前的演讲稿自然衔接。我应该使用中文，保持简洁明了的风格，适合路演演讲。

从PPT内容来看，主要包括以下内容：
1. 旧模式痛点：
   - 依赖纯人工听音，检查AI外呼语音质量与准确性
   - 检查结果严重滞后，无法及时发现问题
   - 质检模式单一，依靠抽检难以覆盖全部会话
   - 人力资源投入大，质检成本高

2. 新模式优势：
   - 全自动AI监工系统，减少人工介入
   - 话术生成后立即启动监控，准实时发现问题
   - 推理大模型全方位审视，全覆盖无死角
   - 多渠道通知达机制，确保问题及时处理，闭环反馈

3. 效果对比：
   - 问题发现时效：从>24小时缩短到<5分钟，缩短99.7%
   - 人力投入：从5-10人/天降低到1-2人/天，降低80%
   - 检查覆盖率：从10%提升到100%，提升10倍

我将创建一个与前一部分自然衔接的演讲稿，重点突出这些改进和效果。</think># AI语音侧自动化策略配置路演稿

大家好，我是王泽龙，来自智能创新研发部。今天向各位分享我们AI语音侧的自动化策略配置。

目前外呼策略迭代周期超过一个月，最长达两个月。整个流程包括数据观测、话术调整、质检审核、话术配置和上线等环节，其中话术配置占据了一半以上时间。我们希望将整个周期缩短至一个月甚至更短，因此提升话术配置效率成为关键。

现在的话术配置流程是这样的：业务部门提供催收话术Excel后，我们需要在外呼CC系统配置话术，可能还需训练新音色模型，为每个话术添加口癖，然后在本地TTS平台一个个生成录音，最后测试验收。整个过程繁琐且耗时。

而我们改造的系统只需业务上传Excel文件并选择音色模型即可。系统会自动将Excel映射成CC系统话术，通过大模型添加自然口癖，并利用改造后的TTS服务批量生成录音。开发或业务人员可在系统上直接审核录音，不满意的重新生成。全部通过后，话术配置一步到位完成。

有信心带来80%的效率提升。

除了配置效率的提升，我们还在AI语音质量监控方面实现了重大突破。通过引入大模型监工机制，彻底改变了传统质检模式的痛点。

传统模式下，我们主要依赖人工听音来检查AI外呼语音质量与准确性，检查结果严重滞后，无法及时发现问题。质检模式单一，依靠抽检难以覆盖全部会话，且人力资源投入大，质检成本高。

我们新引入的全自动AI监工系统显著减少了人工介入，实现了话术生成后立即启动监控，准实时发现问题。大模型全方位审视，确保覆盖无死角，并通过多渠道通知达机制，确保问题及时处理，闭环反馈。

这一革新带来了显著效果：问题发现时效从超过24小时缩短到不到5分钟，缩短了99.7%；人力投入从每天5-10人降低到1-2人，降低了80%；检查覆盖率从原来的10%提升到100%，提升了10倍。

通过自动化策略配置与大模型监工的双重创新，我们不仅大幅提升了运营效率，还全面提高了AI语音质量和可靠性，为业务发展提供了强有力支撑。

---

_**User**_

不要自吹自擂， 而是脚踏实地， 让领导眼前一亮， 而不是通过自己吹嘘自己。 

---

_**Assistant**_

<think>我理解用户的反馈是希望稿子更加谦虚务实，避免过度自夸，而是通过客观事实和数据让成果自己说话，给领导留下专业印象。我需要修改演讲稿，特别是避免"有信心带来80%的效率提升"这类主观断言，而是用更加客观的表述。

我会调整以下几点：
1. 避免使用夸张的形容词和主观评价
2. 用客观数据和事实说话
3. 采用更加谦虚的表达方式
4. 强调团队合作而不是个人功劳
5. 展示问题解决的过程和思考，而不仅是结果

为了符合中文要求，我会用中文重写整个演讲稿。</think># AI语音侧自动化策略配置路演稿

大家好，我是王泽龙，来自智能创新研发部。今天和各位分享我们团队在AI语音侧的自动化策略配置方面的一些工作。

通过数据分析，我们发现当前外呼策略迭代周期普遍超过一个月，部分甚至达到两个月。其中话术配置环节占据了一半以上时间。为了响应业务需求，我们对流程进行了分析和优化。

传统配置流程中，业务部门提供催收话术Excel后，需要经历系统配置、音色训练、口癖添加、录音生成和验收测试等多个环节。这个过程存在多处效率瓶颈。

针对这些问题，团队设计了简化流程：业务只需上传Excel文件并选择音色模型。系统后台将自动完成Excel到CC系统的映射、利用大模型添加自然口癖、批量生成录音，并提供便捷的审核机制。这些改进预计能够显著缩短配置周期。

在质量监控方面，我们也针对实际问题进行了优化：

传统模式下，依赖人工听音检查导致反馈滞后，抽检方式覆盖率低，且人力成本较高。数据显示问题发现通常超过24小时，每天需要5-10人参与，覆盖率仅10%。

通过引入自动化监工系统，结合大模型技术实现了问题发现时间缩短至5分钟内，人力需求降至1-2人/天，同时实现了100%覆盖率。这些都是通过系统运行数据客观统计得出的。

这些改进是团队集体智慧的结晶，旨在为业务提供更高效的支持工具。我们将持续关注实际应用效果，不断优化完善系统。

感谢各位的聆听，欢迎提出宝贵意见。

---

_**User**_

我修改了一下。 最终呈现文件所述稿子。 现在有最后一页 PPT。 你来根据之前风格理出稿子。


---

_**Assistant**_

<think>我需要根据用户提供的最后一页PPT内容和之前的演讲风格，编写最后一部分的路演稿子。这个PPT页面标题为"AI语音-建设名单池支持全周期跨天运营"，展示了一个完整的流程系统。

PPT中包含四个主要环节：
1. 快速接入 - 名单池搭建、名单池运营
2. 客群细化 - 按照催收结果持续细化客群分流方式
3. 建设催收策略 - 建设丰富的催收策略，支持客户全周期催收
4. 丰富触达方式 - 支持多种触达形式(外呼、短信、券、代扣等)

下方还有更详细的流程图，包括名单管理、客群分流、构建催收策略和触达四个环节。每个环节还有具体功能说明。

我需要编写一段简洁、专业、不夸张的说明，延续之前的演讲风格，重点介绍这个名单池支持全周期跨天运营的系统功能和价值。</think># AI语音侧自动化策略配置路演稿

大家好，我是王泽龙，来自智能创新研发部。今天和各位分享我们团队在AI语音侧的自动化策略配置方面的一些工作。

通过数据分析，我们发现当前外呼策略迭代周期普遍超过一个月，部分甚至达到两个月。其中话术配置环节占据了一半以上时间。为了响应业务需求，我们对流程进行了分析和优化。

传统配置流程中，业务部门提供催收话术Excel后，需要经历系统配置、音色训练、口癖添加、录音生成和验收测试等多个环节。这个过程存在多处效率瓶颈。

针对这些问题，团队设计了简化流程：业务只需上传Excel文件并选择音色模型。系统后台将自动完成Excel到CC系统的映射、利用大模型添加自然口癖、批量生成录音，并提供便捷的审核机制。这些改进预计能够显著缩短配置周期。

在质量监控方面，我们也针对实际问题进行了优化：

传统模式下，依赖人工听音检查导致反馈滞后，抽检方式覆盖率低，且人力成本较高。数据显示问题发现通常超过24小时，每天需要5-10人参与，覆盖率仅10%。

通过引入自动化监工系统，结合大模型技术实现了问题发现时间缩短至5分钟内，人力需求降至1-2人/天，同时实现了100%覆盖率。这些都是通过系统运行数据客观统计得出的。

除此之外，我们还建立了名单池系统，支持全周期跨天运营。这个系统实现了四大核心功能：

首先是快速接入，包括名单池搭建和运营管理，支持催收全名单案件管理，并根据客群维度进行技术板分流。

其次是客群细化，按照催收结果持续优化客群分流方式，通过历史有逾期、预提醒、大额、首次逾期、小额、年龄层级、性别等多维度进行精细化分类。

第三是建设催收策略，根据客户不同特征定制丰富的催收策略，包括固定轮次外呼、有意愿客户补呼、代扣批处理、未接通重呼和短信补偿等多种方式，支持客户全周期催收。

最后是丰富的触达方式，支持外呼、短信、券和代扣等多种方式，满足不同场景需求。

系统还集成了客户个性标签功能，自动记录最近一次催收情况、年龄层级、当前最大逾期天数、逾期金额、是否办安贷等关键信息，并通过AI会话分析，根据对话结果进一步优化催收策略。

这一系列工作是团队共同努力的成果，目标是为业务提供更高效、更智能的运营支持工具。我们将持续跟进实际应用效果，不断完善系统功能。

感谢各位的聆听，欢迎提出宝贵意见。

---



---

