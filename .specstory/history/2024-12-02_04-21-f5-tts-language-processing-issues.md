<!-- Generated by SpecStory -->

# F5-TTS Language Processing Issues (2024-12-02 12:21:43)

_**User**_

@https://github.com/SWivid/F5-TTS/issues/263 

帮我看一下这个 issues 在讲什么

---

_**Assistant**_

这个 GitHub issue 主要讨论了在使用 F5-TTS 模型进行非中文语言训练时的文本预处理问题。主要内容如下：

1. 原始问题：
- 用户询问在训练其他语言(如越南语、西班牙语、老挝语)时，是否需要修改 `convert_char_to_pinyin` 函数，因为这个函数原本是为中文设计的。

2. 主要讨论：
- 一位越南语用户分享了他的经验：
  - 他修改了 `convert_char_to_pinyin` 函数，改为只将字符串转换为字符数组
  - 在处理越南语时需要在 vocab.txt 中添加缺失的越南语字符
  - 使用约 300 小时的数据集进行微调，模型可以说出可理解的越南语，但仍存在重复词、缺词和词序错误的问题

3. 文本处理建议：
- 保留标点符号，因为这有助于模型学习语音中的停顿
- 保留大写字母，可以帮助模型学习语调重音
- 建议只对数字和特殊字符（如 %、&、$ 等）进行标准化处理
- 最好保留原始文本的备份

4. 粤语处理相关：
- 有用户询问粤语训练的处理方法
- 有人建议参考 GPT-SoVITS 项目的方法，使用 g2p (grapheme-to-phoneme) 函数将中文字转换为粤语拼音(Jyutping)

这个讨论主要强调了在处理不同语言时，需要根据具体语言特点来调整文本预处理方法，而不能直接使用为中文设计的拼音转换函数。

---

_**User**_

@Web 你帮我查一下。 F5TTS 如何解决数字会读成英文的问题。 比如 1 会读成 one

---

_**Assistant**_



根据搜索结果,我来分析一下 F5-TTS 处理数字的问题:

1. 目前 F5-TTS 在处理数字时确实存在问题,会将数字读成英文。这个问题在 [issues #362](https://github.com/SWivid/F5-TTS/issues/362) 中有提到,主要原因是:

- 当前模型使用了 Emilia 语料库训练,其中包含很多非英语母语者的语音
- 与使用有声书语料库(母语者和标准发音)训练的模型相比,如 VALL-E 系列、Voicebox、MS 的官方 E2 TTS 等,F5 仓库模型在这方面存在问题
- 由于当前基础模型使用字符而不是音素,可能更难学习正确的发音

2. 可能的解决方案:

- 在预处理文本时,可以先将数字转换为对应语言的文字表示
- 例如中文中的 "1" 转换为 "一"
- 英文中的 "1" 转换为 "one" 
- 其他语言也需要相应的数字到文字的转换规则

3. 参考其他项目的做法:

根据 [FCH-TTS](https://github.com/atomicoo/FCH-TTS) 的实现,在处理文本时:

- 使用 `normalized_text` 而不是 `raw_text`
- 因为 tokenizer 不支持数字,所以在 `normalized_text` 中数字会被写成文字形式
- 这样可以避免数字发音的问题

总结:目前这个问题仍在进行中(WIP),建议在使用 F5-TTS 时,先对输入文本进行预处理,将数字转换为对应语言的文字表示,以获得更好的发音效果。

---

_**User**_

那比如说。 342.52 这种数字怎么办呢？

---

_**Assistant**_



对于小数点形式的数字如 342.52，我们需要根据不同语言的读法习惯来转换。以中文为例，有以下几种处理方案：

1. 标准读法转换：
```python
def convert_decimal_to_chinese(num_str):
    # 将 342.52 转换为 "三百四十二点五二"
    integer_part = int(num_str.split('.')[0])
    decimal_part = num_str.split('.')[1]
    
    # 整数部分转中文
    cn_num = {
        '0': '零', '1': '一', '2': '二', '3': '三', '4': '四',
        '5': '五', '6': '六', '7': '七', '8': '八', '9': '九'
    }
    
    # 处理整数部分
    result = ''
    if integer_part >= 100:
        result += cn_num[str(integer_part//100)] + '百'
        if (integer_part % 100) >= 10:
            result += cn_num[str((integer_part%100)//10)] + '十'
        if integer_part % 10:
            result += cn_num[str(integer_part%10)]
    # ... 其他位数处理逻辑
    
    # 处理小数部分
    result += '点'
    for digit in decimal_part:
        result += cn_num[digit]
    
    return result
```

2. 不同场景的处理方式：
- 金额读法：`"三百四十二元五角二分"`
- 数值读法：`"三百四十二点五二"`
- 科学读法：`"三四二点五二"`（某些特定场景）

3. 建议的处理流程：
```python
def process_number(text, mode='standard'):
    import re
    
    def replace_number(match):
        num = match.group()
        if '.' in num:
            if mode == 'currency':
                # 金额处理
                return convert_to_currency(num)
            elif mode == 'scientific':
                # 科学读法处理
                return convert_to_scientific(num)
            else:
                # 标准读法处理
                return convert_decimal_to_chinese(num)
    
    # 使用正则表达式匹配数字（包括小数）
    pattern = r'\d+\.\d+|\d+'
    processed_text = re.sub(pattern, replace_number, text)
    return processed_text
```

4. 使用示例：
```python
text = "商品价格为342.52元"

# 标准读法
result1 = process_number(text)  # "商品价格为三百四十二点五二元"

# 金额读法
result2 = process_number(text, mode='currency')  # "商品价格为三百四十二元五角二分"
```

5. 注意事项：
- 需要根据上下文判断使用哪种读法
- 某些特殊场景（如电话号码、编号等）需要单独处理
- 不同语言有不同的数字读法规则，需要分别实现

建议在 F5-TTS 的文本预处理阶段加入这样的数字转换模块，确保数字能够按照正确的方式被转换和朗读。你可以根据具体需求选择合适的转换方式。

---

_**User**_

@Web 那多音字呢， F5TTS 如何处理， 也是用类似这种 hack 的方式嘛， 而不是直接对模型微调？

---

_**Assistant**_

根据搜索结果和相关讨论，我来分析一下 F5-TTS 处理多音字的方案：

1. 目前 F5-TTS 处理多音字主要有两种方式：

- 预处理文本方式：
```python
def convert_polyphone(text):
    # 常见多音字词典
    polyphone_dict = {
        "重庆": "chong qing",
        "银行": "yin hang",
        "行李": "xing li",
        "称重": "cheng zhong",
        # ... 更多多音字组合
    }
    
    # 先处理词组中的多音字
    for word, pinyin in polyphone_dict.items():
        if word in text:
            text = text.replace(word, f"[{pinyin}]")
            
    return text
```

2. 使用 G2P (Grapheme-to-Phoneme) 模型：
```python
from g2p_en import G2p
from pypinyin import pinyin, Style

def get_pronunciation(text):
    # 使用 pypinyin 处理中文
    if is_chinese(text):
        return pinyin(text, style=Style.TONE3)
    # 使用 g2p 处理英文
    else:
        g2p = G2p()
        return g2p(text)
```

3. 建议的最佳实践：

- 对于常见多音字，建立一个词典进行映射
- 使用上下文感知的 G2P 模型来预测发音
- 在预处理阶段就解决多音字问题，而不是依赖模型学习
- 保留原始文本作为备份，以便后续调试

4. 存在的局限：

- 词典方法需要手动维护，无法覆盖所有情况
- G2P 模型的准确率也不是 100%
- 某些罕见用法可能仍需要人工干预

总的来说，F5-TTS 目前主要采用预处理文本的方式来处理多音字问题，而不是通过模型微调。这是因为：

1. 预处理方案实现简单，易于维护和调试
2. 可以快速部署和更新规则
3. 不需要重新训练模型
4. 处理结果更可控

但从长远来看，如果要获得更好的效果，可能还是需要：

1. 训练专门的多音字预测模型
2. 在 TTS 模型中加入音素感知层
3. 使用更大的训练数据集来提升模型对多音字的理解

---

_**User**_

@https://github.com/SWivid/F5-TTS/blob/main/src/f5_tts/infer/SHARED.md 

这个是在说什么？ custom

---

_**Assistant**_

这是 F5-TTS 项目中的一个共享模型卡片（Shared Model Cards）文档，主要介绍了社区贡献的各种语言模型。让我为您总结主要内容：

1. 使用前提：
- 这是一个社区训练/微调结果的快速查询表
- 所有模型都是开源的，基于贡献者的自愿贡献
- 使用模型时必须尊重创作者

2. 目前支持的语言模型：

- 多语言模型：
  - F5-TTS Base（中文和英文预训练模型）
  - 使用了 Emilia 95K 中英文数据集
  - 模型许可证：cc-by-nc-4.0

- 日语模型：
  - 基于 F5-TTS Base 预训练/微调
  - 使用了 Emilia 1.7k 日语数据集和 Galgame 5.4k 数据集

- 法语模型：
  - 基于 LibriVox 数据集微调
  - 提供了在线推理和训练教程

3. 模型获取方式：
- 所有模型都托管在 Hugging Face 上
- 提供了模型检查点（checkpoint）和词汇表文件（vocab file）的下载链接

4. 分享要求：
- 可以分享预训练/微调的结果
- 需要在 Hugging Face 上托管模型
- 通过 Pull Request 添加模型信息到此文档

这个文档主要作为一个集中展示和共享不同语言 F5-TTS 模型的平台，方便用户查找和使用不同语言的模型。

---

_**User**_

@https://github.com/SWivid/F5-TTS/pull/503 

看看这个 pr

---

_**Assistant**_



这个 PR (Pull Request) 主要是关于解决 F5-TTS 中文本中数字处理的问题。让我为您分析主要内容：

1. PR 的主要目的：
- 改进文本标准化和处理流程
- 优化混合英文和中文文本转拼音的处理
- 增强文本兼容性并处理输入数据的边缘情况

2. 讨论要点：

- 作者 @boomyao 提出了一个将所有数字转换为中文文本的实现
- 项目所有者 @SWivid 提出了一个关键问题：
  - 如果遇到中英混合的数字（code-switching）情况该如何处理？
  
3. 解决方案建议：

@ZhikangNiu 提出了一个简单的处理方法：
```python
# 建议的处理流程
1. 先检测语言
2. 如果是中文，则使用数字转换函数
3. 建议在数据集准备阶段就完成这个处理
```

4. 主要关注点：
- 需要考虑语言检测
- 不能简单地将所有数字都转换为中文
- 应该在数据预处理阶段处理这个问题

这个 PR 目前还在讨论中，主要焦点是如何更智能地处理不同语言环境下的数字转换问题，而不是简单地将所有数字都转换为中文文本。

---



---

