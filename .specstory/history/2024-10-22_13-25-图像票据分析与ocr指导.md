<!-- Generated by SpecStory -->

# 图像票据分析与OCR指导 (2024-10-22 21:25:07)

_**User**_

请仔细观察并分析这张图像，输出提供给多模态模型进行 OCR 的指令、如实例所示：

1. 票据数量和重叠情况：
   - 首先判断图像中票据的排列方式（如并排、堆叠、部分重叠等）
   - 对于并排或轻微重叠的票据：
     a. 计算可识别的独立票据总数
     b. 描述每张票据的可见程度（如"完全可见"、"90%可见"等）
   - 对于严重重叠的票据：
     a. 确定最上层完全可见的票据数量（通常为1）
     b. 估计被遮挡的票据数量（如可能的话）
     c. 如果票据不是呈现明显的并排，而是重叠的话。 则被重叠的票据不纳入需要识别的票据。
   - 给出最终需要识别的票据数量

2. 票据类型：
   - 识别每张需要识别的票据的类型（如收银小票、病例、账单、消费明细等）

3. 关键信息区域：
   - 对每张需要识别的票据，标识以下信息的大致位置：
     a. 宠物信息（如有）
     b. 就诊日期
     c. 医疗机构名称
     d. 诊疗项目列表
     e. 用药信息列表
     f. 总金额
     g. 支付方式
     h. 医生/开单人姓名

4. 特殊情况：
   - 指出任何手写内容的位置
   - 标注可能影响OCR的因素（如模糊、褶皱、印章遮挡等）
   - 如有多个合计金额。 需在任务里强调出来（重要！！！）

请以简洁明了的方式描述上述分析结果，为后续OCR任务提供指导。

输出示例：
"图像包含3张票据，呈并排且轻微重叠状态。左侧和中间的票据完全可见，右侧票据约85%可见，所有3张都需要识别。票据类型均为动物医院收银小票。每张票据的关键信息分布类似：宠物信息在顶部，诊疗项目和用药信息在中部，总金额在底部。中间票据底部有手写备注。直接对3张票据分别进行OCR，重点关注中部的项目列表和底部的总金额信息。右侧票据的部分遮挡区域可能需要特别注意。"


这是我初步图像识别的 Prompt。 现在还不够好。 尤其是输出示例。
重叠的判断需要优化。 如果图像中出现严重重叠。 则只识别最上面那张有总计金额（实收金额）的票据。 这非常重要。

---

_**Assistant**_

非常感谢您提供的详细信息和要求。我理解您希望优化初步图像识别的Prompt，特别是在处理重叠票据和识别关键信息方面。我会根据您的要求调整Prompt，并提供一个更加精确的输出示例。以下是优化后的Prompt和输出示例：

图像分析Prompt：

请仔细分析此图像，并提供以下信息用于后续OCR任务：

1. 票据排列和重叠情况：
   - 描述票据的排列方式（并排、堆叠、重叠等）
   - 对于并排或轻微重叠的票据：
     a. 计算可完整识别的独立票据数量
     b. 描述每张票据的可见程度（例如"完全可见"、"90%可见"等）
   - 对于严重重叠的票据：
     a. 只关注最上层完全可见且包含总计金额（实收金额）

---

_**User**_

给我一个新的 Prompt

---

_**Assistant**_

好的，我会为您设计一个新的Prompt，以更好地满足您的需求。以下是优化后的Prompt：

```
请仔细分析此图像，并提供以下信息用于后续OCR任务：

1. 票据排列和重叠情况：
   - 描述票据的排列方式（并排、堆叠、重叠等）
   - 如果票据并排或轻微重叠：
     a. 计算可完整识别的独立票据数量
     b. 简要描述每张票据的可见程度
   - 如果票据严重重叠：
     a. 只关注最上层完全可见且包含总计金额（实收金额）的票据
     b. 说明其他票据将不进行识别

2. 需识别的票据数量：
   - 给出最终需要识别的票据数量

3. 每张需识别票据的信息：
   a. 票据类型（如收银小票、病例、账单等）
   b. 关键信息区域位置：
      - 宠物信息（如有）
      - 就诊日期
      - 医疗机构名称
      - 诊疗项目列表
      - 用药信息列表
      - 总金额/实收金额（重点标注位置）
      - 支付方式
      - 医生/开单人姓名
   c. 特殊情况：
      - 手写内容位置
      - 可能影响OCR的因素（如模糊、褶皱、印章遮挡等）
      - 多个合计金额（如有，需特别强调）

4. 建议的OCR顺序：
   - 根据票据的清晰度和重要性，建议OCR的处理顺序

请以简洁明了的方式描述上述分析结果，为后续OCR任务提供清晰指导。

输出示例：
"图像包含3张票据，呈严重重叠状态。仅最上层票据完全可见且包含总计金额，将只对此票据进行识别。需识别票据数量：1张。

票据信息：
1. 类型：动物医院收银小票
2. 关键信息区域：
   - 宠物信息：顶部左侧
   - 就诊日期：顶部右侧
   - 医疗机构：顶部中央
   - 诊疗项目列表：中部
   - 总金额：底部右侧（重点关注）
   - 支付方式：底部左侧
3. 特殊情况：
   - 手写签名位于底部
   - 右上角有印章轻微遮挡部分文字

建议OCR顺序：从上到下，重点关注中部项目列表和底部总金额信息。"
```

这个新的Prompt更加简洁明了，同时涵盖了您所强调的重要点，特别是关于严重重叠票据的处理方式。它还提供了一个更加详细和具体的输出示例，以便更好地指导后续的OCR任务。

---



---

