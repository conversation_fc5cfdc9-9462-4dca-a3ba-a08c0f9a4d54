<!-- Generated by SpecStory -->

# 关键词识别系统设置 (2025-04-02 15:15:36)

_**User**_

添加一类识别：

关键词：“友情提醒” 或者图里的关键词相关。 则输出Start

---

_**Assistant**_



---

我将为您添加新的识别类型，当文本包含"友情提醒"或图片中的相关关键词时，输出"Start"。

---

```diff
      # 非本人关键词列表
      not_self_keywords = ['不是', '不认', '错']
- 
+     # 提醒类关键词列表
+     reminder_keywords = ['友情提醒', '请勿透露身份及银行信息', '温馨提示', '来电启用隐私保护', '有疑问请通过官方渠道核实', '提示音']
+ 
+     # 检查文本是否包含任何提醒类关键词
+     for keyword in reminder_keywords:
+         if re.search(keyword, text):
+             return 'Start'
+             
      # 检查文本是否包含任何中断关键词
      for keyword in interrupt_keywords:
```

---

已经成功添加了新的识别类型。现在，当文本中包含"友情提醒"、"请勿透露身份及银行信息"、"温馨提示"、"来电启用隐私保护"、"有疑问请通过官方渠道核实"或"提示音"等关键词时，函数将返回"Start"。

我注意到代码中可能需要导入re模块，如果尚未导入，您可能需要添加。这个修改优先级高于其他识别类型，因此会先检查是否包含提醒类关键词。

---



---

