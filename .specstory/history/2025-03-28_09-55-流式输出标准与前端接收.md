<!-- Generated by SpecStory -->

# 流式输出标准与前端接收 (2025-03-28 17:55:37)

_**User**_

关于流式输出， 前端接收， 是不是有标准的协议？

---

_**Assistant**_



---

是的，流式输出有几种标准协议：

1. Server-Sent Events (SSE)：基于HTTP的单向通信协议，使用文本/事件流内容类型，适合从服务器到客户端的持续数据流。

2. WebSocket：提供全双工通信，适合需要双向实时数据交换的场景。

3. HTTP Chunked Transfer Encoding：HTTP/1.1的标准特性，允许服务器分块发送响应。

4. gRPC Streaming：支持单向和双向流式传输的RPC框架。

5. JSON streaming：通过逐行发送JSON对象或使用分隔符实现。

前端接收流式数据的实现取决于选择的协议，比如使用EventSource API接收SSE，使用WebSocket API处理WebSocket连接等。

---



---

