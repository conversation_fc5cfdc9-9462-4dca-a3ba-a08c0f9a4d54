<!-- Generated by SpecStory -->

# Python脚本转换MP3为WAV (2025-04-15 17:46:52)

_**User**_

写一个脚本把当前文件夹下的指定.mp3文件转成.wav

---

_**Assistant**_

你可以使用 Python 的 `pydub` 库来实现 mp3 转 wav。以下是一个简单的脚本，支持指定文件名进行转换，并使用清华源安装依赖。

1. 安装依赖（使用清华源）：
```bash
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple pydub
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple ffmpeg-python
```
你还需要安装 ffmpeg，可以用 Homebrew（macOS）：
```bash
brew install ffmpeg
```

2. 脚本内容如下（假设文件名为 `convert_mp3_to_wav.py`）：

```python
import os
from pydub import AudioSegment

def mp3_to_wav(mp3_filename):
    if not mp3_filename.endswith('.mp3'):
        print(f"文件 {mp3_filename} 不是 mp3 文件，跳过。")
        return
    wav_filename = mp3_filename[:-4] + '.wav'
    try:
        audio = AudioSegment.from_mp3(mp3_filename)
        audio.export(wav_filename, format="wav")
        print(f"已转换: {mp3_filename} -> {wav_filename}")
    except Exception as e:
        print(f"转换 {mp3_filename} 时出错: {e}")

if __name__ == "__main__":
    # 指定要转换的 mp3 文件名列表
    mp3_files = [
        "example1.mp3",
        "example2.mp3"
        # 在这里添加你要转换的 mp3 文件名
    ]
    for mp3_file in mp3_files:
        if os.path.exists(mp3_file):
            mp3_to_wav(mp3_file)
        else:
            print(f"文件不存在: {mp3_file}")
```

**使用方法：**
1. 把你要转换的 mp3 文件名填到 `mp3_files` 列表里。
2. 运行脚本即可。

如需批量转换当前文件夹下所有 mp3 文件，也可以告诉我，我可以帮你修改脚本。

---

_**User**_

fix

---

_**Assistant**_

我看到你遇到了 `ModuleNotFoundError: No module named 'pyaudioop'` 的错误。这是因为在 Python 3.13 中，`audioop` 模块被移除了，但 pydub 还依赖它。让我们修复这个问题。

---

Read file: 2wav.py

---

这个问题是因为你使用的是 Python 3.13，在这个版本中 `audioop` 模块已被移除，而 pydub 依赖这个模块。让我们使用另一种方法来解决这个问题，通过直接使用 ffmpeg 命令行工具来转换 mp3 文件。

---

```diff
  import os
- from pydub import AudioSegment
+ import subprocess
  
  def mp3_to_wav(mp3_filename):
      wav_filename = mp3_filename[:-4] + '.wav'
      try:
-         audio = AudioSegment.from_mp3(mp3_filename)
-         audio.export(wav_filename, format="wav")
+         # 使用 ffmpeg 命令行工具来转换
+         subprocess.run(['ffmpeg', '-i', mp3_filename, wav_filename], check=True)
          print(f"已转换: {mp3_filename} -> {wav_filename}")
-     except Exception as e:
+     except subprocess.CalledProcessError as e:
          print(f"转换 {mp3_filename} 时出错: {e}")
+     except FileNotFoundError:
+         print("错误: 找不到 ffmpeg。请确保已安装 ffmpeg 并添加到系统路径中。")
+         print("安装命令: brew install ffmpeg")
+         exit(1)
  
  if __name__ == "__main__":
```

---

我已经修复了脚本中的问题。现在脚本直接使用 `ffmpeg` 命令行工具来转换 mp3 文件，而不再依赖 pydub 库，避开了 Python 3.13 中 `audioop` 模块缺失的问题。

修改要点：
1. 移除了 pydub 依赖，改用 subprocess 模块调用 ffmpeg
2. 添加了 ffmpeg 不存在时的错误处理和安装提示
3. 使用更具体的异常处理

使用方法：
1. 确保你的系统上已安装 ffmpeg（如果没有，可以通过 `brew install ffmpeg` 安装）
2. 修改脚本中的 `mp3_files` 列表，添加你想要转换的 mp3 文件名
3. 运行脚本：`python 2wav.py`

如果你还没有安装 ffmpeg，请先运行：
```bash
brew install ffmpeg
```

这个解决方案不需要安装任何额外的 Python 包，只需要系统上有 ffmpeg 工具即可。

---



---

