<!-- Generated by SpecStory -->

# 查看 npm 源的指令和配置 (2024-10-10 15:58:57)

_**User**_

查看 npm 源的指令

---

_**Assistant**_

以下是查看 npm 源的指令：

```bash
npm config get registry
```

这个命令会显示当前 npm 正在使用的包注册表（registry）地址。

如果你想查看所有配置的 npm 源，可以使用以下命令：

```bash
npm config list
```

这将列出所有的 npm 配置，包括已设置的源地址。

如果你只想查看与 registry 相关的配置，可以使用：

```bash
npm config ls -l | grep registry
```

这会过滤出所有包含 "registry" 的配置项。

记住，默认的 npm 源是 https://registry.npmjs.org/。如果你看到的是这个地址，那么你正在使用官方的 npm 源。

---

_**User**_

设置成这个源的指令：@https://npm.zhonganinfo.com/ 

---

_**Assistant**_

要将 npm 源设置为 https://npm.zhonganinfo.com/，你可以使用以下命令：

```bash
npm config set registry https://npm.zhonganinfo.com/
```

这个命令会将 npm 的默认源更改为你指定的地址。

执行完这个命令后，你可以通过之前提到的方法来验证设置是否生效：

```bash
npm config get registry
```

这应该会显示新设置的源地址：https://npm.zhonganinfo.com/

请注意，更改 npm 源可能会影响你的项目依赖安装。确保这个新的源是你想要使用的，并且包含你项目所需的所有包。

如果你只想临时使用这个源，而不是永久更改配置，你可以在安装包时使用 `--registry` 参数：

```bash
npm install 包名 --registry=https://npm.zhonganinfo.com/
```

这样只会在这次安装时使用指定的源，不会更改全局配置。

最后，如果你之后想要恢复到默认的 npm 源，可以使用：

```bash
npm config set registry https://registry.npmjs.org/
```

这会将源重置为官方的 npm 源。

---



---

