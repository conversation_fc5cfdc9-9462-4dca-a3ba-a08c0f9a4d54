<!-- Generated by SpecStory -->

# Claude 3.5与GPT-4的比较分析 (2024-11-15 11:24:20)

_**User**_

@Web 帮我找一下claude3.5 做不到。 GPTo1 能做到的场景

---

_**Assistant**_

根据搜索结果和[The Deep View的报道](https://www.thedeepview.co/p/claude-3-gpt4)，我来分析一下Claude 3.5的一些局限性：

1. 图像分析方面的限制：
- 无法进行人物识别
- 分析低质量图片的能力有限
- 在需要详细空间推理的任务上表现较弱

2. 功能限制：
- 目前还不能直接引用来源(source citation功能还在开发中)
- 作为新模型,在某些特定领域的知识储备可能不如GPT-4丰富

3. 通用限制：
- 和其他AI模型一样,仍然存在偏见(bias)问题
- 在处理需要实时更新的信息时有局限性
- 对于高度专业化或小众领域的知识可能存在不足

4. 技术限制：
- 在处理复杂的多步骤空间推理任务时可能不如GPT-4
- 对于需要长期记忆或上下文理解的复杂对话可能存在挑战

需要注意的是,这些限制是基于目前公开的信息,随着模型的不断更新和改进,这些限制可能会逐步得到解决。建议在使用时根据具体任务需求选择合适的AI模型。

---

_**User**_

@https://mp.weixin.qq.com/s?search_click_id=4357726600929868067-1731628103344-8481201908&__biz=Mzg4OTc0NTk0Mw==&mid=2247488960&idx=1&sn=a110328bd5682c81a373c6c919a4504c&chksm=cee92090b4b58c7df837414256be703103921aa74f3bd8c9f08aad300e62c392ab2cd553e41b&scene=7&subscene=10000&clicktime=1731628103&enterid=1731628103&sessionid=0&ascene=65&fasttmpl_type=0&fasttmpl_fullversion=7471130-zh_CN-zip&fasttmpl_flag=0&realreporttime=1731628103374&devicetype=android-34&version=4.1.26.70359&nettype=ctnet&abtest_cookie=AAACAA%3D%3D&lang=zh_CN&countrycode=CN&exportkey=n_ChQIAhIQMWy44O4nAvsBaJ5q9%2B5G4BLiAQIE97dBBAEAAAAAADTkJJDCUcsAAAAOpnltbLcz9gKNyK89dVj0Y8m5SWt9ev8dmzVh6Fbp%2FqcNyggdE%2B9fUX37Dlvgzn0WortatPW%2Bx10n3i%2F%2FQ9revfbLrIIqm%2Bu04UEuwsozuCWKpKWiGIQ7%2Bmb8OcTgUFOyIzfG8W2xAq1S3BSFFryHouFxEFfZJ5BKBxZ9se%2FuB2kZWD4OcERte5XeHeHW%2FBp4nmVK%2FWsQz1Oh6IB6D%2Fsrfq21o42EfXqEQjnna02wX4J0hJIC4KzWmhjj1x0nboO2v8UzmJRr%2BvErc1k%3D&pass_ticket=vgOahcYegYhb0owSVGW1E8iIE9CXZN%2B5mBOk2hsAYSspH%2Be0GavewwyVIumodVzI&wx_header=3&platform=mac&nwr_flag=1#wechat_redirect 

这个提示词有用吗？

---

_**Assistant**_

根据文章内容分析，这个"Thinking-Claude"提示词确实非常有价值：

1. 核心优势：
- 让Claude能够像人类一样进行深度思考
- 采用自然、有机的意识流思维方式
- 避免机械化的结构化思维
- 在回答前进行全面的多维度分析

2. 实际应用效果：
- 游戏开发：能快速生成无bug的小游戏
- 数学问题：可以解决复杂的数学题
- 文案创作：能产出高质量的文学作品
- 思维链展示：通过浏览器插件可以清晰展示思考过程

3. 创新点：
- 模拟人类思维过程
- 采用自然语言表达
- 保持思维的连贯性和逻辑性
- 支持多语言使用（中英文都有效）

4. 局限性：
- 需要较长的提示词输入
- 可能会增加响应时间
- 对系统资源消耗较大

总的来说，这个提示词确实能显著提升Claude的表现，让其在某些场景下达到接近或不亚于GPT-4的水平。不过建议根据具体使用场景来决定是否使用，因为完整的思维链过程可能会让简单问题变得过于复杂。

[原文链接](https://mp.weixin.qq.com/s?search_click_id=4357726600929868067-1731628103344-8481201908&__biz=Mzg4OTc0NTk0Mw==&mid=2247488960&idx=1&sn=a110328bd5682c81a373c6c919a4504c)

---



---

