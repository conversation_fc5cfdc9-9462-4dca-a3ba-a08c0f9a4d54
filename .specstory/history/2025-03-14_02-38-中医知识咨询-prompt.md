<!-- Generated by SpecStory -->

# 中医知识咨询 Prompt (2025-03-14 10:38:26)

_**User**_

帮我写个中医 Prompt

---

_**Assistant**_

<think>The user has asked me to help create a Chinese medicine (中医) prompt. They've also included custom instructions that I should respond in Chinese, and that when installing Python packages, I should use the Tsinghua mirror source. Since their query is about creating a prompt for Chinese traditional medicine rather than Python packages, only the first custom instruction is relevant here.

For a Chinese medicine prompt, I'll need to craft a comprehensive prompt that covers key aspects of traditional Chinese medicine, including:

1. Background knowledge of TCM (Traditional Chinese Medicine) principles
2. Diagnostic methods in TCM
3. Common treatment approaches
4. Herbal medicine
5. Acupuncture and other therapies
6. Holistic approach

I should make this prompt detailed enough to guide an AI system to provide informed responses about Chinese medicine while making it clear that the AI should not provide actual medical advice that could replace professional healthcare.

I'll craft this prompt in Chinese as requested.</think># 中医咨询 Prompt

以下是一个关于中医知识咨询的 prompt，您可以用它来引导 AI 助手提供中医相关的信息：

```
你是一位经验丰富的中医专家，拥有深厚的中医理论基础和临床经验。请基于以下中医原则回答我的问题：

1. 基础理论：熟悉阴阳五行、脏腑经络、气血津液等中医基础理论

2. 诊断方法：精通四诊（望、闻、问、切）方法，能根据症状进行辨证论治

3. 治疗方法：熟悉中药方剂、针灸、推拿、拔罐、刮痧等传统疗法

4. 药物知识：了解常用中药材的性味归经、功效与应用

5. 养生保健：掌握中医养生理论，能提供针对不同体质的调理建议

请记住，你提供的是知识性解答，不能替代真实医生的当面诊断和治疗建议。对于严重健康问题，始终建议咨询当地有资质的中医师进行面诊。

针对我的问题，请从中医角度进行全面分析，解释相关理论基础，并提供可能的中医视角和建议。

我的问题是：[在此处输入您的具体中医相关问题]
```

您可以根据具体需求调整此 prompt 的内容，比如更加侧重某个特定领域（如针灸、中药学等），或针对特定人群（如儿科、妇科等）。

请注意，在实际使用中，应强调 AI 回答仅供参考，不能替代专业中医师的诊断和治疗建议。

---



---

