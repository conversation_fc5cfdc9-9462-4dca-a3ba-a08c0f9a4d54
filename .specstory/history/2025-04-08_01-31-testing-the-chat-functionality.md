<!-- Generated by SpecStory -->

# Testing the Chat Functionality (2025-04-08 09:31:52)

_**User**_

@Web 帮我搜一下， 本地部署的DeepSeek有联网功能吗？

---

_**Assistant**_



---

根据我找到的信息，本地部署的 DeepSeek 模型本身通常不直接具备联网搜索功能，但可以通过结合其他工具或界面来实现联网。

一些方法包括：

1.  **使用 `Page Assist` 浏览器插件：** 这是一个开源的浏览器扩展程序，可以为本地运行的 AI 模型（包括通过 Ollama 部署的 DeepSeek）提供一个 Web UI，并支持联网搜索功能。 [1](https://deepseek.csdn.net/67ab1b4c79aaf67875cb9442.html) [2](https://deepseek.csdn.net/67ab1b1d79aaf67875cb93d0.html)
2.  **使用 `open-webui`：** 这是一个可以通过 Docker 安装的 Web UI，与 Ollama 结合使用时，也能为本地部署的 DeepSeek 模型提供交互界面，并支持联网搜索。 [2](https://deepseek.csdn.net/67ab1b1d79aaf67875cb93d0.html)

因此，虽然 DeepSeek 模型本身专注于推理，但通过这些辅助工具，可以让你的本地部署实例连接到互联网进行搜索。

---



---

