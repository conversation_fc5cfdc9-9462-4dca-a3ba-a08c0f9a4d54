<!-- Generated by SpecStory -->

# 灵犀平台视频脚本需求 (2025-03-18 14:35:13)

_**User**_

我们公司有个 AI 场景编排的平台，叫做“灵犀“
基于灵犀， 我需要录制一个视频来简单，但透露着专业，来大致介绍一下灵犀平台。我将会给你平台的几个页面。

1. 首页：

2. 点进首页进到具体的机器人里面去：
快速问答就类似ChatGPT，豆包的聊天对话， 不过与他们不同的是， 这里能选到之前编排好的技能，

3. 具体的技能编排，这里是核心包括技能创建，技能编排， 以及发布。

基于以上几张图， 以及描述。 我需要你给我录制视频的脚本，稿子。


---

_**Assistant**_



---

# 灵犀AI场景编排平台宣传视频脚本

## 开场

**[画面展示灵犀平台首页]**

*背景音乐：简洁、科技感*

**旁白：** 
"众有灵犀，人人都是AI玩家。今天，我们为您介绍一款革命性的AI场景编排平台——灵犀。无需编程基础，轻松打造专属AI应用。"

## 第一部分：平台概览

**[画面展示首页各种机器人及数据]**

**旁白：**
"灵犀平台汇集了多种类型的AI应用，从客服咨询到生产验证，从催收到保险营销，每一个场景都能通过灵犀实现智能化。平台数据实时可见，让您随时了解AI应用的使用情况。"

**[镜头扫过各个机器人卡片，展示数据增长]**

**旁白：**
"您看，这些数字背后是成千上万用户的选择与信任。灵犀正在改变传统行业的工作方式，提升效率，创造价值。"

## 第二部分：对话界面与技能选择

**[画面切换到保后-催收机器人的对话界面]**

**旁白：**
"进入具体的机器人应用后，您会看到类似常见AI聊天的界面，但灵犀的独特之处在于——"

**[特写'使用快捷技能'区域的预编排技能列表]**

**旁白：**
"您可以直接调用已编排好的专业技能，如预提醒功能、科大生成式模型等。这些技能都是针对特定场景精心设计的，一键调用，即可发挥强大功能。"

## 第三部分：技能管理中心

**[画面切换到技能列表页]**

**旁白：**
"在灵犀的技能管理中心，您可以查看所有已创建的技能。每个技能都有明确的更新日期和状态标识，便于团队协作和版本管理。"

**[特写技能卡片的编辑、设置、删除按钮]**

**旁白：**
"技能管理非常简便，编辑、设置、删除一应俱全。您还可以根据业务需求随时创建新的技能。"

## 第四部分：核心功能 - 技能编排

**[画面切换到技能编排界面，展示工作流程图]**

**旁白：**
"现在，让我们进入灵犀的核心功能区——技能编排。这是一个直观、可视化的编排界面。"

**[特写左侧组件菜单]**

**旁白：**
"左侧提供了丰富的组件，包括控制流、输入输出、条件判断、大模型调用、知识库查询等。只需拖拽组件到画布，连接流程，就能构建复杂的AI应用场景。"

**[展示工作流程图的节点和连接]**

**旁白：**
"您看，这个工作流程图展示了一个完整的业务流程：从用户输入开始，经过意图识别、大模型处理、知识库查询，最终生成专业回复。整个过程无需编写一行代码，却能实现专业级的AI应用。"

## 结束部分

**[画面回到灵犀平台首页，突出"众有灵犀 人人都是AI玩家"标语]**

**旁白：**
"灵犀平台，让AI技术真正赋能各行各业。无论您是金融、保险、教育还是医疗行业，都能通过灵犀平台快速构建专属AI应用，提升业务效率，创造新的价值。"

**[显示平台登录入口]**

**旁白：**
"众有灵犀，您的智能伙伴。立即体验，开启AI新时代！"

*结束音乐淡出*

## 补充说明

1. 视频建议时长：2-3分钟
2. 配音：专业男/女声，语调自然流畅，充满科技感
3. 背景音乐：科技感强，节奏适中
4. 可在关键功能处添加简单动画效果，增强观看体验
5. 可考虑在视频最后添加实际应用案例的简短展示

---

_**User**_

目的错了。 你这个像是商业宣传脚本。

而我想要的只是平台使用脚本。 一切需要务实、 谦虚、 把平台该有的功能展示出来就行

---

_**Assistant**_



---

# 灵犀AI场景编排平台使用教程脚本

## 开场

**[画面展示灵犀平台首页]**

**旁白：** 
"大家好，今天我将为大家介绍灵犀AI场景编排平台的基本使用方法。这个平台能帮助我们快速搭建和管理AI应用场景。"

## 第一部分：平台首页

**[画面展示首页各种机器人卡片]**

**旁白：**
"登录后，我们看到的是灵犀平台的首页。这里展示了所有可用的机器人应用，每个卡片都显示了近7天的使用数据，包括访问次数和调用技能次数。"

**[指向搜索框]**

**旁白：**
"如果机器人较多，可以通过顶部的搜索框快速找到需要的机器人。"

## 第二部分：机器人对话界面

**[画面切换到保后-催收机器人的对话界面]**

**旁白：**
"点击任意机器人卡片，就能进入对话界面。这里的界面分为几个部分：左侧是功能导航栏，中间是对话区域，右侧是快捷功能区。"

**[指向'使用快捷技能'区域]**

**旁白：**
"在右侧的'使用快捷技能'区域，我们可以看到已经配置好的技能，如'预提醒-qwen-plus'、'预提醒-科大生成式-4.0'等。点击这些技能卡片可以快速调用对应功能。"

**[演示点击一个技能卡片]**

**旁白：**
"这是灵犀平台的一个便捷之处：我们可以通过直接对话，也可以通过预设技能，让AI快速响应特定场景的需求。"

## 第三部分：技能管理

**[画面切换到技能列表页]**

**旁白：**
"点击左侧导航栏的'技能管理'，进入技能管理页面。这里我们可以看到所有已创建的技能。"

**[指向页面顶部的筛选器和创建按钮]**

**旁白：**
"我们可以使用筛选器查找特定类型的技能，也可以点击右上角的'创建'按钮新建技能。"

**[指向技能卡片]**

**旁白：**
"每张技能卡片显示了技能名称、状态、最后更新时间等信息。卡片底部的操作按钮允许我们编辑、设置或删除技能。"

## 第四部分：技能编排（核心功能）

**[画面切换到小鸟-预提醒技能的编排界面]**

**旁白：**
"现在我们进入灵犀平台的核心功能——技能编排界面。左侧是组件菜单，中间是工作流设计区域。"

**[指向左侧组件菜单]**

**旁白：**
"组件菜单分为几类：控制、输入、输出、条件、大模型、知识库、调用等。这些组件是构建AI应用流程的基础元素。"

**[指向中间的工作流图]**

**旁白：**
"在工作流区域，我们可以看到一个完整的流程设计。这个流程从输入开始，经过意图识别、大模型处理等节点，最终输出结果。每个节点都有具体配置，连线表示数据流向。"

**[演示拖拽一个组件到工作区]**

**旁白：**
"创建流程很简单，只需从左侧拖拽所需组件到工作区，然后连接各个节点，设置必要的参数即可。无需编写代码，通过可视化方式就能实现复杂的AI处理流程。"

**[点击保存和发布按钮]**

**旁白：**
"完成流程设计后，点击右上角的'保存'按钮保存当前编排。如果要让这个技能生效，还需要点击'发布'按钮。"

## 结束部分

**[回到主界面]**

**旁白：**
"以上就是灵犀AI场景编排平台的基本使用流程，包括浏览机器人、使用对话功能、管理技能以及编排技能流程。通过这个平台，我们可以快速搭建适合不同业务场景的AI应用，提高工作效率。"

**旁白：**
"感谢观看，希望这个教程对你了解和使用灵犀平台有所帮助。"

## 补充说明

1. 视频建议时长：4-5分钟
2. 语调：自然、清晰，以教学为主
3. 可在演示关键操作时放慢速度
4. 重点强调实用功能，避免过度宣传
5. 适当使用指针或高亮效果指示正在讲解的界面元素

---

_**User**_

把上述的脚本，输出成 Markdown 文档，放在当前目录下。

---

_**Assistant**_



---

```diff
- 
+ # 灵犀AI场景编排平台使用教程脚本
+ 
+ ## 开场
+ 
+ **[画面展示灵犀平台首页]**
+ 
+ **旁白：** 
+ "大家好，今天我将为大家介绍灵犀AI场景编排平台的基本使用方法。这个平台能帮助我们快速搭建和管理AI应用场景。"
+ 
+ ## 第一部分：平台首页
+ 
+ **[画面展示首页各种机器人卡片]**
+ 
+ **旁白：**
+ "登录后，我们看到的是灵犀平台的首页。这里展示了所有可用的机器人应用，每个卡片都显示了近7天的使用数据，包括访问次数和调用技能次数。"
+ 
+ **[指向搜索框]**
+ 
+ **旁白：**
+ "如果机器人较多，可以通过顶部的搜索框快速找到需要的机器人。"
+ 
+ ## 第二部分：机器人对话界面
+ 
+ **[画面切换到保后-催收机器人的对话界面]**
+ 
+ **旁白：**
+ "点击任意机器人卡片，就能进入对话界面。这里的界面分为几个部分：左侧是功能导航栏，中间是对话区域，右侧是快捷功能区。"
+ 
+ **[指向'使用快捷技能'区域]**
+ 
+ **旁白：**
+ "在右侧的'使用快捷技能'区域，我们可以看到已经配置好的技能，如'预提醒-qwen-plus'、'预提醒-科大生成式-4.0'等。点击这些技能卡片可以快速调用对应功能。"
+ 
+ **[演示点击一个技能卡片]**
+ 
+ **旁白：**
+ "这是灵犀平台的一个便捷之处：我们可以通过直接对话，也可以通过预设技能，让AI快速响应特定场景的需求。"
+ 
+ ## 第三部分：技能管理
+ 
+ **[画面切换到技能列表页]**
+ 
+ **旁白：**
+ "点击左侧导航栏的'技能管理'，进入技能管理页面。这里我们可以看到所有已创建的技能。"
+ 
+ **[指向页面顶部的筛选器和创建按钮]**
+ 
+ **旁白：**
+ "我们可以使用筛选器查找特定类型的技能，也可以点击右上角的'创建'按钮新建技能。"
+ 
+ **[指向技能卡片]**
+ 
+ **旁白：**
+ "每张技能卡片显示了技能名称、状态、最后更新时间等信息。卡片底部的操作按钮允许我们编辑、设置或删除技能。"
+ 
+ ## 第四部分：技能编排（核心功能）
+ 
+ **[画面切换到小鸟-预提醒技能的编排界面]**
+ 
+ **旁白：**
+ "现在我们进入灵犀平台的核心功能——技能编排界面。左侧是组件菜单，中间是工作流设计区域。"
+ 
+ **[指向左侧组件菜单]**
+ 
+ **旁白：**
+ "组件菜单分为几类：控制、输入、输出、条件、大模型、知识库、调用等。这些组件是构建AI应用流程的基础元素。"
+ 
+ **[指向中间的工作流图]**
+ 
+ **旁白：**
+ "在工作流区域，我们可以看到一个完整的流程设计。这个流程从输入开始，经过意图识别、大模型处理等节点，最终输出结果。每个节点都有具体配置，连线表示数据流向。"
+ 
+ **[演示拖拽一个组件到工作区]**
+ 
+ **旁白：**
+ "创建流程很简单，只需从左侧拖拽所需组件到工作区，然后连接各个节点，设置必要的参数即可。无需编写代码，通过可视化方式就能实现复杂的AI处理流程。"
+ 
+ **[点击保存和发布按钮]**
+ 
+ **旁白：**
+ "完成流程设计后，点击右上角的'保存'按钮保存当前编排。如果要让这个技能生效，还需要点击'发布'按钮。"
+ 
+ ## 结束部分
+ 
+ **[回到主界面]**
+ 
+ **旁白：**
+ "以上就是灵犀AI场景编排平台的基本使用流程，包括浏览机器人、使用对话功能、管理技能以及编排技能流程。通过这个平台，我们可以快速搭建适合不同业务场景的AI应用，提高工作效率。"
+ 
+ **旁白：**
+ "感谢观看，希望这个教程对你了解和使用灵犀平台有所帮助。"
+ 
+ ## 补充说明
+ 
+ 1. 视频建议时长：4-5分钟
+ 2. 语调：自然、清晰，以教学为主
+ 3. 可在演示关键操作时放慢速度
+ 4. 重点强调实用功能，避免过度宣传
+ 5. 适当使用指针或高亮效果指示正在讲解的界面元素 
```

---

已将灵犀AI场景编排平台使用教程脚本保存为Markdown文件。文件名为"灵犀AI场景编排平台使用教程脚本.md"，已存放在当前目录下。

---

_**User**_

很好， 这里还需要补充上知识库相关的介绍。 图片给到你了。

1. 介绍一下知识库类型的区分
2. 结合一下之前的技能配置介绍一下配置完怎么用， 使用场景是什么样子的。 

补充这个本子

---

_**Assistant**_



---

```diff
  "这是灵犀平台的一个便捷之处：我们可以通过直接对话，也可以通过预设技能，让 AI 快速响应特定场景的需求。"
  
- ## 第三部分：技能管理
+ ## 第三部分：知识库管理
  
- **[画面切换到技能列表页]**
+ **[画面切换到知识库页面]**
+ 
+ **旁白：**
+ "点击左侧导航栏的'知识库'，进入知识库管理页面。知识库是AI回答问题的重要信息来源，可以让AI基于企业专有知识进行回答。"
+ 
+ **[指向顶部的三个知识库类型标签]**
+ 
+ **旁白：**
+ "灵犀平台支持三种类型的知识库：文档知识库、问答知识库和结构化知识库。"
+ 
+ **[特写文档知识库标签]**
+ 
+ **旁白：**
+ "文档知识库适用于导入Word、PDF等文档，系统会自动提取文档中的内容作为知识源。这适合处理大量的文本文档，如产品手册、政策条款等。"
+ 
+ **[特写问答知识库标签]**
+ 
+ **旁白：**
+ "问答知识库是以问答对形式存储的知识，包括标准问题和对应答案。这适合FAQ场景，能准确匹配用户提问并给出标准回答。"
+ 
+ **[特写结构化知识库标签]**
+ 
+ **旁白：**
+ "结构化知识库则是以表格形式存储的知识，适合处理有明确字段和关系的数据，如产品参数对比、客户信息等。"
+ 
+ **[指向左侧知识库分类菜单]**
+ 
+ **旁白：**
+ "左侧菜单栏可以看到知识库的分类，如'默认知识库'、'资金困难'、'拒绝还款'等。这些分类帮助我们更好地管理不同业务场景的知识。"
+ 
+ **[指向知识条目列表]**
+ 
+ **旁白：**
+ "中间区域显示知识条目列表，每条知识都有唯一编号、检索问题、相似问题等字段。我们可以通过'新增知识'按钮添加新的知识条目。"
+ 
+ **[演示点击编辑按钮]**
+ 
+ **旁白：**
+ "通过操作栏的按钮，我们可以编辑、删除或设置知识条目。每个条目都有创建时间和创建人信息，方便团队协作管理。"
+ 
+ ## 第四部分：知识库与技能的结合使用
+ 
+ **[画面切换回技能编排界面，指向知识库组件]**
+ 
+ **旁白：**
+ "在技能编排环节，我们可以看到左侧组件菜单中有'知识库'类组件。通过拖拽这些组件到工作流中，可以让AI调用指定的知识库内容。"
+ 
+ **[演示拖拽知识库查询组件到工作区]**
+ 
+ **旁白：**
+ "例如，我们可以添加'知识库查询'组件，设置要查询的知识库类型和范围，实现在对话中精准调用相关知识。"
+ 
+ **[展示配置知识库组件的参数界面]**
  
  **旁白：**
- "点击左侧导航栏的'技能管理'，进入技能管理页面。这里我们可以看到所有已创建的技能。"
+ "在组件配置中，我们可以选择关联哪个知识库，设置查询方式、相似度阈值等参数，使AI回答更加精准。"
  
- **[指向页面顶部的筛选器和创建按钮]**
+ **[画面切换到实际对话场景]**
  
  **旁白：**
- "我们可以使用筛选器查找特定类型的技能，也可以点击右上角的'创建'按钮新建技能。"
+ "在实际使用场景中，当用户询问相关问题时，系统会自动从知识库中检索最相关的内容。例如，在催收场景中，当客户表示'最近资金紧张'时，AI可以调用'资金困难'分类下的知识，给出合适的回复和解决方案。"
  
- **[指向技能卡片]**
+ **[演示一个完整的用户问答流程]**
  
  **旁白：**
- "每张技能卡片显示了技能名称、状态、最后更新时间等信息。卡片底部的操作按钮允许我们编辑、设置或删除技能。"
+ "这样，我们就实现了专业知识与AI对话能力的结合，让AI回答既有大模型的灵活性，又有企业专有知识的专业性和准确性。"
  
- ## 第四部分：技能编排（核心功能）
+ ## 第五部分：技能编排（核心功能）
  
  **[画面切换到小鸟-预提醒技能的编排界面]**
  
  **旁白：**
- "以上就是灵犀 AI 场景编排平台的基本使用流程，包括浏览机器人、使用对话功能、管理技能以及编排技能流程。通过这个平台，我们可以快速搭建适合不同业务场景的 AI 应用，提高工作效率。"
+ "以上就是灵犀 AI 场景编排平台的基本使用流程，包括浏览机器人、使用对话功能、管理知识库、管理技能以及编排技能流程。通过这个平台，我们可以快速搭建适合不同业务场景的 AI 应用，提高工作效率。"
  
  **旁白：**
```

---

已将知识库相关内容添加到灵犀AI场景编排平台使用教程脚本中。新增内容包括：

1. 增加了"第三部分：知识库管理"，详细介绍了三种知识库类型：
   - 文档知识库：适用于导入Word、PDF等文档
   - 问答知识库：以问答对形式存储知识，适合FAQ场景
   - 结构化知识库：以表格形式存储有明确字段和关系的数据

2. 新增"第四部分：知识库与技能的结合使用"，说明了：
   - 如何在技能编排中使用知识库组件
   - 如何配置知识库查询参数
   - 实际对话场景中知识库的应用，如催收场景中调用"资金困难"分类下的知识

3. 将原来的"技能编排"部分调整为第五部分，并在结束部分添加了对知识库管理的总结提及。

修改已保存到原文件中，可以继续查看和编辑。

---

_**User**_

挺好的。

现在我需要你给我完成两个文件。

1. 辅助演示的企业知识库内容。 我会维护到知识库里面辅助演示。 内容需可以展示知识库功能的编排

2. “问答对“的文件， 我也会维护到问答知识库里去。

你生成这两个文件， 放在当前目录下。

---

_**Assistant**_



---

```diff
- 
+ # 企业知识库内容（催收场景示例）
+ 
+ ## 公司产品信息
+ 
+ ### 贷款产品类型
+ - **小额信贷**：额度5000-50000元，期限3-12个月，日利率0.03%-0.05%
+ - **消费信贷**：额度10000-100000元，期限6-24个月，日利率0.02%-0.04%
+ - **经营贷款**：额度50000-500000元，期限12-36个月，日利率0.015%-0.03%
+ 
+ ### 还款方式
+ - **等额本息**：每月还款金额相同，包括本金和利息
+ - **等额本金**：每月归还固定本金和剩余本金利息，月供逐月递减
+ - **先息后本**：贷款期限内只归还利息，到期一次性归还本金
+ - **灵活还款**：根据客户情况定制还款计划，适用于特殊场景
+ 
+ ## 催收政策与流程
+ 
+ ### 逾期等级定义
+ - **轻度逾期**：逾期1-7天
+ - **中度逾期**：逾期8-30天
+ - **重度逾期**：逾期31-90天
+ - **严重逾期**：逾期超过90天
+ 
+ ### 催收流程
+ 1. **预提醒**：还款日前3天发送短信提醒
+ 2. **轻度催收**：电话提醒、短信提醒
+ 3. **中度催收**：电话催收、上门拜访
+ 4. **重度催收**：律师函、法律诉讼预警
+ 5. **诉讼阶段**：诉讼流程启动、财产保全
+ 
+ ### 催收禁区
+ - 禁止在22:00-8:00进行电话催收
+ - 禁止向客户亲友透露具体欠款信息
+ - 禁止使用威胁、恐吓等过激言辞
+ - 禁止频繁骚扰客户（每日通话不超过3次）
+ - 禁止在客户工作场所大声催收
+ 
+ ## 客户情况应对策略
+ 
+ ### 资金困难
+ - **临时困难**：可申请延期还款，最长可延期30天，需支付延期费用
+ - **短期失业**：可申请缓息方案，暂缓利息，仅归还本金
+ - **重大变故**：可申请减免部分罚息，评估后制定专项还款计划
+ - **长期困难**：可推荐债务重组方案，延长还款期限，降低月供压力
+ 
+ ### 拒绝还款
+ - **质疑合同**：出示电子合同及签署记录，说明法律效力
+ - **质疑费用**：详细解释费用构成，提供计算明细
+ - **恶意拖欠**：告知不良信用记录影响，说明可能面临的法律后果
+ - **联系不上**：启动辅助联系人通知程序，必要时进行地址排查
+ 
+ ## 常见问题解答
+ 
+ ### 还款问题
+ - **问**：逾期一天有影响吗？
+ - **答**：逾期即使一天也会产生罚息，且记入信用记录，建议按时还款。
+ 
+ - **问**：如何申请延期还款？
+ - **答**：可通过APP"我的借款"-"申请延期"功能，或联系客服热线申请。
+ 
+ - **问**：还款日是哪一天？
+ - **答**：还款日为每月与放款日相同的日期，如遇节假日不顺延。
+ 
+ ### 费用问题
+ - **问**：逾期费用如何计算？
+ - **答**：逾期费用包括罚息和违约金，罚息为日利率的1.5倍，违约金为当期应还金额的5%。
+ 
+ - **问**：提前还款有违约金吗？
+ - **答**：提前还款需支付剩余本金1%的手续费，且需提前3个工作日申请。
+ 
+ ## 客户权益保障
+ 
+ ### 投诉渠道
+ - 客服热线：400-888-XXXX（工作时间9:00-18:00）
+ - 邮箱投诉：<EMAIL>
+ - APP投诉入口：我的-客户服务-投诉建议
+ 
+ ### 隐私保护
+ - 客户信息严格保密，未经授权不得对外泄露
+ - 催收过程全程录音，确保服务质量和行为规范
+ - 客户有权查询个人信息处理记录
+ 
+ ## 常用话术示例
+ 
+ ### 首次催收
+ "您好，我是XX金融的客服专员。提醒您，您有一笔借款已经到期，为了避免对您的信用造成影响，请您尽快完成还款。如有困难，可以和我详细沟通，我们会尽力为您提供帮助。"
+ 
+ ### 资金困难应对
+ "我理解您目前的资金困难，我们可以为您提供延期还款方案。您只需支付本期利息和少量延期费用，本金可以延期30天归还。这样可以缓解您的当前压力，也不会产生大额逾期记录。"
+ 
+ ### 失联客户恢复联系
+ "很高兴能再次联系到您。关于您的逾期情况，我们希望能够了解您的实际困难，共同寻找解决方案。我们可以根据您的实际情况，制定个性化的还款计划，帮助您度过难关。"
+ 
+ ### 投诉处理
+ "非常抱歉给您带来的不便，我们高度重视您的反馈。我已经记录了您的投诉内容，将在24小时内给您答复。同时，我们会对相关人员进行培训，确保类似问题不再发生。" 
```

---

```diff
- 
+ 问题,回答,类别
+ 我最近资金紧张，无法按时还款怎么办？,您可以申请延期还款服务。根据您的实际情况，我们可以提供最长30天的延期服务，在此期间您只需支付少量延期费用。请登录APP点击"我的借款"-"申请延期"功能，或致电客服热线400-888-XXXX申请办理。,资金困难
+ 我因为失业暂时无法全额还款，有什么解决方案吗？,针对您的情况，我们有专门的缓息方案，可以让您暂时只归还本金，利息部分可以缓交。您可以提供相关证明材料，我们将为您评估并定制专属还款计划。请联系客服专线400-888-XXXX，转接专员为您服务。,资金困难
+ 我家人生病住院，医疗费用支出很大，能否减免部分费用？,对于因重大变故导致的还款困难，我们有相应的关怀政策。您可以提供住院证明及医疗费用单据，我们将评估后为您减免部分罚息，并制定专项还款计划。请联系您的专属客服或拨打热线400-888-XXXX申请。,资金困难
+ 逾期一天会有什么影响吗？,逾期即使只有一天，也会产生罚息（按日利率的1.5倍计算），并且会被记入您的信用记录。长期来看，这可能影响您未来的贷款申请。建议您务必按时还款，如有特殊情况，请提前与我们联系安排。,还款问题
+ 我忘记还款日期了，在哪里可以查询？,您可以通过以下几种方式查询还款日期：1. 登录APP，在"我的借款"页面查看；2. 查看每月发送给您的还款提醒短信；3. 拨打客服热线400-888-XXXX查询。一般而言，还款日为每月与放款日相同的日期，如遇节假日不顺延。,还款问题
+ 提前还款需要支付违约金吗？,提前还款需要支付剩余本金1%的手续费，且需提前3个工作日申请。您可以登录APP，在"我的借款"页面点击"提前还款"，系统会自动计算应还金额及手续费，或致电客服热线400-888-XXXX咨询具体操作流程。,还款问题
+ 逾期费用是怎么计算的？,逾期费用包括两部分：1. 罚息：按照合同日利率的1.5倍计算；2. 违约金：当期应还金额的5%。例如，如果您的贷款日利率为0.03%，逾期后的罚息日利率为0.045%，若当期应还金额为1000元，逾期一天需支付罚息0.45元和违约金50元，共计50.45元。,费用问题
+ 我想查询贷款的总费用构成，可以吗？,当然可以。您的贷款费用主要包括本金、利息、管理费和可能的保险费等。您可以登录APP，在"我的借款"-"借款详情"中查看完整的费用明细和还款计划表。如需更详细解释，可拨打客服热线400-888-XXXX，我们将为您提供完整的费用构成说明。,费用问题
+ 我对最近一期的费用有异议，应该怎么处理？,非常抱歉给您带来困扰。您可以通过以下渠道提出异议：1. 致电客服热线400-888-XXXX；2. 在APP中选择"我的-客户服务-投诉建议"提交问题；3. 发送邮件至*********************。我们会在24小时内核实并给您回复，如确实存在错误，将立即为您更正并赔偿损失。,费用问题
+ 我的合同在哪里可以查看？,您可以通过以下方式查看您的电子合同：1. 登录APP，在"我的借款"-"借款详情"-"查看合同"；2. 登录官网，在"我的账户"-"合同管理"中查看；3. 如需纸质合同，可致电客服热线400-888-XXXX申请邮寄服务（可能收取少量费用）。,合同问题
+ 合同上的电子签名具有法律效力吗？,是的，根据《中华人民共和国电子签名法》，电子签名与手写签名具有同等法律效力。在您申请贷款过程中，我们采用了符合国家标准的电子签名技术，并保存了完整的签署过程及验证信息，确保合同的法律效力。如有疑问，可致电客服热线400-888-XXXX咨询。,合同问题
+ 我想修改合同上的某些条款，可以吗？,贷款合同为标准化合同，一经签署生效后通常不可单方面修改条款。如您对某些条款有特殊需求，可以提前终止当前合同（可能产生提前还款费用），然后重新申请新的贷款产品。您可以致电客服热线400-888-XXXX，我们的专员将为您提供详细的操作建议。,合同问题
+ 我收到了催收电话，但我并没有借款，怎么回事？,非常抱歉给您带来困扰。可能是以下几种情况：1. 信息错误；2. 您是他人贷款的联系人；3. 可能存在身份冒用情况。请您保持冷静，并尽快致电我们的客服热线400-888-XXXX，提供您的身份信息，我们将立即核实并处理，如有错误将立即纠正并道歉。,催收问题
+ 催收人员态度恶劣，我该如何投诉？,我们对此表示诚挚的歉意。我们严格禁止任何不当催收行为。请您记录催收人员的姓名或工号、通话时间，并通过以下渠道投诉：1. 客服热线400-888-XXXX；2. 邮箱投诉*********************；3. APP投诉入口"我的-客户服务-投诉建议"。我们将在24小时内调查处理，并给您满意的答复。,催收问题
+ 催收人员可以联系我的家人朋友吗？,根据相关法规和我们的催收行为准则，催收人员不得向您的家人朋友透露您的欠款信息。他们只能在无法与您取得联系的情况下，通过您提供的紧急联系人询问如何联系到您，但不得透露具体的欠款金额和用途等隐私信息。如有违规行为，请立即向我们投诉。,催收问题
+ 我可以申请哪些类型的贷款？,我们目前提供三种主要贷款产品：1. 小额信贷：额度5000-50000元，期限3-12个月；2. 消费信贷：额度10000-100000元，期限6-24个月；3. 经营贷款：额度50000-500000元，期限12-36个月。您可以根据自身需求选择合适的产品，并通过APP或官网申请。,产品咨询
+ 贵公司的贷款利率是多少？,我们的贷款利率根据产品类型和您的个人信用评分有所不同。一般范围是：1. 小额信贷：日利率0.03%-0.05%；2. 消费信贷：日利率0.02%-0.04%；3. 经营贷款：日利率0.015%-0.03%。具体到您的个人利率，需要通过申请后的评估才能确定。您可以在APP上进行预评估，获取个性化利率报价。,产品咨询
+ 申请贷款需要哪些材料？,基本申请材料包括：1. 身份证；2. 个人银行卡；3. 手机号（实名认证）。根据不同产品和金额，可能还需要提供：1. 收入证明；2. 工作证明；3. 居住证明；4. 经营证明（经营贷适用）等。您可以在APP上选择产品后，系统会提示您需要准备的具体材料。,产品咨询
+ 我的信用不太好，能申请贷款吗？,我们的审核会综合考虑多种因素，不仅仅是传统信用记录。即使您的信用记录不太理想，仍有机会获得贷款，但可能额度较低或利率较高。建议您先在APP上进行预评估，如果符合基本条件，可以提交申请，我们会给予全面评估。同时，我们也提供信用修复建议，帮助您逐步改善信用状况。,产品咨询
+ 我的个人信息会被保密吗？,是的，我们严格遵守《个人信息保护法》等相关法规，对您的个人信息实施严格的保密措施。您的信息仅用于贷款审核、账户管理和法律要求的用途，不会未经授权向第三方披露。我们采用加密技术保护数据传输和存储，并定期进行安全审计。您有权查询、更正和删除您的个人信息。,隐私保护
+ 贵公司会将我的信息出售给第三方吗？,绝对不会。我们严格禁止未经授权出售或共享客户信息。您的个人信息仅在以下情况下与第三方共享：1. 经您明确授权；2. 为完成贷款服务必要的合作方（如支付机构）；3. 法律法规要求的政府部门。所有信息共享都有严格的保密协议约束，确保您的信息安全。,隐私保护 
```

---

已为您创建两个文件：

1. **企业知识库内容.md**：包含催收场景下的企业知识库示例内容，内容涵盖：
   - 公司产品信息（贷款产品类型、还款方式）
   - 催收政策与流程（逾期等级定义、催收流程、催收禁区）
   - 客户情况应对策略（资金困难、拒绝还款）
   - 常见问题解答（还款问题、费用问题）
   - 客户权益保障（投诉渠道、隐私保护）
   - 常用话术示例

2. **问答知识库内容.csv**：按CSV格式创建的问答对，共20条问答内容，分为以下几个类别：
   - 资金困难（3条）
   - 还款问题（3条）
   - 费用问题（3条）
   - 合同问题（3条）
   - 催收问题（3条）
   - 产品咨询（4条）
   - 隐私保护（2条）

这些文件内容符合金融贷款催收场景，可以直接导入到灵犀平台的知识库中，用于演示知识库功能的编排和实际应用效果。

---

_**User**_

问答对的内容应该以企业知识模拟的问答对。
比如公司成立时间， 员工手册之类的。然后输出 docx 文档。

---

_**Assistant**_



---

```diff
- 
+ # 企业知识问答对
+ 
+ ## 公司基本信息
+ 
+ | 问题 | 回答 | 类别 |
+ |------|------|------|
+ | 我们公司是什么时候成立的？ | 我们公司成立于2010年3月15日，至今已有超过14年的历史。公司最初由5位创始人在北京中关村创立，初始注册资本为500万元人民币。 | 公司历史 |
+ | 公司的使命和愿景是什么？ | 我们的使命是"通过科技创新，提供普惠金融服务"；愿景是"成为最受尊敬的金融科技企业"。我们秉承"诚信、创新、共赢、责任"的核心价值观。 | 公司文化 |
+ | 公司目前有多少员工？ | 截至2024年6月，公司共有员工3500余人，其中技术研发人员占比40%，业务运营人员占比35%，客户服务人员占比15%，行政管理及其他人员占比10%。 | 公司规模 |
+ | 公司的主要办公地点在哪里？ | 我们的总部位于北京市海淀区中关村软件园，在上海、深圳、杭州、广州、成都设有分公司，并在香港设有国际业务办公室。 | 公司位置 |
+ | 公司获得过哪些重要荣誉和认证？ | 公司曾获得"中国金融科技50强"、"年度最佳雇主"、"金融科技创新奖"等荣誉，并通过了ISO27001信息安全管理体系认证、ISO9001质量管理体系认证和CMMI5级认证。 | 公司荣誉 |
+ 
+ ## 组织架构
+ 
+ | 问题 | 回答 | 类别 |
+ |------|------|------|
+ | 公司的组织结构是怎样的？ | 公司采用矩阵式组织结构，主要分为产品研发中心、业务运营中心、数据技术中心、风控中心、客户服务中心和行政管理中心六大功能模块，各业务线设置产品负责人(PO)统筹协调。 | 组织架构 |
+ | 公司的决策机制是什么？ | 公司实行董事会领导下的总经理负责制。重大战略决策由董事会负责，日常经营决策由总经理办公会议决定，各部门和业务线有明确的授权范围和决策权限。 | 管理制度 |
+ | 我想了解一下我们部门的主管是谁？ | 请您告知您所在的具体部门，我可以提供相应的部门主管信息。您也可以在公司内网的"组织架构"页面查询完整的部门领导信息。 | 组织架构 |
+ | IT支持部门的联系方式是什么？ | IT支持部门的服务热线是400-888-1234（工作日8:30-18:30），也可以发送邮件至**********************或在OA系统中提交IT服务工单。紧急情况可联系IT值班经理（电话号码可在内网查询）。 | 部门信息 |
+ 
+ ## 员工手册
+ 
+ | 问题 | 回答 | 类别 |
+ |------|------|------|
+ | 公司的工作时间是怎样安排的？ | 我们实行弹性工作制，核心工作时间为10:00-16:00，员工可以在8:30-10:00之间选择上班时间，相应地在16:00-19:00之间下班，保证每天工作8小时。技术开发团队可根据项目需要协商调整工作时间。 | 工作制度 |
+ | 年假如何计算和申请？ | 基础年假为每年5天，入职满1年后每年增加1天，最高可达15天。此外，工龄满5年、10年、15年时分别额外增加3天、5天、7天长期服务假。年假申请需提前3个工作日通过OA系统提交，由直接主管审批。 | 休假制度 |
+ | 绩效考核的周期和标准是什么？ | 我们采用季度考核与年度考核相结合的方式。季度考核侧重于KPI达成情况和阶段性工作表现；年度考核综合评估全年业绩、能力提升和价值观践行情况。考核结果分为S、A、B+、B、C、D六个等级，与年终奖和晋升直接挂钩。 | 绩效管理 |
+ | 公司有哪些晋升通道？ | 公司设有管理序列和专业序列两条晋升通道。管理序列从主管、经理到总监、VP；专业序列从专员、高级专员到专家、资深专家。每年有两次晋升评审机会（6月和12月），需满足在当前职级任职满1年且绩效达到B+以上的基本条件。 | 职业发展 |
+ | 如何申请内部调动？ | 在现岗位工作满一年且近两次绩效考核均达到B及以上等级的员工可申请内部调动。申请流程：在内网"人才发展"平台提交申请→现部门主管审批→目标部门面试评估→人力资源部审核→办理交接。原则上每人每年只能申请一次内部调动。 | 职业发展 |
+ | 公司的薪酬结构是怎样的？ | 薪酬结构包括基本工资、绩效奖金、专项奖励和福利四部分。基本工资按月发放；绩效奖金根据个人和团队绩效按季度发放；专项奖励包括项目奖金、创新奖等；福利包括五险一金、补充商业保险、节日福利等。具体薪资标准依据职级、岗位和个人能力确定。 | 薪酬福利 |
+ | 公司提供哪些福利？ | 法定福利：五险一金（养老、医疗、失业、工伤、生育保险和住房公积金）；<br>补充福利：补充商业医疗保险、意外险、定期体检、带薪休假；<br>特色福利：弹性工作制、生日关怀、节日礼品、团建活动、学习发展基金、员工子女教育补贴、长期服务奖励等。 | 薪酬福利 |
+ 
+ ## 公司政策与制度
+ 
+ | 问题 | 回答 | 类别 |
+ |------|------|------|
+ | 公司的保密政策有哪些？ | 所有员工入职时需签署《保密协议》和《知识产权协议》。公司信息分为绝密、机密、内部资料三级，不同级别信息有不同的访问权限和保密要求。严禁在公共场所、社交媒体讨论或披露公司商业机密。违反保密协议将面临纪律处分直至解除劳动合同，情节严重者可能承担法律责任。 | 保密制度 |
+ | 什么情况下员工会被解雇？ | 根据《员工手册》第五章规定，以下情况可能导致解雇：1)严重违反公司规章制度；2)严重失职或营私舞弊；3)连续旷工超过3天或累计旷工超过6天；4)连续两次绩效考核为D级；5)被依法追究刑事责任。解雇决定需经部门主管、人力资源部和法务部共同审核，并由总经理批准。 | 纪律处罚 |
+ | 公司的差旅报销政策是什么？ | 差旅标准按职级设定：总监级及以上可乘坐飞机头等舱/高铁商务座，住宿五星级酒店；经理级可乘坐飞机经济舱/高铁一等座，住宿四星级酒店；专员级乘坐高铁二等座/硬卧，住宿三星级酒店。差旅报销需在出差结束后7个工作日内在OA系统提交，并附上有效票据。 | 财务制度 |
+ | 如何申请办公用品？ | 办公用品分为标准配置和非标准配置两类。标准办公用品（如纸张、笔等）可直接在内网"行政服务"平台申请，每月有固定额度；非标准办公用品需提交申请表，500元以下由部门经理审批，500-2000元由部门总监审批，2000元以上需由行政部门会签后报请分管副总审批。 | 行政管理 |
+ | 公司对员工培训有什么政策？ | 公司设有完善的培训体系，包括新员工入职培训、专业技能培训、管理能力培训和领导力发展项目。每位员工每年有3000元的学习发展基金，可用于参加外部培训或购买学习资料。对于公司统一组织的关键岗位培训，通常要求员工签署服务期协议，在规定服务期内离职需按比例返还培训费用。 | 培训发展 |
+ | 公司的环保政策有哪些？ | 我们倡导"绿色办公"理念，实施以下环保措施：1)推行无纸化办公，鼓励电子文档流转；2)设置废旧电池、纸张等回收点；3)倡导节约用电用水，下班后关闭电源；4)鼓励员工乘坐公共交通工具或拼车上下班；5)每年组织环保公益活动，如植树活动、环保志愿者日等。 | 企业责任 |
+ 
+ ## 信息技术使用
+ 
+ | 问题 | 回答 | 类别 |
+ |------|------|------|
+ | 如何重置内网密码？ | 密码重置有三种方式：1)自助重置：访问密码自助服务平台(password.example.com)，通过预留手机号验证重置；2)IT服务台重置：致电400-888-1234申请重置；3)现场重置：携带工牌前往IT服务台现场处理。首次登录或密码重置后，系统会要求强制修改初始密码。 | IT支持 |
+ | 公司的网络使用规定有哪些？ | 公司网络仅限工作用途，禁止访问色情、赌博等不良网站；禁止下载、安装未经授权的软件；禁止使用P2P软件占用带宽；禁止将公司WiFi密码告知外部人员。IT部门会定期监控网络使用情况，违规使用将面临警告直至解除劳动合同的处罚。 | IT政策 |
+ | 员工可以使用哪些软件工具？ | 公司为员工提供标准化的软件工具包，包括：1)办公套件：Microsoft 365；2)通讯工具：企业微信、Zoom；3)项目管理：JIRA、Confluence；4)开发工具：Visual Studio、IntelliJ IDEA等；5)设计工具：Adobe Creative Cloud。如需使用其他软件，须通过IT服务平台申请审批。 | IT支持 |
+ | 公司的数据备份政策是什么？ | 员工工作电脑中的重要数据应定期备份至公司指定的网盘或云存储空间。公司服务器数据实行自动备份机制：关键业务系统每小时增量备份，每日全量备份；一般系统每日增量备份，每周全量备份。所有备份数据保留期为：增量备份30天，全量备份180天。 | 数据安全 |
+ 
+ ## 应急处理
+ 
+ | 问题 | 回答 | 类别 |
+ |------|------|------|
+ | 办公室发生火灾怎么办？ | 发现火情第一时间拨打内线火警电话8119，同时按响最近的火警报警器；小型火情可使用灭火器扑救，但确保人身安全第一；听到疏散警报后，按照疏散指示牌指引，通过安全通道有序撤离至指定集合点；撤离时不要使用电梯，保持低姿态，用湿毛巾捂住口鼻。每季度公司会组织一次消防演练，请积极参与。 | 安全管理 |
+ | 工作期间发生人身伤害怎么处理？ | 现场人员应立即拨打内线急救电话8120，同时通知行政部门；公司每层楼的医疗急救箱可提供基础急救物品；严重伤害需及时拨打120，并由行政部门指派人员陪同就医；事后需填写《工伤事故报告表》，提交至人力资源部，按工伤保险流程办理。公司定期组织急救知识培训，鼓励员工参加。 | 安全管理 |
+ | 发现信息安全隐患怎么报告？ | 发现信息安全问题（如可疑邮件、异常系统行为、数据泄露迹象等）应立即通过以下渠道报告：1)发送邮件至********************；2)拨打信息安全热线8888；3)在内网安全平台提交问题。信息安全团队会在24小时内响应并处理。公司鼓励举报安全隐患，并设有安全奖励计划。 | 信息安全 |
+ 
+ ## 员工发展与培训
+ 
+ | 问题 | 回答 | 类别 |
+ |------|------|------|
+ | 公司有哪些培训项目？ | 公司培训体系包括：1)"新芽计划"：新员工入职培训；2)"技能树计划"：专业技能进阶培训；3)"领航计划"：管理者培训；4)"火箭计划"：高潜人才培养；5)"大师课"：行业专家讲座。此外，各部门还有针对业务需求的专项培训。培训日历可在内网"成长中心"查看，大部分课程提供线上学习选项。 | 培训发展 |
+ | 如何申请外部培训资源？ | 申请外部培训需在OA系统提交《外部培训申请表》，填写培训内容、机构、费用、预期收益等信息。5000元以下培训由部门经理审批，5000-20000元由部门总监审批，20000元以上需由人力资源部评估后报请分管副总审批。参训后需提交培训总结，并在部门内部分享学习成果。 | 培训发展 |
+ | 公司提供哪些学习资源？ | 公司提供丰富的学习资源：1)内部知识库和课程平台；2)专业技术图书馆（实体和电子）；3)外部学习平台会员（如Coursera、Udemy企业版）；4)行业会议和研讨会参与机会；5)导师制和跨部门轮岗计划。员工可以通过内网"学习与发展"平台查询和申请这些资源。 | 培训发展 |
+ 
+ ## 公司产品与服务
+ 
+ | 问题 | 回答 | 类别 |
+ |------|------|------|
+ | 公司的主要产品和服务有哪些？ | 我们的核心产品线包括：1)"易贷"系列：个人消费信贷产品；2)"商融通"：小微企业经营贷款；3)"安心保"：保险科技服务；4)"财智管家"：智能财富管理平台；5)"数据魔方"：企业级数据分析服务。各产品详细介绍可在公司内网"产品知识库"查阅，新产品发布会定期举行。 | 产品信息 |
+ | 我们与竞争对手相比有什么优势？ | 相较于行业竞争对手，我们的核心优势在于：1)自主研发的风控引擎，审批效率高于行业平均水平40%；2)AI客服系统，满意度达98%；3)全场景数据分析能力，用户画像精准度行业领先；4)平均获客成本低于行业均值30%；5)用户续借率高于行业平均15个百分点。具体竞品分析报告可在内网"市场分析"专区查阅。 | 市场竞争 |
+ | 客户主要投诉的问题有哪些？如何改进？ | 根据近期客户反馈分析，主要投诉集中在：1)APP响应速度慢（占比28%）；2)客服等待时间长（占比23%）；3)产品费率不透明（占比18%）；4)还款提醒不及时（占比15%）；5)其他问题（占比16%）。针对这些问题，产品部已启动"用户体验提升计划"，包括技术架构优化、客服团队扩编、费率说明页面重设计等多项改进措施。 | 客户服务 | 
```

---

```bash
pandoc 企业知识问答对.md -o 企业知识问答对.docx
```

```
➜  python-project pandoc 企业知识问答对.md -o 企业知识问答对.docx
zsh: command not found: pandoc
➜  python-project
```

---

我已经创建了企业知识问答对的内容，以Markdown格式保存。这个文件包含了公司基本信息、组织架构、员工手册、公司政策与制度、信息技术使用、应急处理、员工发展与培训以及公司产品与服务等方面的问答对。

我尝试将Markdown转换为Word文档，但系统中没有安装pandoc工具。您可以通过以下方式获取Word文档：

1. 安装pandoc工具，然后再次运行转换命令：
```
brew install pandoc
pandoc 企业知识问答对.md -o 企业知识问答对.docx
```

2. 或者使用Microsoft Word直接打开Markdown文件并另存为docx格式

3. 还可以使用在线转换工具，如Convertio、CloudConvert等将Markdown文件转换为Word格式

文件内容已经按照企业知识库的形式组织，包含了公司成立时间、组织架构、员工手册、薪酬福利等企业内部信息，可以直接导入到灵犀平台的知识库中使用。

---

_**User**_

现在补充一个从 0到 1 创建一个技能的台本

大体是这样的：
一个 Prompt 作为识别客户意图：

你是一个专业的客户意图识别系统。你的任务是分析客户输入的文本，并将其归类为以下预定义的意图类型之一：

意图类型：

1. 查询信息 - 客户寻求某种信息或数据
2. 请求帮助 - 客户需要解决问题或获取支持
3. 投诉反馈 - 客户表达不满或负面体验
4. 请求变更 - 客户希望修改或调整已有的设置或安排
5. 咨询建议 - 客户寻求专业意见或建议
6. 表达感谢 - 客户表达积极情绪或感谢
7. 确认信息 - 客户希望验证或确认某些信息
8. 终止服务 - 客户希望结束某项服务
9. 闲聊寒暄 - 客户进行非业务相关的对话
10. 其他 - 不属于以上任何类别的意图

请按照以下格式回复：
[识别出的意图类型]

few-shot 示例：

客户：你好，我想查询我的贷款余额。
输出：查询信息

你的回应应该简洁、准确，只需识别意图而不需要解决客户的问题。

类似这个。 

识别到客户意图之后进行定制化的服务。  我打算就以客户投诉为案例。
第一个 Prompt 识别到客户投诉， 则拉另外一个 Prompt 进行定制化服务。 其他则简单输出"抱歉，无法给您答复"

你补充这个台本

---

_**Assistant**_



---

Cancelled

---



---



---

