<!-- Generated by SpecStory -->

# 查看 Mac 磁盘占用文件 (2025-04-11 11:00:41)

_**User**_

我是 mac 电脑， 我如何查看占用磁盘最多的文件？ 并列举出来？

---

_**Assistant**_



---

要查看占用磁盘最多的文件，你可以使用以下几种方法：

---

```bash
find / -type f -size +100M -exec ls -lh {} \; 2>/dev/null | sort -k5hr | head -n 10
```

```
➜  python-project find / -type f -size +100M -exec ls -lh {} \; 2>/dev/null | sort -k5hr | head -nf
ind / -type f -size +100M -exec ls -lh {} \; 2>/dev/null | sort -k5hr | head -n 10
```

---

这个命令可能需要比较长时间运行并且可能需要权限。以下是几个更实用的命令：

---

```bash
du -h -d 1 ~ | sort -hr | head -n 10
```

```
➜  python-project du -h -d 1 ~ | sort -hr | head -n 10
^C

➜  python-project
```

---

让我们尝试更适合Mac的命令：

---

```bash
du -h -d 2 ~/Desktop | sort -hr | head -n 10
```

```
➜  python-project du -h -d 2 ~/Desktop | sort -hr | head -n 10
 13G    /Users/<USER>/Desktop
5.3G    /Users/<USER>/Desktop/音频素材汇总
4.4G    /Users/<USER>/Desktop/音频素材汇总/声音
3.0G    /Users/<USER>/Desktop/python-project
1.4G    /Users/<USER>/Desktop/obs
1.3G    /Users/<USER>/Desktop/工作
1.0G    /Users/<USER>/Desktop/python-project/venv
624M    /Users/<USER>/Desktop/python-project/data
485M    /Users/<USER>/Desktop/工作/2022
416M    /Users/<USER>/Desktop/obs/xb36ip
➜  python-project
```

---

以下是在Mac上查找占用磁盘空间最大的文件的几种方法：

1. 使用终端查看特定目录下最大的文件夹（已展示Desktop目录）：
```
du -h -d 2 ~/Desktop | sort -hr | head -n 10
```

2. 查找整个系统中大于特定大小的文件（例如超过1GB的文件）：
```
sudo find / -type f -size +1G -exec ls -lh {} \; 2>/dev/null | sort -k5hr
```

3. 查找当前用户目录下的大文件：
```
find ~/ -type f -size +100M -exec ls -lh {} \; 2>/dev/null | sort -k5hr | head -n 20
```

4. 使用macOS内置的"存储管理"功能：
   - 点击苹果菜单 () → "关于本机"
   - 点击"存储空间"
   - 点击"管理"查看详细信息

如果你需要图形化工具，可以考虑安装：
- DaisyDisk
- OmniDiskSweeper
- GrandPerspective

这些工具可以直观显示大文件的分布情况。

---



---

