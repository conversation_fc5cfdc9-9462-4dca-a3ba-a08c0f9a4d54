<!-- Generated by SpecStory -->

# Groovy 不等于运算符用法讨论 (2024-10-10 14:48:02)

_**User**_

groovy 语法， 不等于如何写？ 是!= 吗？

---

_**Assistant**_

您好！在 Groovy 语言中，不等于的写法确实是 `!=`。

以下是一些 Groovy 中常用的比较运算符：

1. 等于：`==`
2. 不等于：`!=`
3. 大于：`>`
4. 小于：`<`
5. 大于等于：`>=`
6. 小于等于：`<=`

示例：

```groovy
def a = 5
def b = 10

if (a != b) {
    println "a 不等于 b"
}
```

另外，Groovy 还提供了一些额外的比较方法：

1. `<=>`: 比较运算符，返回 -1（小于）、0（等于）或 1（大于）
2. `is`: 用于比较对象引用是否相同
3. `in`: 用于检查元素是否在集合中

例如：

```groovy
def x = 5
def y = 10

println(x <=> y)  // 输出: -1

def list = [1, 2, 3, 4, 5]
println(3 in list)  // 输出: true
```

希望这个解释对您有帮助！如果您还有其他问题，请随时询问。

---



---

