<!-- Generated by SpecStory -->

# Docker Deployment for NewsNow Service (2025-04-11 15:10:13)

_**User**_

@https://github.com/ourongxing/newsnow/blob/main/README.zh-CN.md 

帮我用 Docker 部署这个服务

---

_**Assistant**_

我需要查看项目的 Docker 配置信息，来帮你部署这个 NewsNow 服务。

---

<details>
            <summary>Searched codebase "docker-compose.yaml" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---

Read file: /Users/<USER>/Desktop/python-project/GPT-SoVITS/docker-compose.yaml

---

我看这个不是 NewsNow 项目的 docker-compose 文件，让我尝试直接去项目根目录查看文件结构。

---

<details>
            <summary>Listed current directory • **100** results</summary>
        
| Name |
|-------|
| 📁 `Plotting` |
| 📄 `.DS_Store` |
| 📁 `processed_audios` |
| 📁 `downloaded_audios` |
| 📄 `download_and_process_audio.py` |
| 📄 `process_local_audio.py` |
| 📄 `download_audio.py` |
| 📄 `rename_wav.py` |
| 📄 `sip-27-1740962881.2869.wav` |
| 📁 `remove` |
| 📁 `ASRAudio` |
| 📄 `添加标签、RAG、监工.html` |
| 📄 `AI外呼催收系统2.0流程图2.html` |
| 📄 `index3.html` |
| 📄 `催收系统流程图纵向布局.html` |
| 📁 `tool_audios` |
| 📄 `刘毅开场白.WAV` |
| 📄 `output5.wav` |
| 📄 `genAudio.py` |
| 📄 `业务催收思路.md` |
| 📄 `output4.wav` |
| 📄 `催收策略流程图_新风格.html` |
| 📁 `output` |
| 📄 `m1Prompt.py` |
| 📄 `催收策略流程图.html` |
| 📄 `.~灵犀解答记录汇总.xlsx` |
| 📄 `灵犀解答记录汇总.xlsx` |
| 📄 `灵犀解答记录汇总.py` |
| 📄 `灵犀解答记录.md` |
| 📄 `README.md` |
| 📄 `.gitignore` |
| 📄 `requirements.txt` |
| 📁 `projects` |
| 📁 `技术调研` |
| 📁 `docs` |
| 📁 `tests` |
| 📁 `data` |
| 📁 `scripts` |
| 📁 `web` |
| 📄 `agent_demo.py` |
| 📄 `demo.` |
| 📁 `excel` |
| 📄 `m1prompt.txt` |
| 📄 `m1Prompt.md` |
| 📄 `advanced_wechat_work_qa_table.py` |
| 📄 `wechat_work_qa_table.py` |
| 📄 `T0Prompt.md` |
| 📄 `temp.py` |
| 📄 `催收思路.md` |
| 📄 `imagecopy2.png` |
| 📄 `<!-- Generated by SpecStory -->.md` |
| 📄 `灵犀平台使用视频_压缩版.mp4.zip` |
| 📄 `灵犀平台使用视频_压缩版.mp4` |
| 📄 `灵犀平台使用视频.mp4` |
| 📄 `录屏2025-03-19 16.52.54.mov` |
| 📄 `脚本.md` |
| 📄 `企业知识问答对.md` |
| 📄 `企业知识库内容.md` |
| 📄 `灵犀AI场景编排平台使用教程脚本.md` |
| 📁 `__pycache__` |
| 📄 `intent_classification_prompt.md` |
| 📄 `intent_mapping.md` |
| 📁 `.specstory` |
| 📄 `minimax_meeting_notes.md` |
| 📄 `fx.md` |
| 📄 `weekly_report_template.py` |
| 📄 `标签调整报告.md` |
| 📄 `final_tag_check.py` |
| 📄 `manual_adjust_tags.py` |
| 📄 `adjust_tags.py` |
| 📄 `转换说明.md` |
| 📄 `final_check.py` |
| 📄 `refine_converted_file.py` |
| 📄 `check_converted_file.py` |
| 📄 `analyze_new_excel.py` |
| 📁 `excel_analysis_results` |
| 📄 `DeepSeek-R1-Distill-Llama-70B 启动文档及故障排查方案.md` |
| 📄 `webui.py` |
| 📄 `路演.md` |
| 📄 `图片3.png` |
| 📄 `voiceData.json` |
| 📄 `speech.txt` |
| 📄 `生成PPT.command` |
| 📄 `md_to_ppt.py` |
| 📄 `AI催收话术迭代提效-完整PPT内容.md` |
| 📄 `PPT设计建议.md` |
| 📄 `AI催收话术迭代提效.md` |
| 📄 `流程对比图.md` |
| 📄 `test.md` |
| 📁 `~` |
| 📄 `demo.md` |
| 📁 `venv` |
| 📄 `batch_tts.py` |
| 📁 `GPT-SoVITS` |
| 📄 `login_company.sh` |
| 📄 `batch_tts_zxy.py` |
| 📄 `batch_tts copy.py` |
| 📄 `tts_infer.yaml` |
| 📄 `flow.md` |
| 📄 `waihu.md` |

</details>

---

我看不到直接的 NewsNow 项目文件。让我寻找更多信息，看看是否能够获取 NewsNow 的 docker-compose 文件。

---

```bash
git clone https://github.com/ourongxing/newsnow.git
```

```
➜  python-project git clone https://github.com/ourongxing/newsnow.git
Cloning into 'newsnow'...
remote: Enumerating objects: 2847, done.
remote: Counting objects: 100% (28/28), done.
remote: Compressing objects: 100% (17/17), done.
remote: Total 2847 (delta 15), reused 11 (delta 11), pack-reused 2819 (from 2)
Receiving objects: 100% (2847/2847), 4.72 MiB | 1.77 MiB/s, done.
Resolving deltas: 100% (1668/1668), done.
➜  python-project
```

---

很好，我已经克隆了 NewsNow 仓库。让我检查一下其中的 docker-compose.yaml 文件：

---

<details>
            <summary>Listed directory newsnow • **36** results</summary>
        
| Name |
|-------|
| 📁 `.git` |
| 📁 `test` |
| 📁 `tools` |
| 📄 `tsconfig.app.json` |
| 📄 `tsconfig.base.json` |
| 📄 `tsconfig.json` |
| 📄 `tsconfig.node.json` |
| 📄 `uno.config.ts` |
| 📄 `vite.config.ts` |
| 📄 `vitest.config.ts` |
| 📁 `src` |
| 📁 `shared` |
| 📁 `server` |
| 📁 `scripts` |
| 📁 `screenshots` |
| 📁 `public` |
| 📄 `pwa.config.ts` |
| 📄 `pnpm-lock.yaml` |
| 📄 `index.html` |
| 📄 `nitro.config.ts` |
| 📄 `package.json` |
| 📁 `patches` |
| 📄 `LICENSE` |
| 📄 `README.ja-JP.md` |
| 📄 `README.md` |
| 📄 `README.zh-CN.md` |
| 📄 `docker-compose.yml` |
| 📄 `eslint.config.mjs` |
| 📄 `example.env.server` |
| 📄 `example.wrangler.toml` |
| 📁 `.github` |
| 📄 `.gitignore` |
| 📁 `.vscode` |
| 📄 `CONTRIBUTING.md` |
| 📄 `Dockerfile` |
| 📄 `.dockerignore` |

</details>

---

太好了，找到了 docker-compose.yml 和 Dockerfile 文件。让我查看这两个文件的内容：

---

Read file: newsnow/docker-compose.yml

---

Read file: newsnow/Dockerfile

---

Read file: newsnow/example.env.server

---

现在我已经收集了足够的信息，下面是使用 Docker 部署 NewsNow 服务的步骤：

## 使用 Docker 部署 NewsNow

1. 创建一个目录用于部署

```bash
mkdir newsnow-deploy
cd newsnow-deploy
```

2. 创建 docker-compose.yml 文件

```bash
cat > docker-compose.yml << 'EOF'
version: '3'

services:
  newsnow:
    image: ghcr.io/ourongxing/newsnow:latest
    container_name: newsnow
    restart: always
    ports:
      - '4444:4444'
    environment:
      - G_CLIENT_ID=
      - G_CLIENT_SECRET=
      - JWT_SECRET=
      - INIT_TABLE=true
      - ENABLE_CACHE=true
EOF
```

3. 如果需要 GitHub 登录功能，需要配置环境变量：

   - 在 GitHub 创建一个 OAuth 应用
   - 获取 Client ID 和 Client Secret
   - 将这些值填入 docker-compose.yml 文件的对应环境变量中

4. 启动服务

```bash
docker compose up -d
```

5. 访问服务：服务将在 http://localhost:4444 上运行

### 环境变量说明

- `G_CLIENT_ID`：GitHub OAuth 应用的 Client ID
- `G_CLIENT_SECRET`：GitHub OAuth 应用的 Client Secret
- `JWT_SECRET`：JWT 加密密钥，可以使用 Client Secret 或其他安全字符串
- `INIT_TABLE`：首次运行时设置为 true，初始化数据库表结构
- `ENABLE_CACHE`：是否启用缓存功能

### 注意事项

- 首次运行后，建议将 `INIT_TABLE` 设置为 false
- 服务默认在 4444 端口运行，可以根据需要修改端口映射
- 如果不需要 GitHub 登录功能，可以保持 `G_CLIENT_ID` 和 `G_CLIENT_SECRET` 为空

这样就完成了 NewsNow 服务的 Docker 部署。服务会自动获取最新热门新闻并提供优雅的阅读界面。

---



---

