<!-- Generated by SpecStory -->

# 视频压缩与画质保持 (2025-03-19 19:24:43)

_**User**_

我当前目录下， 有一个 灵犀平台使用视频.mp4 文件。
在不影响画质流畅度的前提下， 尽可能压缩视频。 目前视频 1.多个 G 很大。

---

_**Assistant**_



---

首先我来检查这个视频文件的详细信息。

---

<details>
            <summary>Listed current directory • **100** results</summary>
        
| Name |
|-------|
| 📄 `灵犀平台使用视频.mp4` |
| 📄 `录屏2025-03-19 16.52.54.mov` |
| 📄 `从0到1创建技能台本.md` |
| 📄 `脚本.md` |
| 📄 `.DS_Store` |
| 📄 `企业知识问答对.md` |
| 📄 `企业知识库内容.md` |
| 📄 `灵犀AI场景编排平台使用教程脚本.md` |
| 📄 `mermaid_with_annotation.html` |
| 📄 `M12+分层新话术第一版-intentions-manual_副本.xlsx` |
| 📄 `mermaid_with_annotation_react.html` |
| 📄 `mermaid_with_annotation_alternative.html` |
| 📄 `detect_faq.py` |
| 📄 `test_detect_faq.py` |
| 📄 `pre_process.py` |
| 📁 `__pycache__` |
| 📄 `test_pre_process.py` |
| 📄 `intent_mapper.py` |
| 📄 `test_special_cases.py` |
| 📄 `test_updated_intent_mapper.py` |
| 📄 `test_simplified_intent_mapper.py` |
| 📄 `test_intent_mapper.py` |
| 📄 `intent_classification_prompt.md` |
| 📄 `intent_mapping.md` |
| 📄 `话术加口癖.txt` |
| 📄 `<!-- Generated by SpecStory -->.md` |
| 📁 `.specstory` |
| 📄 `minimax_meeting_notes.md` |
| 📄 `fx.md` |
| 📄 `王泽龙工作周报.md` |
| 📄 `weekly_report_template.py` |
| 📄 `M12+分层新话术第一版-intentions-refined.xlsx` |
| 📄 `标签调整报告.md` |
| 📄 `final_tag_check.py` |
| 📄 `manual_adjust_tags.py` |
| 📄 `M12+分层新话术第一版-intentions-final.xlsx` |
| 📄 `adjust_tags.py` |
| 📄 `转换说明.md` |
| 📄 `final_check.py` |
| 📄 `refine_converted_file.py` |
| 📄 `check_converted_file.py` |
| 📄 `M12+分层新话术第一版-intentions.xlsx` |
| 📄 `convert_excel_format.py` |
| 📄 `analyze_new_excel.py` |
| 📄 `M12+新版施压话术20241018--质检已审核.xlsx` |
| 📁 `excel_analysis_results` |
| 📄 `analyze_excel_detailed.py` |
| 📄 `analyze_excel.py` |
| 📄 `M1LY男声-分层新话术第一版-intentions (3).xls` |
| 📄 `genAudio.py` |
| 📄 `minimax生成.wav` |
| 📄 `DeepSeek-R1-Distill-Llama-70B 启动文档及故障排查方案.md` |
| 📄 `webui.py` |
| 📄 `gptsovits_fast_inference.py` |
| 📄 `路演.md` |
| 📄 `ai_tutorial.html` |
| 📄 `index.html` |
| 📄 `ai_human_collaboration_ppt_v2.html` |
| 📄 `ai_human_collaboration_ppt.html` |
| 📄 `ai_human_collaboration.html` |
| 📄 `图片3.png` |
| 📄 `1.html` |
| 📄 `voiceData.json` |
| 📄 `speech.txt` |
| 📄 `ai_monitor.html` |
| 📄 `流程对比图PPT风格版.html` |
| 📄 `流程对比图优化版.html` |
| 📄 `AI催收话术迭代提效_静态版.html` |
| 📄 `AI催收话术迭代提效.html` |
| 📄 `生成PPT.command` |
| 📄 `md_to_ppt.py` |
| 📄 `requirements.txt` |
| 📄 `README.md` |
| 📄 `AI催收话术迭代提效-完整PPT内容.md` |
| 📄 `PPT设计建议.md` |
| 📄 `AI催收话术迭代提效.md` |
| 📄 `流程对比图.md` |
| 📄 `test.md` |
| 📁 `~` |
| 📄 `demo.md` |
| 📁 `venv` |
| 📄 `batch_tts.py` |
| 📄 `m1Prompt.md` |
| 📁 `GPT-SoVITS` |
| 📄 `login_company.sh` |
| 📄 `batch_tts_zxy.py` |
| 📄 `batch_tts copy.py` |
| 📄 `tts_infer.yaml` |
| 📄 `flow.md` |
| 📄 `waihu.md` |
| 📁 `flow_diagrams` |
| 📄 `README_business_flow.md` |
| 📄 `business_flow_diagram.py` |
| 📄 `compress_audio.py` |
| 📄 `凤霞 TTS 音频素材.WAV` |
| 📄 `%E6%B5%81%E6%B0%93%E9%9F%B3%E8%89%B2%E5%8F%82%E8%80%83%E9%9F%B3%E9%A2%91.wav` |
| 📁 `test_audio_folder` |
| 📁 `raycast-compress-audio` |
| 📁 `compressed` |
| 📄 `create_multiple_test_audio.py` |

</details>

---

我发现视频文件"灵犀平台使用视频.mp4"存在，大小为241MB，而不是您提到的几个G。我还看到另一个视频文件"录屏2025-03-19 16.52.54.mov"，大小为70MB。

我们可以使用FFmpeg来压缩视频文件，这是处理视频的强大工具。首先让我们检查是否已安装FFmpeg：

---

```bash
which ffmpeg
```

```
➜  python-project which ffmpeg
/Users/<USER>/Documents/ffmpeg/bin/ffmpeg
➜  python-project
```

---

很好，FFmpeg已经安装。现在让我们获取视频的详细信息，以便选择合适的压缩参数：

---

```bash
ffmpeg -i "灵犀平台使用视频.mp4" -hide_banner
```

```
➜  python-project ffmpeg -i "灵犀平台使用视频.mp4" -hide_banner
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x7fe9c4804340] Unknown cover type: 0x1.
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from '灵犀平台使用视频.mp4':
  Metadata:
    compatible_brands: qt  
    major_brand     : qt  
    minor_version   : 0
    creation_time   : 2025-03-19T11:24:08.000000Z
    modification_time: 2025-03-19T11:26:22.000000Z
    mp4_data_incomplete: false
    encoder         : Lavf58.76.100
  Duration: 00:13:22.76, start: 0.000000, bitrate: 2521 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt7
09, progressive), 1670x1080, 2410 kb/s, 30 fps, 30 tbr, 19200 tbn (default)
    Metadata:
      creation_time   : 2025-03-19T11:24:08.000000Z
      handler_name    : VideoHandler
      vendor_id       : [0][0][0][0]
  Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, 
fltp, 104 kb/s (default)
    Metadata:
      creation_time   : 2025-03-19T11:24:08.000000Z
      handler_name    : SoundHandler
      vendor_id       : [0][0][0][0]
At least one output file must be specified
➜  python-project
```

---

根据获取的信息，我了解到视频的详细信息：
- 分辨率：1670x1080
- 码率：2521 kb/s (视频2410 kb/s，音频104 kb/s)
- 帧率：30 fps
- 视频编码：H.264 (High profile)
- 音频编码：AAC
- 总时长：13分22秒

现在我可以使用FFmpeg进行压缩，在保持画质的同时降低文件大小。我将使用以下策略：
1. 使用更高效的H.264编码（保持兼容性）
2. 稍微降低视频码率
3. 保持原始分辨率以维持画质

下面是压缩命令：

---

```bash
ffmpeg -i "灵犀平台使用视频.mp4" -c:v libx264 -crf 23 -preset medium -c:a aac -b:a 96k "灵犀平台使用视频_压缩版.mp4"
```

```
ffmpeg version 6.0-tessus  https://evermeet.cx/ffmpeg/  Copyright (c) 2000-2023 
the FFmpeg developers
  built with Apple clang version 11.0.0 (clang-1100.0.33.17)
  configuration: --cc=/usr/bin/clang --prefix=/opt/ffmpeg --extra-version=tessus
 --enable-avisynth --enable-fontconfig --enable-gpl --enable-libaom --enable-lib
ass --enable-libbluray --enable-libdav1d --enable-libfreetype --enable-libgsm --
enable-libmodplug --enable-libmp3lame --enable-libmysofa --enable-libopencore-am
rnb --enable-libopencore-amrwb --enable-libopenh264 --enable-libopenjpeg --enabl
e-libopus --enable-librubberband --enable-libshine --enable-libsnappy --enable-l
ibsoxr --enable-libspeex --enable-libtheora --enable-libtwolame --enable-libvids
tab --enable-libvmaf --enable-libvo-amrwbenc --enable-libvorbis --enable-libvpx
--enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs --enable-lib
xvid --enable-libzimg --enable-libzmq --enable-libzvbi --enable-version3 --pkg-c
onfig-flags=--static --disable-ffplay
  libavutil      58.  2.100 / 58.  2.100
  libavcodec     60.  3.100 / 60.  3.100
  libavformat    60.  3.100 / 60.  3.100
  libavdevice    60.  1.100 / 60.  1.100
  libavfilter     9.  3.100 /  9.  3.100
  libswscale      7.  1.100 /  7.  1.100
  libswresample   4. 10.100 /  4. 10.100
  libpostproc    57.  1.100 / 57.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x7fa5e300da80] Unknown cover type: 0x1.
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from '灵犀平台使用视频.mp4':
  Metadata:
    compatible_brands: qt  
    major_brand     : qt  
    minor_version   : 0
    creation_time   : 2025-03-19T11:24:08.000000Z
    modification_time: 2025-03-19T11:26:22.000000Z
    mp4_data_incomplete: false
    encoder         : Lavf58.76.100
  Duration: 00:13:22.76, start: 0.000000, bitrate: 2521 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt7
09, progressive), 1670x1080, 2410 kb/s, 30 fps, 30 tbr, 19200 tbn (default)
    Metadata:
      creation_time   : 2025-03-19T11:24:08.000000Z
      handler_name    : VideoHandler
      vendor_id       : [0][0][0][0]
  Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, 
fltp, 104 kb/s (default)
    Metadata:
      creation_time   : 2025-03-19T11:24:08.000000Z
      handler_name    : SoundHandler
      vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (libx264))
  Stream #0:1 -> #0:1 (aac (native) -> aac (native))
Press [q] to stop, [?] for help
[libx264 @ 0x7fa5b2704f00] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2
[libx264 @ 0x7fa5b2704f00] profile High, level 4.0, 4:2:0, 8-bit
[libx264 @ 0x7fa5b2704f00] 264 - core 164 r3106 eaa68fa - H.264/MPEG-4 AVC codec
 - Copyleft 2003-2023 - http://www.videolan.org/x264.html - options: cabac=1 ref
=3 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=7 psy=1 psy_rd=1.00:0.00 mixed_r
ef=1 me_range=16 chroma_me=1 trellis=1 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=
1 chroma_qp_offset=-2 threads=15 lookahead_threads=2 sliced_threads=0 nr=0 decim
ate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2 b_a
dapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=2 keyint=250 keyint_min=25
 scenecut=40 intra_refresh=0 rc_lookahead=40 rc=crf mbtree=1 crf=23.0 qcomp=0.60
 qpmin=0 qpmax=69 qpstep=4 ip_ratio=1.40 aq=1:1.00
Output #0, mp4, to '灵犀平台使用视频_压缩版.mp4':
  Metadata:
    compatible_brands: qt  
    major_brand     : qt  
    minor_version   : 0
    mp4_data_incomplete: false
    modification_time: 2025-03-19T11:26:22.000000Z
    encoder         : Lavf60.3.100
  Stream #0:0(und): Video: h264 (avc1 / 0x31637661), yuv420p(tv, bt709, progress
ive), 1670x1080, q=2-31, 30 fps, 15360 tbn (default)
    Metadata:
      creation_time   : 2025-03-19T11:24:08.000000Z
      handler_name    : VideoHandler
      vendor_id       : [0][0][0][0]
      encoder         : Lavc60.3.100 libx264
    Side data:
      cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
  Stream #0:1(und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp,
 96 kb/s (default)
    Metadata:
      creation_time   : 2025-03-19T11:24:08.000000Z
      handler_name    : SoundHandler
      vendor_id       : [0][0][0][0]
      encoder         : Lavc60.3.100 aac
frame=    0 fps=0.0 q=0.0 size=       0kB time=00:00:00.27 bitrate=   1.4kbits/s
frame=   15 fps=0.0 q=29.0 size=       0kB time=00:00:02.76 bitrate=   0.1kbits/
frame=   74 fps= 71 q=29.0 size=       0kB time=00:00:04.69 bitrate=   0.1kbits/
frame=  134 fps= 87 q=29.0 size=       0kB time=00:00:06.68 bitrate=   0.1kbits/
frame=  194 fps= 95 q=29.0 size=     256kB time=00:00:08.70 bitrate= 240.9kbits/
frame=  262 fps=102 q=29.0 size=     256kB time=00:00:10.95 bitrate= 191.4kbits/
frame=  339 fps=111 q=29.0 size=     256kB time=00:00:13.53 bitrate= 154.9kbits/
frame=  412 fps=116 q=29.0 size=     256kB time=00:00:15.97 bitrate= 131.3kbits/
frame=  487 fps=119 q=26.0 size=     512kB time=00:00:18.45 bitrate= 227.2kbits/
frame=  546 fps=118 q=29.0 size=     512kB time=00:00:20.43 bitrate= 205.3kbits/
frame=  613 fps=120 q=29.0 size=     768kB time=00:00:22.70 bitrate= 277.1kbits/
frame=  676 fps=120 q=29.0 size=     768kB time=00:00:24.24 bitrate= 259.5kbits/
frame=  742 fps=121 q=29.0 size=     768kB time=00:00:25.98 bitrate= 242.2kbits/
frame=  797 fps=119 q=29.0 size=    1024kB time=00:00:27.79 bitrate= 301.8kbits/
frame=  858 fps=118 q=29.0 size=    1024kB time=00:00:29.83 bitrate= 281.2kbits/
frame=  924 fps=119 q=29.0 size=    1024kB time=00:00:32.02 bitrate= 262.0kbits/
frame=  969 fps=117 q=29.0 size=    1280kB time=00:00:33.57 bitrate= 312.3kbits/
frame= 1032 fps=118 q=29.0 size=    1280kB time=00:00:35.64 bitrate= 294.2kbits/
frame= 1115 fps=120 q=29.0 size=    1280kB time=00:00:38.40 bitrate= 273.0kbits/
frame= 1203 fps=123 q=29.0 size=    1536kB time=00:00:41.35 bitrate= 304.3kbits/
frame= 1297 fps=126 q=29.0 size=    1536kB time=00:00:44.46 bitrate= 283.0kbits/
frame= 1388 fps=128 q=29.0 size=    1536kB time=00:00:47.50 bitrate= 264.9kbits/
frame= 1438 fps=126 q=29.0 size=    1536kB time=00:00:49.20 bitrate= 255.7kbits/
frame= 1507 fps=126 q=29.0 size=    1792kB time=00:00:51.47 bitrate= 285.2kbits/
frame= 1575 fps=127 q=29.0 size=    2048kB time=00:00:53.73 bitrate= 312.3kbits/
frame= 1658 fps=128 q=29.0 size=    2048kB time=00:00:56.54 bitrate= 296.7kbits/
frame= 1702 fps=127 q=29.0 size=    2304kB time=00:00:58.00 bitrate= 325.4kbits/
frame= 1793 fps=129 q=29.0 size=    2304kB time=00:01:01.02 bitrate= 309.3kbits/
frame= 1885 fps=131 q=29.0 size=    2304kB time=00:01:04.06 bitrate= 294.6kbits/
frame= 1979 fps=132 q=29.0 size=    2560kB time=00:01:07.19 bitrate= 312.1kbits/
frame= 2069 fps=134 q=29.0 size=    2560kB time=00:01:10.19 bitrate= 298.8kbits/
frame= 2166 fps=136 q=29.0 size=    2816kB time=00:01:13.44 bitrate= 314.1kbits/
frame= 2249 fps=136 q=29.0 size=    2816kB time=00:01:16.23 bitrate= 302.6kbits/
frame= 2318 fps=136 q=29.0 size=    2816kB time=00:01:18.50 bitrate= 293.8kbits/
frame= 2399 fps=137 q=29.0 size=    3072kB time=00:01:21.20 bitrate= 309.9kbits/
frame= 2478 fps=138 q=29.0 size=    3328kB time=00:01:23.84 bitrate= 325.2kbits/
frame= 2567 fps=139 q=29.0 size=    3584kB time=00:01:26.79 bitrate= 338.3kbits/
frame= 2654 fps=140 q=29.0 size=    3584kB time=00:01:29.69 bitrate= 327.3kbits/
frame= 2727 fps=140 q=29.0 size=    3840kB time=00:01:32.16 bitrate= 341.3kbits/
frame= 2795 fps=139 q=29.0 size=    4096kB time=00:01:34.38 bitrate= 355.5kbits/
frame= 2895 fps=141 q=29.0 size=    4096kB time=00:01:37.73 bitrate= 343.3kbits/
frame= 2992 fps=142 q=29.0 size=    4352kB time=00:01:40.98 bitrate= 353.0kbits/
frame= 3065 fps=142 q=29.0 size=    4352kB time=00:01:43.39 bitrate= 344.8kbits/
frame= 3176 fps=144 q=29.0 size=    4352kB time=00:01:47.11 bitrate= 332.8kbits/
frame= 3285 fps=146 q=29.0 size=    4608kB time=00:01:50.73 bitrate= 340.9kbits/
frame= 3382 fps=147 q=29.0 size=    4608kB time=00:01:53.98 bitrate= 331.2kbits/
frame= 3483 fps=148 q=29.0 size=    4608kB time=00:01:57.33 bitrate= 321.7kbits/
frame= 3584 fps=149 q=26.0 size=    4608kB time=00:02:00.69 bitrate= 312.8kbits/
frame= 3683 fps=150 q=29.0 size=    4864kB time=00:02:04.01 bitrate= 321.3kbits/
frame= 3759 fps=150 q=29.0 size=    4864kB time=00:02:06.54 bitrate= 314.9kbits/
frame= 3846 fps=150 q=29.0 size=    5120kB time=00:02:09.42 bitrate= 324.1kbits/
frame= 3940 fps=151 q=29.0 size=    5376kB time=00:02:12.56 bitrate= 332.2kbits/
frame= 4035 fps=152 q=29.0 size=    5376kB time=00:02:15.72 bitrate= 324.5kbits/
frame= 4137 fps=153 q=29.0 size=    5376kB time=00:02:19.13 bitrate= 316.5kbits/
frame= 4246 fps=154 q=29.0 size=    5632kB time=00:02:22.77 bitrate= 323.1kbits/
frame= 4355 fps=155 q=29.0 size=    5632kB time=00:02:26.40 bitrate= 315.1kbits/
frame= 4444 fps=155 q=29.0 size=    5632kB time=00:02:29.37 bitrate= 308.9kbits/
frame= 4526 fps=155 q=29.0 size=    5888kB time=00:02:32.09 bitrate= 317.1kbits/
frame= 4619 fps=156 q=29.0 size=    5888kB time=00:02:35.20 bitrate= 310.8kbits/
frame= 4702 fps=156 q=29.0 size=    6144kB time=00:02:37.96 bitrate= 318.6kbits/
frame= 4795 fps=157 q=29.0 size=    6144kB time=00:02:41.05 bitrate= 312.5kbits/
frame= 4853 fps=156 q=29.0 size=    6144kB time=00:02:43.00 bitrate= 308.8kbits/
frame= 4936 fps=156 q=29.0 size=    6400kB time=00:02:45.76 bitrate= 316.3kbits/
frame= 5031 fps=156 q=29.0 size=    6400kB time=00:02:48.92 bitrate= 310.4kbits/
frame= 5118 fps=157 q=29.0 size=    6656kB time=00:02:51.85 bitrate= 317.3kbits/
frame= 5199 fps=157 q=29.0 size=    6656kB time=00:02:54.54 bitrate= 312.4kbits/
frame= 5290 fps=157 q=29.0 size=    6912kB time=00:02:57.56 bitrate= 318.9kbits/
frame= 5379 fps=157 q=29.0 size=    7168kB time=00:03:00.53 bitrate= 325.3kbits/
frame= 5464 fps=157 q=29.0 size=    7168kB time=00:03:03.36 bitrate= 320.2kbits/
frame= 5527 fps=157 q=29.0 size=    7424kB time=00:03:05.45 bitrate= 327.9kbits/
frame= 5592 fps=157 q=29.0 size=    7936kB time=00:03:07.64 bitrate= 346.5kbits/
frame= 5692 fps=157 q=29.0 size=    7936kB time=00:03:10.98 bitrate= 340.4kbits/
frame= 5784 fps=158 q=29.0 size=    7936kB time=00:03:14.02 bitrate= 335.1kbits/
frame= 5880 fps=158 q=29.0 size=    8192kB time=00:03:17.27 bitrate= 340.2kbits/
frame= 5977 fps=158 q=29.0 size=    8192kB time=00:03:20.45 bitrate= 334.8kbits/
frame= 6076 fps=159 q=29.0 size=    8448kB time=00:03:23.75 bitrate= 339.7kbits/
frame= 6170 fps=159 q=29.0 size=    8448kB time=00:03:26.88 bitrate= 334.5kbits/
frame= 6250 fps=159 q=29.0 size=    8704kB time=00:03:29.56 bitrate= 340.3kbits/
frame= 6324 fps=159 q=29.0 size=    8704kB time=00:03:32.02 bitrate= 336.3kbits/
frame= 6408 fps=159 q=29.0 size=    8960kB time=00:03:34.83 bitrate= 341.7kbits/
frame= 6489 fps=159 q=29.0 size=    8960kB time=00:03:37.54 bitrate= 337.4kbits/
frame= 6583 fps=160 q=29.0 size=    9216kB time=00:03:40.65 bitrate= 342.1kbits/
frame= 6682 fps=160 q=29.0 size=    9216kB time=00:03:43.97 bitrate= 337.1kbits/
frame= 6767 fps=160 q=29.0 size=    9216kB time=00:03:46.78 bitrate= 332.9kbits/
frame= 6836 fps=160 q=29.0 size=    9472kB time=00:03:49.13 bitrate= 338.6kbits/
frame= 6909 fps=160 q=29.0 size=    9472kB time=00:03:51.54 bitrate= 335.1kbits/
frame= 6995 fps=160 q=29.0 size=    9728kB time=00:03:54.40 bitrate= 340.0kbits/
frame= 7084 fps=160 q=29.0 size=    9984kB time=00:03:57.40 bitrate= 344.5kbits/
frame= 7163 fps=160 q=29.0 size=    9984kB time=00:04:00.00 bitrate= 340.8kbits/
frame= 7233 fps=160 q=29.0 size=   10240kB time=00:04:02.32 bitrate= 346.2kbits/
frame= 7311 fps=160 q=29.0 size=   10496kB time=00:04:04.94 bitrate= 351.0kbits/
frame= 7389 fps=159 q=29.0 size=   10752kB time=00:04:07.52 bitrate= 355.8kbits/
frame= 7471 fps=160 q=29.0 size=   10752kB time=00:04:10.28 bitrate= 351.9kbits/
frame= 7557 fps=160 q=29.0 size=   11008kB time=00:04:13.16 bitrate= 356.2kbits/
frame= 7649 fps=160 q=29.0 size=   11008kB time=00:04:16.20 bitrate= 352.0kbits/
frame= 7738 fps=160 q=29.0 size=   11264kB time=00:04:19.15 bitrate= 356.1kbits/
frame= 7813 fps=160 q=29.0 size=   11520kB time=00:04:21.68 bitrate= 360.6kbits/
frame= 7911 fps=160 q=29.0 size=   11520kB time=00:04:24.93 bitrate= 356.2kbits/
frame= 7989 fps=160 q=29.0 size=   11520kB time=00:04:27.54 bitrate= 352.7kbits/
frame= 8060 fps=160 q=29.0 size=   11776kB time=00:04:29.90 bitrate= 357.4kbits/
frame= 8154 fps=160 q=29.0 size=   11776kB time=00:04:33.02 bitrate= 353.3kbits/
frame= 8234 fps=160 q=29.0 size=   11776kB time=00:04:35.69 bitrate= 349.9kbits/
frame= 8320 fps=160 q=29.0 size=   12032kB time=00:04:38.56 bitrate= 353.8kbits/
frame= 8412 fps=161 q=29.0 size=   12032kB time=00:04:41.65 bitrate= 350.0kbits/
frame= 8504 fps=161 q=29.0 size=   12288kB time=00:04:44.72 bitrate= 353.5kbits/
frame= 8586 fps=161 q=29.0 size=   12288kB time=00:04:47.46 bitrate= 350.2kbits/
frame= 8666 fps=161 q=29.0 size=   12544kB time=00:04:50.13 bitrate= 354.2kbits/
frame= 8750 fps=161 q=29.0 size=   12544kB time=00:04:52.89 bitrate= 350.8kbits/
frame= 8838 fps=161 q=29.0 size=   12800kB time=00:04:55.82 bitrate= 354.5kbits/
frame= 8925 fps=161 q=29.0 size=   12800kB time=00:04:58.72 bitrate= 351.0kbits/
frame= 9003 fps=161 q=29.0 size=   13056kB time=00:05:01.32 bitrate= 354.9kbits/
frame= 9085 fps=161 q=29.0 size=   13312kB time=00:05:04.06 bitrate= 358.6kbits/
frame= 9172 fps=161 q=29.0 size=   13312kB time=00:05:06.96 bitrate= 355.3kbits/
frame= 9250 fps=161 q=29.0 size=   13312kB time=00:05:09.56 bitrate= 352.3kbits/
frame= 9345 fps=161 q=29.0 size=   13568kB time=00:05:12.72 bitrate= 355.4kbits/
frame= 9421 fps=161 q=29.0 size=   13568kB time=00:05:15.28 bitrate= 352.5kbits/
frame= 9502 fps=161 q=29.0 size=   13824kB time=00:05:17.97 bitrate= 356.2kbits/
frame= 9598 fps=161 q=29.0 size=   14080kB time=00:05:21.15 bitrate= 359.2kbits/
frame= 9683 fps=161 q=29.0 size=   14336kB time=00:05:24.01 bitrate= 362.5kbits/
frame= 9749 fps=161 q=29.0 size=   14592kB time=00:05:26.21 bitrate= 366.4kbits/
frame= 9834 fps=161 q=29.0 size=   14848kB time=00:05:29.04 bitrate= 369.7kbits/
frame= 9914 fps=161 q=29.0 size=   14848kB time=00:05:31.72 bitrate= 366.7kbits/
frame= 9995 fps=161 q=29.0 size=   15104kB time=00:05:34.41 bitrate= 370.0kbits/
frame=10078 fps=161 q=29.0 size=   15104kB time=00:05:37.17 bitrate= 367.0kbits/
frame=10153 fps=161 q=29.0 size=   15360kB time=00:05:39.68 bitrate= 370.4kbits/
frame=10237 fps=161 q=29.0 size=   15616kB time=00:05:42.47 bitrate= 373.5kbits/
frame=10318 fps=161 q=29.0 size=   15872kB time=00:05:45.16 bitrate= 376.7kbits/
frame=10388 fps=161 q=29.0 size=   15872kB time=00:05:47.50 bitrate= 374.2kbits/
frame=10456 fps=161 q=29.0 size=   16384kB time=00:05:49.80 bitrate= 383.7kbits/
frame=10539 fps=161 q=29.0 size=   16640kB time=00:05:52.54 bitrate= 386.7kbits/
frame=10626 fps=161 q=29.0 size=   16640kB time=00:05:55.42 bitrate= 383.5kbits/
frame=10713 fps=161 q=29.0 size=   16640kB time=00:05:58.33 bitrate= 380.4kbits/
frame=10793 fps=161 q=29.0 size=   17408kB time=00:06:01.00 bitrate= 395.0kbits/
frame=10878 fps=161 q=29.0 size=   17408kB time=00:06:03.85 bitrate= 391.9kbits/
frame=10950 fps=161 q=29.0 size=   17408kB time=00:06:06.27 bitrate= 389.3kbits/
frame=11025 fps=161 q=29.0 size=   17920kB time=00:06:08.73 bitrate= 398.1kbits/
frame=11115 fps=161 q=29.0 size=   18176kB time=00:06:11.75 bitrate= 400.5kbits/
frame=11218 fps=161 q=29.0 size=   18176kB time=00:06:15.16 bitrate= 396.9kbits/
frame=11318 fps=161 q=29.0 size=   18432kB time=00:06:18.50 bitrate= 398.9kbits/
frame=11410 fps=162 q=29.0 size=   18432kB time=00:06:21.57 bitrate= 395.7kbits/
frame=11479 fps=161 q=29.0 size=   18432kB time=00:06:23.89 bitrate= 393.3kbits/
frame=11548 fps=161 q=29.0 size=   18688kB time=00:06:26.17 bitrate= 396.4kbits/
frame=11617 fps=161 q=29.0 size=   18944kB time=00:06:28.49 bitrate= 399.5kbits/
frame=11689 fps=161 q=29.0 size=   18944kB time=00:06:30.86 bitrate= 397.0kbits/
frame=11776 fps=161 q=29.0 size=   18944kB time=00:06:33.76 bitrate= 394.1kbits/
frame=11868 fps=161 q=29.0 size=   19200kB time=00:06:36.80 bitrate= 396.4kbits/
frame=11937 fps=161 q=29.0 size=   19200kB time=00:06:39.12 bitrate= 394.1kbits/
frame=12002 fps=161 q=29.0 size=   19456kB time=00:06:41.28 bitrate= 397.2kbits/
frame=12077 fps=161 q=29.0 size=   19712kB time=00:06:43.81 bitrate= 399.9kbits/
frame=12169 fps=161 q=29.0 size=   19712kB time=00:06:46.86 bitrate= 396.9kbits/
frame=12262 fps=161 q=29.0 size=   19712kB time=00:06:49.97 bitrate= 393.9kbits/
frame=12345 fps=161 q=29.0 size=   19968kB time=00:06:52.73 bitrate= 396.3kbits/
frame=12425 fps=161 q=29.0 size=   19968kB time=00:06:55.42 bitrate= 393.8kbits/
frame=12514 fps=161 q=29.0 size=   20224kB time=00:06:58.37 bitrate= 396.0kbits/
frame=12594 fps=161 q=29.0 size=   20224kB time=00:07:01.02 bitrate= 393.5kbits/
frame=12678 fps=161 q=29.0 size=   20224kB time=00:07:03.83 bitrate= 390.9kbits/
frame=12765 fps=161 q=29.0 size=   20480kB time=00:07:06.73 bitrate= 393.2kbits/
frame=12861 fps=161 q=29.0 size=   20480kB time=00:07:09.94 bitrate= 390.2kbits/
frame=12949 fps=161 q=29.0 size=   20736kB time=00:07:12.86 bitrate= 392.4kbits/
frame=13037 fps=161 q=29.0 size=   20736kB time=00:07:15.81 bitrate= 389.8kbits/
frame=13127 fps=162 q=29.0 size=   20992kB time=00:07:18.78 bitrate= 391.9kbits/
frame=13215 fps=162 q=29.0 size=   20992kB time=00:07:21.73 bitrate= 389.3kbits/
frame=13292 fps=162 q=29.0 size=   21248kB time=00:07:24.29 bitrate= 391.8kbits/
frame=13373 fps=162 q=29.0 size=   21504kB time=00:07:27.03 bitrate= 394.1kbits/
frame=13442 fps=161 q=29.0 size=   21760kB time=00:07:29.30 bitrate= 396.7kbits/
frame=13493 fps=161 q=29.0 size=   21760kB time=00:07:31.02 bitrate= 395.2kbits/
frame=13547 fps=161 q=29.0 size=   22272kB time=00:07:32.78 bitrate= 403.0kbits/
frame=13623 fps=161 q=29.0 size=   22272kB time=00:07:35.34 bitrate= 400.7kbits/
frame=13709 fps=161 q=29.0 size=   22528kB time=00:07:38.19 bitrate= 402.8kbits/
frame=13790 fps=161 q=29.0 size=   22784kB time=00:07:40.91 bitrate= 404.9kbits/
frame=13880 fps=161 q=29.0 size=   22784kB time=00:07:43.88 bitrate= 402.4kbits/
frame=13956 fps=161 q=29.0 size=   23040kB time=00:07:46.46 bitrate= 404.6kbits/
frame=14010 fps=160 q=29.0 size=   23296kB time=00:07:48.25 bitrate= 407.6kbits/
frame=14101 fps=160 q=29.0 size=   23296kB time=00:07:51.27 bitrate= 404.9kbits/
frame=14180 fps=160 q=29.0 size=   23552kB time=00:07:53.89 bitrate= 407.1kbits/
frame=14233 fps=160 q=29.0 size=   23808kB time=00:07:55.66 bitrate= 410.0kbits/
frame=14307 fps=160 q=29.0 size=   24320kB time=00:07:58.14 bitrate= 416.7kbits/
frame=14385 fps=160 q=29.0 size=   24320kB time=00:08:00.72 bitrate= 414.4kbits/
frame=14459 fps=160 q=29.0 size=   24576kB time=00:08:03.20 bitrate= 416.6kbits/
frame=14539 fps=160 q=29.0 size=   24576kB time=00:08:05.87 bitrate= 414.4kbits/
frame=14620 fps=160 q=29.0 size=   24832kB time=00:08:08.57 bitrate= 416.4kbits/
frame=14705 fps=160 q=29.0 size=   24832kB time=00:08:11.40 bitrate= 414.0kbits/
frame=14787 fps=160 q=29.0 size=   24832kB time=00:08:14.16 bitrate= 411.7kbits/
frame=14868 fps=160 q=29.0 size=   25088kB time=00:08:16.86 bitrate= 413.6kbits/
frame=14927 fps=160 q=29.0 size=   25088kB time=00:08:18.78 bitrate= 412.0kbits/
frame=15000 fps=159 q=29.0 size=   25600kB time=00:08:21.22 bitrate= 418.4kbits/
frame=15069 fps=159 q=29.0 size=   26112kB time=00:08:23.52 bitrate= 424.8kbits/
frame=15144 fps=159 q=29.0 size=   26368kB time=00:08:26.03 bitrate= 426.9kbits/
frame=15202 fps=159 q=29.0 size=   26624kB time=00:08:28.00 bitrate= 429.3kbits/
frame=15274 fps=159 q=26.0 size=   26880kB time=00:08:30.39 bitrate= 431.4kbits/
frame=15361 fps=159 q=29.0 size=   27136kB time=00:08:33.25 bitrate= 433.1kbits/
frame=15445 fps=159 q=29.0 size=   27392kB time=00:08:36.06 bitrate= 434.8kbits/
frame=15531 fps=159 q=29.0 size=   27392kB time=00:08:38.96 bitrate= 432.4kbits/
frame=15626 fps=159 q=29.0 size=   27648kB time=00:08:42.10 bitrate= 433.8kbits/
frame=15721 fps=159 q=29.0 size=   27648kB time=00:08:45.28 bitrate= 431.2kbits/
frame=15800 fps=159 q=29.0 size=   27904kB time=00:08:47.90 bitrate= 433.0kbits/
frame=15886 fps=159 q=29.0 size=   27904kB time=00:08:50.76 bitrate= 430.7kbits/
frame=15973 fps=160 q=29.0 size=   27904kB time=00:08:53.66 bitrate= 428.3kbits/
frame=16052 fps=160 q=29.0 size=   28160kB time=00:08:56.28 bitrate= 430.2kbits/
frame=16131 fps=159 q=29.0 size=   28160kB time=00:08:58.93 bitrate= 428.0kbits/
frame=16223 fps=160 q=29.0 size=   28416kB time=00:09:02.00 bitrate= 429.5kbits/
frame=16308 fps=160 q=29.0 size=   28416kB time=00:09:04.83 bitrate= 427.3kbits/
frame=16394 fps=160 q=29.0 size=   28672kB time=00:09:07.68 bitrate= 428.9kbits/
frame=16478 fps=160 q=29.0 size=   28672kB time=00:09:10.49 bitrate= 426.7kbits/
frame=16575 fps=160 q=29.0 size=   28928kB time=00:09:13.72 bitrate= 428.0kbits/
frame=16667 fps=160 q=29.0 size=   29184kB time=00:09:16.79 bitrate= 429.4kbits/
frame=16771 fps=160 q=29.0 size=   29184kB time=00:09:20.27 bitrate= 426.7kbits/
frame=16865 fps=160 q=29.0 size=   29440kB time=00:09:23.40 bitrate= 428.1kbits/
frame=16963 fps=160 q=29.0 size=   29440kB time=00:09:26.65 bitrate= 425.6kbits/
frame=17053 fps=161 q=29.0 size=   29696kB time=00:09:29.67 bitrate= 427.0kbits/
frame=17137 fps=161 q=29.0 size=   29696kB time=00:09:32.46 bitrate= 425.0kbits/
frame=17219 fps=161 q=29.0 size=   29952kB time=00:09:35.20 bitrate= 426.6kbits/
frame=17320 fps=161 q=29.0 size=   30208kB time=00:09:38.57 bitrate= 427.7kbits/
frame=17413 fps=161 q=29.0 size=   30208kB time=00:09:41.70 bitrate= 425.4kbits/
frame=17507 fps=161 q=29.0 size=   30208kB time=00:09:44.79 bitrate= 423.2kbits/
frame=17606 fps=161 q=29.0 size=   30464kB time=00:09:48.09 bitrate= 424.4kbits/
frame=17710 fps=161 q=29.0 size=   30720kB time=00:09:51.57 bitrate= 425.4kbits/
frame=17799 fps=161 q=29.0 size=   30720kB time=00:09:54.57 bitrate= 423.3kbits/
frame=17869 fps=161 q=29.0 size=   30976kB time=00:09:56.86 bitrate= 425.1kbits/
frame=17951 fps=161 q=29.0 size=   30976kB time=00:09:59.60 bitrate= 423.2kbits/
frame=18022 fps=161 q=29.0 size=   31232kB time=00:10:01.97 bitrate= 425.0kbits/
frame=18113 fps=161 q=29.0 size=   31744kB time=00:10:05.01 bitrate= 429.8kbits/
frame=18197 fps=161 q=29.0 size=   31744kB time=00:10:07.82 bitrate= 427.8kbits/
frame=18280 fps=161 q=29.0 size=   32000kB time=00:10:10.59 bitrate= 429.3kbits/
frame=18374 fps=162 q=29.0 size=   32256kB time=00:10:13.72 bitrate= 430.6kbits/
frame=18467 fps=162 q=29.0 size=   32256kB time=00:10:16.79 bitrate= 428.4kbits/
frame=18554 fps=162 q=29.0 size=   32768kB time=00:10:19.69 bitrate= 433.2kbits/
frame=18622 fps=162 q=29.0 size=   32768kB time=00:10:21.96 bitrate= 431.6kbits/
frame=18704 fps=162 q=29.0 size=   33024kB time=00:10:24.70 bitrate= 433.1kbits/
frame=18800 fps=162 q=29.0 size=   33280kB time=00:10:27.89 bitrate= 434.2kbits/
frame=18896 fps=162 q=29.0 size=   33280kB time=00:10:31.09 bitrate= 432.0kbits/
frame=18986 fps=162 q=29.0 size=   33536kB time=00:10:34.09 bitrate= 433.3kbits/
frame=19031 fps=162 q=29.0 size=   33536kB time=00:10:35.62 bitrate= 432.2kbits/
frame=19122 fps=162 q=29.0 size=   34048kB time=00:10:38.64 bitrate= 436.7kbits/
frame=19202 fps=162 q=29.0 size=   34048kB time=00:10:41.28 bitrate= 434.9kbits/
frame=19282 fps=162 q=29.0 size=   34560kB time=00:10:43.98 bitrate= 439.6kbits/
frame=19390 fps=162 q=29.0 size=   34816kB time=00:10:47.58 bitrate= 440.4kbits/
frame=19482 fps=162 q=29.0 size=   35072kB time=00:10:50.62 bitrate= 441.6kbits/
frame=19586 fps=162 q=29.0 size=   35072kB time=00:10:54.10 bitrate= 439.2kbits/
frame=19685 fps=162 q=29.0 size=   35328kB time=00:10:57.40 bitrate= 440.2kbits/
frame=19771 fps=162 q=29.0 size=   35328kB time=00:11:00.25 bitrate= 438.3kbits/
frame=19867 fps=162 q=29.0 size=   35584kB time=00:11:03.46 bitrate= 439.4kbits/
frame=19974 fps=163 q=29.0 size=   35584kB time=00:11:07.03 bitrate= 437.0kbits/
frame=20062 fps=163 q=29.0 size=   35840kB time=00:11:09.96 bitrate= 438.2kbits/
frame=20143 fps=163 q=29.0 size=   36096kB time=00:11:12.65 bitrate= 439.6kbits/
frame=20229 fps=163 q=29.0 size=   36608kB time=00:11:15.53 bitrate= 443.9kbits/
frame=20310 fps=163 q=29.0 size=   36864kB time=00:11:18.23 bitrate= 445.3kbits/
frame=20388 fps=163 q=29.0 size=   37120kB time=00:11:20.85 bitrate= 446.6kbits/
frame=20465 fps=163 q=29.0 size=   37376kB time=00:11:23.40 bitrate= 448.0kbits/
frame=20556 fps=163 q=29.0 size=   37632kB time=00:11:26.42 bitrate= 449.1kbits/
frame=20658 fps=163 q=29.0 size=   37888kB time=00:11:29.84 bitrate= 449.9kbits/
frame=20755 fps=163 q=29.0 size=   37888kB time=00:11:33.06 bitrate= 447.8kbits/
frame=20855 fps=163 q=29.0 size=   38144kB time=00:11:36.43 bitrate= 448.7kbits/
frame=20962 fps=163 q=29.0 size=   38144kB time=00:11:39.96 bitrate= 446.4kbits/
frame=21070 fps=163 q=29.0 size=   38400kB time=00:11:43.56 bitrate= 447.1kbits/
frame=21149 fps=163 q=29.0 size=   38400kB time=00:11:46.18 bitrate= 445.5kbits/
frame=21227 fps=163 q=29.0 size=   38656kB time=00:11:48.78 bitrate= 446.8kbits/
frame=21292 fps=163 q=29.0 size=   38912kB time=00:11:50.97 bitrate= 448.4kbits/
frame=21389 fps=163 q=29.0 size=   39168kB time=00:11:54.19 bitrate= 449.3kbits/
frame=21486 fps=163 q=29.0 size=   39168kB time=00:11:57.42 bitrate= 447.2kbits/
frame=21567 fps=163 q=29.0 size=   39424kB time=00:12:00.12 bitrate= 448.5kbits/
frame=21640 fps=163 q=29.0 size=   39680kB time=00:12:02.55 bitrate= 449.9kbits/
frame=21730 fps=163 q=29.0 size=   39936kB time=00:12:05.55 bitrate= 450.9kbits/
frame=21801 fps=163 q=29.0 size=   40448kB time=00:12:07.92 bitrate= 455.2kbits/
frame=21898 fps=163 q=29.0 size=   40704kB time=00:12:11.17 bitrate= 456.0kbits/
frame=21994 fps=163 q=29.0 size=   40704kB time=00:12:14.35 bitrate= 454.1kbits/
frame=22088 fps=164 q=29.0 size=   40960kB time=00:12:17.51 bitrate= 455.0kbits/
frame=22173 fps=164 q=29.0 size=   40960kB time=00:12:20.32 bitrate= 453.2kbits/
frame=22240 fps=163 q=29.0 size=   41216kB time=00:12:22.57 bitrate= 454.7kbits/
frame=22307 fps=163 q=29.0 size=   41984kB time=00:12:24.82 bitrate= 461.8kbits/
frame=22390 fps=163 q=29.0 size=   42240kB time=00:12:27.56 bitrate= 462.9kbits/
frame=22455 fps=163 q=29.0 size=   42496kB time=00:12:29.72 bitrate= 464.3kbits/
frame=22526 fps=163 q=29.0 size=   43264kB time=00:12:32.09 bitrate= 471.2kbits/
frame=22593 fps=163 q=29.0 size=   43264kB time=00:12:34.34 bitrate= 469.8kbits/
frame=22653 fps=163 q=29.0 size=   43520kB time=00:12:36.32 bitrate= 471.4kbits/
frame=22730 fps=163 q=29.0 size=   43776kB time=00:12:38.89 bitrate= 472.5kbits/
frame=22821 fps=163 q=29.0 size=   43776kB time=00:12:41.93 bitrate= 470.7kbits/
frame=22896 fps=163 q=29.0 size=   44032kB time=00:12:44.42 bitrate= 471.9kbits/
frame=22962 fps=163 q=29.0 size=   44288kB time=00:12:46.63 bitrate= 473.3kbits/
frame=23046 fps=163 q=29.0 size=   44288kB time=00:12:49.43 bitrate= 471.5kbits/
frame=23109 fps=163 q=29.0 size=   44544kB time=00:12:51.55 bitrate= 472.9kbits/
frame=23174 fps=162 q=29.0 size=   44544kB time=00:12:53.71 bitrate= 471.6kbits/
frame=23263 fps=162 q=29.0 size=   44800kB time=00:12:56.66 bitrate= 472.5kbits/
frame=23339 fps=162 q=29.0 size=   45056kB time=00:12:59.19 bitrate= 473.7kbits/
frame=23403 fps=162 q=29.0 size=   45056kB time=00:13:01.32 bitrate= 472.4kbits/
frame=23500 fps=162 q=29.0 size=   45312kB time=00:13:04.57 bitrate= 473.1kbits/
frame=23608 fps=163 q=29.0 size=   45312kB time=00:13:08.15 bitrate= 471.0kbits/
frame=23703 fps=163 q=29.0 size=   45312kB time=00:13:11.33 bitrate= 469.1kbits/
frame=23804 fps=163 q=29.0 size=   45568kB time=00:13:15.70 bitrate= 469.1kbits/
frame=23922 fps=163 q=29.0 size=   45568kB time=00:13:19.62 bitrate= 466.8kbits/
frame=24081 fps=163 q=-1.0 Lsize=   46751kB time=00:13:22.73 bitrate= 477.1kbits
/s speed=5.45x
video:36556kB audio:9346kB subtitle:0kB other streams:0kB global headers:0kB mux
ing overhead: 1.848928%
[libx264 @ 0x7fa5b2704f00] frame I:98    Avg QP:14.62  size: 83646
[libx264 @ 0x7fa5b2704f00] frame P:6703  Avg QP:18.19  size:  3031
[libx264 @ 0x7fa5b2704f00] frame B:17280 Avg QP:19.81  size:   516
[libx264 @ 0x7fa5b2704f00] consecutive B-frames:  2.8%  3.4%  3.2% 90.6%
[libx264 @ 0x7fa5b2704f00] mb I  I16..4: 56.3% 24.1% 19.6%
[libx264 @ 0x7fa5b2704f00] mb P  I16..4:  1.5%  1.9%  0.4%  P16..4:  1.8%  0.2% 
 0.1%  0.0%  0.0%    skip:94.0%
[libx264 @ 0x7fa5b2704f00] mb B  I16..4:  0.1%  0.1%  0.0%  B16..8:  1.3%  0.1% 
 0.0%  direct: 0.7%  skip:97.7%  L0:52.5% L1:46.1% BI: 1.4%
[libx264 @ 0x7fa5b2704f00] 8x8 transform intra:41.6% inter:56.6%
[libx264 @ 0x7fa5b2704f00] coded y,uvDC,uvAC intra: 11.0% 12.9% 7.3% inter: 0.2%
 0.8% 0.0%
[libx264 @ 0x7fa5b2704f00] i16 v,h,dc,p: 47% 51%  1%  0%
[libx264 @ 0x7fa5b2704f00] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu: 42% 13% 44%  1%  0%  0
%  0%  0%  0%
[libx264 @ 0x7fa5b2704f00] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 31% 32% 18%  3%  3%  3
%  4%  3%  4%
[libx264 @ 0x7fa5b2704f00] i8c dc,h,v,p: 74% 17%  7%  2%
[libx264 @ 0x7fa5b2704f00] Weighted P-Frames: Y:0.3% UV:0.1%
[libx264 @ 0x7fa5b2704f00] ref P L0: 68.4%  8.2% 16.7%  6.6%  0.0%
[libx264 @ 0x7fa5b2704f00] ref B L0: 64.6% 32.8%  2.5%
[libx264 @ 0x7fa5b2704f00] ref B L1: 96.9%  3.1%
[libx264 @ 0x7fa5b2704f00] kb/s:373.07
[aac @ 0x7fa5b27061c0] Qavg: 588.878

```

---

现在让我们检查一下压缩后的文件大小和原文件大小进行对比：

---

```bash
ls -lh "灵犀平台使用视频.mp4" "灵犀平台使用视频_压缩版.mp4"
```

```
➜  python-project ls -lh "灵犀平台使用视频.mp4" "灵犀平台使用视频_压缩版.mp4"
-rw-r--r--  1 <USER>  <GROUP>   241M  3 19 19:26 灵犀平台使用视频.mp4
-rw-r--r--  1 <USER>  <GROUP>    46M  3 19 19:30 灵犀平台使用视频_压缩版.mp4
➜  python-project
```

---

太好了！压缩效果非常明显：

- 原视频大小：241MB
- 压缩后视频大小：46MB

压缩率约为81%（从241MB减少到46MB），这是一个很好的结果。我们成功地将视频大小减少了约195MB，同时保持了良好的画质。

压缩视频使用的主要参数说明：
- `libx264`：使用H.264编码器，这是目前最流行的视频编码标准之一，提供良好的压缩率和兼容性
- `crf 23`：常量速率因子设为23（范围通常是0-51，数值越低质量越高，23是一个很好的平衡点）
- `preset medium`：编码速度和压缩率的平衡选项
- `aac -b:a 96k`：使用AAC音频编码，码率设为96kbps

视频已保存为"灵犀平台使用视频_压缩版.mp4"，您可以播放它检查视频质量。如果您对压缩后的视频质量和大小满意，可以使用这个版本替代原视频。

如果您想进一步优化视频大小和质量的平衡，我可以提供其他参数组合的建议。

---



---

