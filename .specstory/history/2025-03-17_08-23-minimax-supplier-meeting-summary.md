<!-- Generated by SpecStory -->

# Minimax Supplier Meeting Summary (2025-03-17 16:23:19)

_**User**_

0307，Minimax 供应商 会议纪要

Minimax有新模型在灰度
生成的音频前后空音会提交给 Minimax 的算法解决
提供训练素材以外，还需要提供一段 Prompt 音频。(类似 GPT-Sovits 的参考音频) 基于训练素材以及Prompt 音频产出一个voiceId
直接通过录音取声道复刻（类似腾讯喷麦声音录制方法）
5.  minimax 会提供迭代表格 (我们需要补充当前复刻音频、训练语料、以及需要改进点)


润色一下会议纪要， 不改变原意， 通俗易懂

---

_**Assistant**_

我来润色这份会议纪要：

# Minimax供应商会议纪要（2024年3月7日）

## 主要内容：

1. Minimax正在灰度测试新模型
2. 音频生成中出现的前后空音问题将由Minimax算法团队解决
3. 声音复刻流程更新：
   - 除提供训练素材外，还需提供一段参考音频（Prompt音频）
   - 系统将基于训练素材和参考音频生成专属voiceId
   - 支持通过直接录音方式采集声道进行复刻（类似腾讯喷麦录音方法）
4. Minimax将提供迭代反馈表格，我方需填写：
   - 当前复刻音频样本
   - 使用的训练语料
   - 需要改进的具体方面

---

_**User**_

给出.md 文件

---

_**Assistant**_

```diff
- 
+ # Minimax供应商会议纪要（2024年3月7日）
+ 
+ ## 主要内容：
+ 
+ 1. Minimax正在灰度测试新模型
+ 2. 音频生成中出现的前后空音问题将由Minimax算法团队解决
+ 3. 声音复刻流程更新：
+    - 除提供训练素材外，还需提供一段参考音频（Prompt音频）
+    - 系统将基于训练素材和参考音频生成专属voiceId
+    - 支持通过直接录音方式采集声道进行复刻（类似腾讯喷麦录音方法）
+ 4. Minimax将提供迭代反馈表格，我方需填写：
+    - 当前复刻音频样本
+    - 使用的训练语料
+    - 需要改进的具体方面 
```

---

已创建Markdown格式的会议纪要文件。

---



---

