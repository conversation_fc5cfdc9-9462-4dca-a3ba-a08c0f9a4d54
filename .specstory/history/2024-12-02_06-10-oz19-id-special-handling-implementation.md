<!-- Generated by SpecStory -->

# OZ19 ID Special Handling Implementation (2024-12-02 14:10:27)

_**User**_

帮写个OZ19 id 的特殊处理：
overdue正常输出 OZ19.  mob1 跟 正常的就输出 C14


---

_**Assistant**_

我来帮你修改代码，主要是在处理特殊场景的部分添加 OZ19 的逻辑。以下是需要修改的部分：

```python:demo.py
# ... existing code ...

    # 处理特殊场景
    if "Z34" in preId_list and id == "Z34":
        result_id = "Z341"
    elif "Z18" in preId_list and id == "C15":
        result_id = "Z181"
    elif "C15" in preId_list and id == "C15":
        result_id = "Z181"
    elif "Z37" in preId_list and id == "Z37":
        result_id = "Z371"
    elif "B00" in preId_list and id == "B00":
        result_id = "B001"
    elif len(preId_list) == 0 and id == "OZ19":
        result_id = "C1411"
    # 添加 OZ19 的特殊处理
    elif id == "OZ19":
        if group == 'overdue':
            result_id = "OZ19"
        else:
            result_id = "C14"

# ... existing code ...
```

修改说明：
1. 在特殊场景处理部分添加了 OZ19 的判断逻辑
2. 当 group 为 'overdue' 时，保持 OZ19 不变
3. 其他情况（包括 mob1 和默认情况）都输出 C14
4. 这个修改会覆盖后面 base_map 中的映射规则，确保 OZ19 按照我们的要求正确处理

这样修改后，OZ19 的处理逻辑将符合你的要求：
- overdue 组：输出 OZ19
- mob1 组：输出 C14
- 默认组：输出 C14

---



---

