<!-- Generated by SpecStory -->

# 灵犀平台流程图解说明 (2025-03-13 09:30:58)

_**User**_

我想给这张图所画出来的东西做图解。 你帮我想一下文字写什么。

---

_**Assistant**_

<think>用户想要我为图片中所示的内容提供文字图解说明。我需要分析图片内容并提供相应的文字解释。

从图片中我可以看到这是一个名为"灵犀平台"的流程画布界面。左侧有一个红色框标注的工具栏，这可能是用户想要我特别关注的区域。下面我来分析这个工具栏中的各个选项:

左侧工具栏包含:
1. 控制 - 控制面板或控制选项
2. 输入 - 数据输入功能
3. 输出 - 数据输出功能
4. 条件 - 条件判断节点
5. 大模型 - 可能是AI大语言模型相关功能
6. Prompt - AI提示词设置
7. 知识库 - 知识库访问功能
8. 查询FAQ - 常见问题查询
9. 查询文档 - 文档搜索功能
10. 查询数据集 - 数据集查询
11. 调用 - 可能是API调用功能
12. 调用接口 - 第三方接口调用
13. 调用工具 - 工具调用功能
14. 工具 - 工具箱
15. Toolkit - 工具包
16. Webhook - Web钩子功能

右侧主界面显示了一个流程图，看起来是一个对话或AI处理流程。有多个节点连接在一起，包括输入节点、处理节点和输出节点等。右侧还有一个"Prompt模板"的配置面板，用于设置AI提示词。

我需要为用户提供一个简洁明了的文字图解，解释这个流程画布的功能和各个组件的用途。由于用户要求用中文回复，我将用中文提供这个图解。</think># 灵犀平台流程画布功能图解

## 左侧工具栏功能说明：

### 基础组件
- **控制**：流程控制节点，用于管理流程走向
- **输入**：数据输入节点，用于接收用户输入或系统数据
- **输出**：数据输出节点，展示结果或返回数据
- **条件**：条件判断节点，根据条件决定流程分支

### AI能力
- **大模型**：接入AI大模型能力，实现智能对话和内容生成
- **Prompt**：提示词模板配置，优化AI回复质量
- **知识库**：连接知识库资源，提供专业领域知识支持

### 信息检索
- **查询FAQ**：快速检索常见问题及答案
- **查询文档**：搜索文档内容，提取相关信息
- **查询数据集**：从数据集中检索匹配数据

### 系统集成
- **调用**：调用外部服务功能
- **调用接口**：集成第三方API接口
- **调用工具**：使用内置工具和功能
- **工具**：实用工具集合
- **Toolkit**：工具箱，提供扩展功能
- **Webhook**：网络钩子，实现系统间通信

## 中心画布
流程设计区域，通过连接各种节点，可视化设计对话流程和业务逻辑，支持拖拽操作和节点连接。

## 右侧配置面板
节点属性配置区，可设置Prompt模板、输出变量、执行条件等参数，优化流程执行效果。

这个平台允许用户以可视化方式设计AI对话流程，无需编程即可实现复杂的智能交互场景，适合客服、知识问答、流程自动化等多种应用场景。

---

_**User**_

很好， 如你所示， 现在提示词可以在流程画布里的 Prompt 组件使用。

可以在批量测试里通过定义 Prompt 来定义测试标准。


以上我已经在图里面画出来了， 你补充图解需要的内容。

---

_**Assistant**_

<think>根据用户提供的图片和说明，我需要为灵犀平台的提示词功能编写图解内容。图片展示了两个关键区域：

1. 流程画布中的Prompt组件配置区域（右上方红框标注）
2. 批量测试中的Prompt定义区域（右下方红框标注）

用户希望我补充这两个区域的图解说明。我需要详细解释这些区域的功能和用途，特别是关于如何在流程画布中使用Prompt组件以及如何在批量测试中定义测试标准。

我将为这两个重点区域编写清晰的图解说明，使用中文，并保持专业和信息丰富的风格。</think># 灵犀平台提示词功能图解

## 【右上方绿框区域】Prompt组件配置

### Prompt组件功能说明
- **提示词模板**：在流程画布中，Prompt组件允许用户定义与大模型交互的提示词模板
- **变量插入**：支持动态变量插入，格式为`{{变量名}}`，实现提示词的动态生成
- **上下文管理**：可配置上下文保留策略，控制对话历史的传递方式
- **温度设置**：调节模型回复的随机性和创造性，数值越高创造性越强
- **输出格式**：可选文本、JSON等多种输出格式，适应不同应用场景需求
- **系统角色定义**：设置AI助手的角色定位、专业背景和行为准则

## 【右下方绿框区域】批量测试配置

### 批量测试功能说明
- **测试标准化**：通过定义标准Prompt模板创建测试基准
- **场景覆盖**：支持定义多种输入场景，验证模型在不同情境下的表现
- **预期结果设置**：定义期望的输出结果，用于自动化评估回复质量
- **性能分析**：对比不同提示词模板的效果，选择最优方案
- **边界测试**：验证极端情况下的模型响应情况
- **批量验证**：一次性测试多个样本，提高测试效率

## 提示词优化建议

- **简洁明确**：提示词应当清晰表达需求，避免歧义
- **结构化指令**：使用分步骤、分条件的结构提升指令清晰度
- **角色定义**：明确AI应扮演的角色与具备的专业知识背景
- **约束边界**：设定回答的长度、格式、风格等约束条件
- **示例引导**：通过少样本学习(few-shot)提供示例，引导预期输出格式

通过灵犀平台的提示词功能，用户可以在可视化界面中精确控制AI大模型的行为表现，无需编程即可实现提示词工程(Prompt Engineering)的专业应用，大幅提升AI应用开发效率和质量。

---

_**User**_

好的。 现在就是要结合我们的灵犀系统说主题。

我这里的主题差不多这几点：
1.   怎么用灵犀制作一个自己的机器人

2. 提示词小技巧

3. 新的大模型使用认知

基于以上几点我可能写的比较粗糙草率， 我需要你帮我润色， 或者给出其他建议或意见。
最后你输出一个 Markdown。 我会基于你的 Markdown 复制到 PPT 里。

---

_**Assistant**_

<think>我需要基于用户提供的三个主题，为他们的灵犀系统制作一个更加精细、结构化的Markdown内容，用于后续PPT制作。用户已经提到三个主要主题：
1. 如何使用灵犀系统制作自己的机器人
2. 提示词小技巧
3. 新的大模型使用认知

我将为每个主题提供更详细的内容并进行润色，同时确保整个文档结构清晰，便于制作PPT。</think># 手把手教你用AI干活

## 主题一：如何用灵犀打造专属AI助手

### 快速起步
- **简单三步**：选择模板 → 定义能力 → 发布使用
- **零代码搭建**：拖拽式流程设计，所见即所得
- **丰富模板**：客服机器人、知识问答、流程引导等多场景模板

### 核心功能模块
- **对话流程**：可视化设计对话逻辑，实现复杂交互
- **知识库连接**：导入文档、FAQ，赋予AI专业知识
- **API集成**：一键接入内外部系统，实现数据互通
- **角色定制**：精细调教AI人格，打造专属品牌形象

### 场景示例
- **智能客服**：7×24小时响应，自动分流人工
- **行业专家**：医疗咨询、法律顾问、金融分析
- **流程机器人**：订单处理、预约服务、信息收集

## 主题二：提示词工程小技巧

### 结构化提示
- **角色定义**：明确指定AI角色（"作为一名资深金融分析师..."）
- **任务描述**：清晰界定任务边界和期望输出
- **格式规范**：指定回复格式（表格、JSON、分点列举）

### 优化策略
- **层级指令**：将复杂任务分解为步骤（"首先分析...然后评估..."）
- **示例引导**：提供输入-输出示例，引导AI理解预期
- **约束条件**：设定字数、专业程度、受众等边界条件

### 常见问题解决
- **回答不准**：添加专业背景知识，增强指令明确性
- **输出不全**：使用检查清单确保完整性
- **风格不符**：添加风格示例和具体修辞要求

## 主题三：大模型应用新认知

### 能力边界
- **强项领域**：文本分析、逻辑推理、知识整合、创意生成
- **弱项识别**：精确计算、最新信息、专业深度知识
- **协同增强**：AI+人工协作发挥最大价值

### 应用思维转变
- **从"工具"到"助手"**：不只是执行指令，更是思维伙伴
- **从"替代"到"增强"**：不是取代人类，而是扩展人类能力
- **从"通用"到"专精"**：领域特化训练创造更高价值

### 未来展望
- **多模态交互**：文本、图像、语音无缝协作
- **闭环业务流**：从咨询到执行的全流程智能化
- **持续自我进化**：基于反馈不断优化的AI助手

---

## 实践演示：三分钟创建专业领域AI助手

1. **选择模板**：从灵犀模板库选择知识问答模板
2. **导入知识**：上传专业文档，系统自动解析为知识点
3. **配置提示词**：设定专业角色和回答风格
4. **测试优化**：使用批量测试工具验证效果
5. **一键发布**：生成链接或嵌入代码，随时随地访问

## 即刻开始，智启未来

- **免费试用**：立即注册体验灵犀平台全部功能
- **技术支持**：专业团队7×24小时提供技术咨询
- **定制服务**：企业级需求一对一解决方案

---

_**User**_

1。东西太多， 不好展示在 PPT 上。 你需要精简。
2.  我不想调排版以及样式， 我需要你提供的东西能让我一次性粘到一页 PPT 里。

---

_**Assistant**_

<think>用户需求明确：
1. 内容需要精简，适合放在一页PPT里
2. 不想调整排版和样式
3. 希望能一次性粘贴到PPT

我需要大幅精简之前的内容，聚焦最核心的点，同时保持良好的Markdown结构，确保粘贴到PPT后效果良好。我将：
- 减少每个主题的内容
- 简化标题结构
- 减少描述性文字
- 专注于关键词和短句
- 确保整体篇幅适合一页PPT</think># 手把手教你用AI干活

## 主题一：用灵犀制作专属机器人
- 拖拽式流程设计，零代码搭建
- 一键接入知识库，让AI拥有专业知识
- 场景应用：智能客服、行业专家顾问、流程自动化

## 主题二：提示词小技巧
- 角色定义：明确指定AI角色定位
- 任务分解：复杂问题拆解为步骤
- 格式约束：指定输出格式和结构
- 示例引导：提供范例，引导预期输出

## 主题三：大模型使用新认知
- 优势互补：发挥AI强项，弥补AI弱项
- 思维转变：从工具思维到助手思维
- 专业定制：领域特化训练创造更高价值
- 协同增强：人机协作发挥最大效能

灵犀平台让AI落地应用变得简单高效，从构思到上线，一站式解决方案！

---

_**User**_

写一个 HTML 简单排版一下， 就排版左侧， 我右侧有图片了。

---

_**Assistant**_

<think>用户希望我提供一个HTML简单排版，用于放置在PPT的左侧，右侧已经有图片。我需要创建一个简洁的HTML代码，确保文字内容整齐地显示在左侧区域。将保持原有的三个主题，但使用HTML进行更好的布局控制。

我将使用基本的HTML元素如h1, h2, ul, li等，并添加简单的内联CSS样式来控制排版，确保文字在PPT左侧区域内美观展示。</think>```html
<div style="font-family: 'Microsoft YaHei', sans-serif; color: #333; padding: 20px; width: 45%; float: left;">
    <h1 style="color: #0a8f6c; font-size: 24px; margin-bottom: 15px;">手把手教你用AI干活</h1>
    
    <h2 style="color: #0a8f6c; font-size: 18px; margin-top: 20px; margin-bottom: 10px;">主题一：用灵犀制作专属机器人</h2>
    <ul style="margin-left: 15px; padding-left: 10px; list-style-type: circle;">
        <li>拖拽式流程设计，零代码搭建</li>
        <li>一键接入知识库，让AI拥有专业知识</li>
        <li>场景应用：智能客服、行业专家顾问、流程自动化</li>
    </ul>
    
    <h2 style="color: #0a8f6c; font-size: 18px; margin-top: 20px; margin-bottom: 10px;">主题二：提示词小技巧</h2>
    <ul style="margin-left: 15px; padding-left: 10px; list-style-type: circle;">
        <li>角色定义：明确指定AI角色定位</li>
        <li>任务分解：复杂问题拆解为步骤</li>
        <li>格式约束：指定输出格式和结构</li>
        <li>示例引导：提供范例，引导预期输出</li>
    </ul>
    
    <h2 style="color: #0a8f6c; font-size: 18px; margin-top: 20px; margin-bottom: 10px;">主题三：大模型使用新认知</h2>
    <ul style="margin-left: 15px; padding-left: 10px; list-style-type: circle;">
        <li>优势互补：发挥AI强项，弥补AI弱项</li>
        <li>思维转变：从工具思维到助手思维</li>
        <li>专业定制：领域特化训练创造更高价值</li>
        <li>协同增强：人机协作发挥最大效能</li>
    </ul>
    
    <p style="font-style: italic; margin-top: 20px; color: #0a8f6c;">
        灵犀平台让AI落地应用变得简单高效，从构思到上线，一站式解决方案！
    </p>
</div>
```

这段HTML代码设计用于PPT左侧，右侧已有图片。代码具有以下特点：
- 使用绿色主题色彩与您的PPT风格匹配
- 采用微软雅黑字体，提升中文显示效果
- 控制宽度为45%，确保仅占据左侧空间
- 清晰的层级结构，易于阅读
- 简洁的项目符号，突出重点内容

您可以直接将此代码复制到PPT中的HTML插入组件，或转换为图片后插入。

---

_**User**_

你需要给我 HTML 文件

---

_**Assistant**_

<think>用户需要一个完整的HTML文件，而不只是HTML片段。我需要创建一个包含完整结构的HTML文件，包括doctype、head和body标签等。

我将创建一个简单的HTML文件，保持之前的内容和样式，但确保它是一个完整的、可独立运行的HTML文档。</think>我将为您创建一个完整的HTML文件：

---

```diff
- 
+  
```

---

我已经创建了完整的HTML文件 `ai_tutorial.html`。这个文件包含以下特点：

1. 完整的HTML文档结构，包括DOCTYPE、head和body标签
2. 中文字体优化，使用微软雅黑和黑体
3. 左侧内容区域（占45%宽度）和右侧图片区域的布局
4. 绿色主题，与您的PPT风格一致
5. 响应式设计，适合不同尺寸的屏幕

您可以直接将此HTML文件用于您的PPT演示：
1. 在PPT中插入HTML对象（在"插入"选项卡中）
2. 选择这个HTML文件
3. 或者，您也可以将此HTML在浏览器中打开，截图后插入PPT

右侧的图片区域在HTML中是一个占位符，因为您提到PPT中已经有图片了。如果需要调整任何部分，请告诉我。

---

_**User**_

提供一下下载整个 html 的高清图片。@ai_monitor.html  参考这个

---

_**Assistant**_

<think>我需要在ai_tutorial.html中添加类似于ai_monitor.html中的下载图片功能。这个功能使用html2canvas库将HTML内容转换为高清图片，用户可以通过按钮下载。

ai_monitor.html中的下载功能包括：
1. 一个下载按钮
2. 设置面板（可调整缩放比例和图片质量）
3. JavaScript脚本处理下载逻辑

我需要将这些内容添加到ai_tutorial.html文件中，并确保功能正常工作。</think>

---



---

我已经更新了`ai_tutorial.html`文件，添加了高清图片下载功能。现在您可以按照以下步骤获取高清图片：

### 新增功能说明：

1. **下载按钮**：在页面右上角增加了"下载高清图片"按钮
2. **图片设置面板**：点击下载按钮后会显示设置面板，可以调整：
   - 图片缩放比例（默认2倍，更高的值=更高清的图片）
   - 图片质量（0.1-1.0，默认0.9）
3. **高清下载**：点击"保存并下载"按钮后，会生成高清PNG图片并自动下载

### 使用方法：

1. 在浏览器中打开`ai_tutorial.html`文件
2. 点击右上角的"下载高清图片"按钮
3. 根据需要调整缩放比例和图片质量（数值越高，图片越清晰，但文件也越大）
4. 点击"保存并下载"按钮
5. 图片会自动下载到您的计算机上，文件名为"手把手教你用AI干活-当前日期.png"

这种方式可以保证PPT内容完整高清地展示，避免了截图可能导致的质量损失。如果您需要更高清晰度的图片，可以在设置面板中将缩放比例调整到更高的值（如3或4）。

---



---

