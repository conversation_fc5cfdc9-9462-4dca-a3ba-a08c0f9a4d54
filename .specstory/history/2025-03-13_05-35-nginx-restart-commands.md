<!-- Generated by SpecStory -->

# Nginx Restart Commands (2025-03-13 13:35:19)

_**User**_

nginx 重新启动指令

---

_**Assistant**_

<think>这个问题很直接，用户想知道 Nginx 的重新启动指令。根据自定义指令，我需要用中文回答。让我整理一下 Nginx 重启的几种常见方法：

1. 通过 systemd 重启（适用于大多数现代 Linux 发行版）
2. 通过 service 命令重启（适用于一些 Linux 发行版）
3. 直接使用 nginx 命令重启
4. 通过发送信号重启

我将提供这些方法的命令，并简要解释每种方法的用途和适用场景。</think># Nginx 重新启动指令

Nginx 可以通过多种方式重启，以下是几种常用方法：

## 使用 systemd（现代 Linux 系统）

```bash
sudo systemctl restart nginx
```

## 使用 service 命令

```bash
sudo service nginx restart
```

## 直接使用 nginx 命令

```bash
sudo nginx -s reload  # 重新加载配置文件而不停止服务
```

或者彻底重启：

```bash
sudo nginx -s stop    # 先停止
sudo nginx            # 再启动
```

## 通过进程信号

```bash
sudo kill -HUP $(cat /var/run/nginx.pid)  # 发送 HUP 信号重新加载配置
```

## 检查 Nginx 状态

重启后可以检查状态：

```bash
sudo systemctl status nginx
# 或
sudo nginx -t  # 检查配置文件语法
```

选择适合你系统的方法即可，大多数情况下 `systemctl restart nginx` 是最常用的。

---



---

