<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI 外呼催收技术演进：从 1.0 到 2.0 的智能化升级之路</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
          "Helvetica Neue", Arial, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #2c3e50;
        overflow: hidden;
        height: 100vh;
      }

      .presentation-container {
        position: relative;
        width: 100%;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .slide {
        display: none;
        width: 95%;
        max-width: 1200px;
        height: 90vh;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
        overflow-y: auto;
        animation: slideIn 0.6s ease-out;
      }

      .slide.active {
        display: block;
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .slide-content {
        padding: 40px;
        line-height: 1.6;
      }

      h1 {
        font-size: 2.5rem;
        color: #2c3e50;
        text-align: center;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      h2 {
        font-size: 1.8rem;
        color: #34495e;
        text-align: center;
        margin-bottom: 30px;
        font-weight: 400;
      }

      h3 {
        font-size: 1.4rem;
        color: #2c3e50;
        margin-bottom: 15px;
        border-left: 4px solid #667eea;
        padding-left: 15px;
      }

      h4 {
        font-size: 1.2rem;
        color: #34495e;
        margin-bottom: 10px;
        font-weight: 600;
      }

      p {
        margin-bottom: 15px;
        font-size: 1rem;
        color: #2c3e50;
      }

      ul {
        margin-bottom: 20px;
        padding-left: 20px;
      }

      li {
        margin-bottom: 8px;
        font-size: 0.95rem;
        color: #34495e;
      }

      .grid-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin: 30px 0;
      }

      .grid-3 {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        margin: 30px 0;
      }

      .grid-item {
        background: #f8f9fa;
        padding: 25px;
        border-radius: 15px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
      }

      .grid-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      }

      .grid-item.highlight {
        background: linear-gradient(135deg, #667eea20 0%, #764ba220 100%);
        border-color: #667eea;
      }

      .grid-item.success {
        background: linear-gradient(135deg, #27ae6020 0%, #2ecc7120 100%);
        border-color: #27ae60;
      }

      .grid-item.warning {
        background: linear-gradient(135deg, #f39c1220 0%, #e67e2220 100%);
        border-color: #f39c12;
      }

      .grid-item.danger {
        background: linear-gradient(135deg, #e74c3c20 0%, #c0392b20 100%);
        border-color: #e74c3c;
      }

      .svg-container {
        display: flex;
        justify-content: center;
        margin: 30px 0;
        overflow-x: auto;
      }

      .svg-container svg {
        max-width: 100%;
        height: auto;
      }

      /* 导航按钮 */
      .navigation {
        position: fixed;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
        gap: 20px;
        z-index: 1000;
      }

      .nav-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 25px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
      }

      .nav-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
      }

      .nav-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
      }

      .slide-counter {
        background: rgba(255, 255, 255, 0.9);
        color: #2c3e50;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 600;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
      }

      /* 图片模态窗口 */
      .image-modal {
        display: none;
        position: fixed;
        z-index: 2000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(5px);
      }

      .modal-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 90%;
        max-height: 90%;
        border-radius: 10px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
      }

      .modal-content img {
        width: 100%;
        height: auto;
        border-radius: 10px;
      }

      .close-modal {
        position: absolute;
        top: 15px;
        right: 25px;
        color: white;
        font-size: 35px;
        font-weight: bold;
        cursor: pointer;
        z-index: 2001;
      }

      .close-modal:hover {
        opacity: 0.7;
      }

      .modal-caption {
        text-align: center;
        color: white;
        padding: 10px;
        font-size: 16px;
        background: rgba(0, 0, 0, 0.7);
        border-radius: 0 0 10px 10px;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .slide-content {
          padding: 20px;
        }

        h1 {
          font-size: 2rem;
        }

        h2 {
          font-size: 1.4rem;
        }

        .grid-container {
          grid-template-columns: 1fr;
          gap: 20px;
        }

        .grid-3 {
          grid-template-columns: 1fr;
          gap: 15px;
        }

        .navigation {
          bottom: 20px;
          gap: 15px;
        }

        .nav-btn {
          padding: 10px 20px;
          font-size: 14px;
        }
      }

      /* 图片样式 */
      img {
        max-width: 100%;
        height: auto;
        border-radius: 10px;
        cursor: pointer;
        transition: transform 0.3s ease;
      }

      img:hover {
        transform: scale(1.02);
      }

      /* 强调文本样式 */
      strong {
        color: #667eea;
        font-weight: 600;
      }

      /* 代码块样式 */
      code {
        background: #f8f9fa;
        padding: 2px 6px;
        border-radius: 4px;
        font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
        font-size: 0.9em;
        color: #e74c3c;
      }

      /* 表格样式 */
      table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      th,
      td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid #e9ecef;
      }

      th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
      }

      tr:hover {
        background: #f8f9fa;
      }

      .quote-box {
        background: #f8f9fa;
        border-left: 4px solid #667eea;
        padding: 20px;
        margin: 20px 0;
        border-radius: 0 10px 10px 0;
        font-style: italic;
      }
    </style>
  </head>
  <body>
    <div class="presentation-container">
      <!-- 幻灯片 1: 标题页与引言 -->
      <div class="slide active">
        <div class="slide-content">
          <h1>AI 外呼催收技术演进</h1>
          <h2>从 1.0 到 2.0 的智能化升级之路</h2>

          <div class="quote-box">
            <p>
              在金融科技快速发展的今天，AI
              外呼催收技术已成为金融机构提升催收效率、降低运营成本的重要手段。随着大语言模型技术的成熟和应用场景的深化，我们的
              AI 外呼催收系统经历了从 1.0 到 2.0
              的重要技术演进，实现了从"静态预制"到"动态智能"的跨越式升级。
            </p>
          </div>

          <div class="grid-container">
            <div class="grid-item highlight">
              <h3>核心挑战</h3>
              <ul>
                <li>
                  <strong>个性化应对</strong
                  >：不同客户的还款能力、还款意愿差异巨大，需要个性化的催收策略
                </li>
                <li>
                  <strong>话术适配性</strong
                  >：传统预制话术难以覆盖复杂多变的客户回复场景
                </li>
                <li>
                  <strong>质量监控</strong>：如何确保 AI
                  外呼的质量和合规性，及时发现和纠正异常情况
                </li>
                <li>
                  <strong>效果评估</strong
                  >：如何科学评估催收效果，持续优化催收策略
                </li>
                <li>
                  <strong>语音质量</strong
                  >：如何生成高质量、自然流畅的语音，提升客户体验
                </li>
              </ul>
            </div>
            <div class="grid-item success">
              <h3>技术演进路径</h3>
              <ul>
                <li><strong>催收 1.0</strong>：意图识别驱动的预制话术体系</li>
                <li>
                  <strong>催收 2.0</strong>：风格判断 + 话术选择双引擎架构
                </li>
                <li><strong>AI 监工 2.0</strong>：9 维度异常检测与智能告警</li>
                <li><strong>混合 TTS</strong>：静态高质量 + 动态灵活性</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 幻灯片 2: AI 外呼催收流程概览 -->
      <div class="slide">
        <div class="slide-content">
          <h2>AI 外呼催收流程概览</h2>

          <div class="svg-container">
            <img
              src="https://static.zhongan.com/website/aigc/share/时序图.png"
              alt="外呼流程时序图"
              style="
                max-width: 100%;
                height: auto;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
              "
            />
          </div>

          <div class="grid-container">
            <div class="grid-item highlight">
              <h3>核心流程步骤</h3>
              <ol>
                <li>
                  <strong>外呼任务发起</strong
                  >：催收系统根据业务规则和客户信息，生成外呼任务
                </li>
                <li>
                  <strong>CC 外呼中心处理</strong>：接收任务，向客户发起电话呼叫
                </li>
                <li>
                  <strong>客户回复与意图识别</strong>：AI
                  系统基于客户回复进行实时意图识别和分析
                </li>
                <li>
                  <strong>话术选择与播报</strong
                  >：根据识别的客户意图，选择对应的催收话术
                </li>
                <li>
                  <strong>多轮对话交互</strong>：重复步骤 3-4，形成多轮对话交互
                </li>
                <li>
                  <strong>通话结束与小结</strong
                  >：系统进行多维度的对话小结和标签提取
                </li>
              </ol>
            </div>
            <div class="grid-item success">
              <h3>系统协同工作</h3>
              <ul>
                <li>
                  <strong>催收系统</strong
                  >：任务生成，包含客户基本信息、逾期情况、历史催收记录等关键数据
                </li>
                <li>
                  <strong>CC 外呼中心</strong
                  >：建立通话连接，播放核身开场白进行客户身份确认
                </li>
                <li>
                  <strong>AI 意图识别</strong>：实时分析客户回复，识别意图和情绪
                </li>
                <li>
                  <strong>TTS 语音合成</strong>：将话术转换为语音并播报给客户
                </li>
                <li><strong>对话管理</strong>：动态调整催收策略和话术选择</li>
              </ul>
            </div>
          </div>

          <div class="quote-box">
            <p>
              <strong>关键特点</strong>：这一流程在催收 1.0 和 2.0
              中保持基本一致，主要差异在于意图识别和话术选择的实现方式。
            </p>
          </div>
        </div>
      </div>

      <!-- 幻灯片 3: 催收 1.0 技术架构 -->
      <div class="slide">
        <div class="slide-content">
          <h2>催收 1.0 技术架构：从 0 到 1 的突破性建设</h2>

          <div class="quote-box">
            <p>
              催收 1.0 是我们
              <strong>AI 外呼催收系统从 0 到 1 的重要里程碑</strong
              >，标志着传统人工催收向智能化催收的历史性转变。
            </p>
          </div>

          <div class="grid-container">
            <div class="grid-item highlight">
              <h3>核心技术特点</h3>
              <ul>
                <li>
                  <strong>一对一话术映射</strong
                  >：每个意图类别对应一套预制话术，关系固定
                </li>
                <li>
                  <strong>分层流程控制</strong>：通过灵犀平台的 flow
                  控制不同催收阶段（核身层、告知层、施压层）
                </li>
                <li>
                  <strong>大模型意图识别</strong
                  >：主要依靠大语言模型进行客户意图的识别和分类
                </li>
                <li>
                  <strong>FAQ 知识库</strong>：通过 RAG 召回建立 FAQ
                  知识库，作为话术选项的补充
                </li>
                <li>
                  <strong>基础架构搭建</strong
                  >：完成了从无到有的系统架构设计和技术栈选型
                </li>
              </ul>
            </div>
            <div class="grid-item success">
              <h3>意图分类体系</h3>
              <ul>
                <li>
                  <strong>A 类 - 资金困难</strong
                  >：客户表达经济困难，无法按时还款
                </li>
                <li>
                  <strong>B 类 - 承诺还款</strong
                  >：客户明确表示愿意还款，需要确认具体时间和金额
                </li>
                <li>
                  <strong>C 类 - 拖延时间</strong
                  >：客户试图推迟还款，寻找各种理由延期
                </li>
                <li>
                  <strong>D 类 - 拒绝还款</strong>：客户明确拒绝还款或否认债务
                </li>
                <li>
                  <strong>E 类 - 其他情况</strong>：无法归类到上述类别的其他回复
                </li>
              </ul>
            </div>
          </div>

          <div class="grid-3">
            <div class="grid-item warning">
              <h4>意图识别流程</h4>
              <ol>
                <li>语音转文本</li>
                <li>文本预处理</li>
                <li>大模型推理</li>
                <li>话术匹配</li>
              </ol>
            </div>
            <div class="grid-item highlight">
              <h4>历史价值</h4>
              <ul>
                <li>技术突破：从 0 到 1 实现大规模 AI 外呼催收</li>
                <li>流程标准化：建立完整的催收外呼流程和质量标准</li>
                <li>数据积累：为后续版本优化提供宝贵的业务数据</li>
              </ul>
            </div>
            <div class="grid-item danger">
              <h4>技术局限</h4>
              <ul>
                <li>话术灵活性不足</li>
                <li>个性化程度有限</li>
                <li>上下文理解能力弱</li>
                <li>实时优化能力不足</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 幻灯片 4: 多维度小结评估体系 -->
      <div class="slide">
        <div class="slide-content">
          <h2>催收 1.0 多维度小结评估体系</h2>

          <div class="svg-container">
            <img
              src="https://static.zhongan.com/website/aigc/share/小结图.png"
              alt="多维度小结评估"
              style="
                max-width: 100%;
                height: auto;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
              "
            />
          </div>

          <div class="grid-container">
            <div class="grid-item highlight">
              <h3>对话内容分析</h3>
              <ul>
                <li>
                  <strong>对话概述</strong
                  >：生成简洁的对话内容摘要，便于后续催收员快速了解沟通情况
                </li>
                <li>
                  <strong>客户声明</strong
                  >：记录客户是否强调自己从未逾期等关键声明
                </li>
              </ul>

              <h3>客户特征分析</h3>
              <ul>
                <li>
                  <strong>施压敏感点</strong
                  >：识别客户在催收过程中的敏感反应点，为后续策略制定提供参考
                </li>
                <li>
                  <strong>逾期原因分析</strong
                  >：分析客户逾期的具体原因，如资金困难、忘记还款等
                </li>
              </ul>
            </div>
            <div class="grid-item success">
              <h3>还款评估</h3>
              <ul>
                <li>
                  <strong>还款能力评估</strong
                  >：基于客户透露的信息，评估其还款能力等级（高、中、低）
                </li>
                <li>
                  <strong>还款意愿评估</strong
                  >：分析客户的还款意愿强度，为催收策略提供依据
                </li>
                <li>
                  <strong>承诺记录</strong
                  >：记录客户的具体还款承诺，包括承诺金额和时间
                </li>
              </ul>

              <h3>联系信息更新</h3>
              <ul>
                <li><strong>新联系方式</strong>：收集客户提供的最新联系方式</li>
                <li>
                  <strong>沟通时间偏好</strong>：记录客户方便接听电话的时间段
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 幻灯片 5: 催收 2.0 技术突破 -->
      <div class="slide">
        <div class="slide-content">
          <h2>催收 2.0 技术突破：动态话术选择的智能化升级</h2>

          <div class="quote-box">
            <p>
              为了解决催收 1.0 的技术局限，我们开发了催收 2.0
              系统，实现了从"静态预制"到"动态智能"的重要跨越。
            </p>
          </div>

          <div class="svg-container">
            <img
              src="https://static.zhongan.com/website/aigc/share/催收2.0架构图.png"
              alt="催收2.0架构图"
              style="
                max-width: 100%;
                height: auto;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
              "
            />
          </div>

          <div class="grid-container">
            <div class="grid-item highlight">
              <h3>核心技术创新</h3>
              <p>
                催收 2.0 的核心创新在于<strong
                  >风格判断 Agent + 话术选择 Agent 的双引擎架构</strong
                >：
              </p>

              <h4>1. 风格判断 Agent</h4>
              <ul>
                <li>
                  <strong>九宫格评估机制</strong>：还款能力 ×
                  还款意愿（各分高、中、低三个等级）
                </li>
                <li>
                  <strong>风格选择策略</strong>：
                  <ul>
                    <li>施压风格：适用于能力强、意愿弱的客户</li>
                    <li>共情风格：适用于能力弱、意愿强的客户</li>
                    <li>通用风格：适用于其他情况</li>
                  </ul>
                </li>
              </ul>
            </div>
            <div class="grid-item success">
              <h4>2. 话术选择 Agent</h4>
              <ul>
                <li>
                  <strong>话术包分类体系</strong
                  >：根据客户意图大类匹配对应话术包
                </li>
                <li>
                  <strong>动态话术选项构建</strong>：
                  <ul>
                    <li>基础话术包：根据意图类别匹配</li>
                    <li>FAQ 内容整合：通过 RAG 召回相关内容</li>
                    <li>选项组合：基于客户标签和风格判断构建</li>
                  </ul>
                </li>
              </ul>

              <h4>意图大类识别</h4>
              <ul>
                <li>承诺还款</li>
                <li>敷衍/拒绝沟通</li>
                <li>拒绝还款</li>
                <li>协商还款</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 幻灯片 6: RAG 召回机制与智能结束规则 -->
      <div class="slide">
        <div class="slide-content">
          <h2>RAG 召回机制与智能结束规则判断</h2>

          <div class="grid-container">
            <div class="grid-item highlight">
              <h3>RAG 召回机制</h3>

              <h4>召回流程</h4>
              <ol>
                <li><strong>客户情况分析</strong>：根据客户最新回复内容</li>
                <li>
                  <strong>相关 FAQ 检索</strong
                  >：从知识库中检索与客户情况最相关的 FAQ 内容
                </li>
                <li>
                  <strong>内容整合</strong>：将召回的 FAQ
                  内容与话术模板进行智能整合
                </li>
              </ol>

              <h4>知识库构建</h4>
              <ul>
                <li>
                  <strong>FAQ 分类</strong
                  >：按照客户问题类型、催收阶段等维度进行分类
                </li>
                <li>
                  <strong>内容更新</strong
                  >：基于实际催收效果和客户反馈持续更新知识库
                </li>
                <li>
                  <strong>质量控制</strong>：建立 FAQ 内容的质量评估和审核机制
                </li>
              </ul>
            </div>
            <div class="grid-item success">
              <h3>智能结束规则判断</h3>

              <h4>结束条件</h4>
              <ul>
                <li>
                  <strong>承诺还款</strong>：客户明确承诺具体的还款时间和金额
                </li>
                <li>
                  <strong>轮次限制</strong>：对话轮次超过 8 轮，避免过度催收
                </li>
                <li>
                  <strong>客户明确拒绝</strong>：客户明确表示拒绝沟通或还款
                </li>
                <li><strong>系统异常</strong>：检测到系统异常或通话质量问题</li>
              </ul>

              <h4>智能判断逻辑</h4>
              <ul>
                <li>基于对话内容和客户回复，智能判断是否满足结束条件</li>
                <li>结合客户情绪分析，避免在客户情绪激动时强行继续</li>
                <li>考虑催收效果和客户体验的平衡</li>
              </ul>
            </div>
          </div>

          <div class="grid-3">
            <div class="grid-item warning">
              <h4>个性化程度大幅提升</h4>
              <ul>
                <li>基于九宫格评估的个性化风格选择</li>
                <li>动态话术构建，适应不同客户特征</li>
                <li>实时调整催收策略，提升催收效果</li>
              </ul>
            </div>
            <div class="grid-item highlight">
              <h4>话术选择灵活性显著增强</h4>
              <ul>
                <li>从固定预制话术转向动态话术选项构建</li>
                <li>RAG 召回机制丰富可选话术内容</li>
                <li>多风格话术包，覆盖更多催收场景</li>
              </ul>
            </div>
            <div class="grid-item success">
              <h4>上下文理解能力提升</h4>
              <ul>
                <li>充分利用对话历史信息进行决策</li>
                <li>考虑客户情绪和反馈，调整催收策略</li>
                <li>基于客户标签和历史记录进行个性化处理</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 幻灯片 7: AI 语音生成技术 -->
      <div class="slide">
        <div class="slide-content">
          <h2>AI 语音生成技术：静态与动态 TTS 的智能化应用</h2>

          <div class="quote-box">
            <p>
              在 AI
              外呼催收系统中，高质量的语音生成是提升客户体验的关键技术。我们采用了<strong
                >静态 TTS + 动态 TTS</strong
              >
              的混合架构，实现了语音质量与生成效率的最佳平衡。
            </p>
          </div>

          <div class="grid-container">
            <div class="grid-item highlight">
              <h3>静态 TTS：高质量预生成语音</h3>

              <h4>技术特点</h4>
              <ul>
                <li>
                  <strong>本地模型优势</strong>：使用
                  GPT-SoVITS、CosyVoice、F5-TTS 等开源 TTS 模型
                </li>
                <li>
                  <strong>音质优势</strong>：本地 TTS
                  模型生成的语音拟人度更高，音质更自然
                </li>
                <li>
                  <strong>预生成策略</strong
                  >：针对不包含变量的固定话术，提前批量生成音频文件
                </li>
                <li>
                  <strong>存储优化</strong
                  >：预生成的音频文件可重复使用，减少实时计算压力
                </li>
              </ul>

              <h4>应用场景</h4>
              <ul>
                <li>不带变量的固定话术</li>
              </ul>
            </div>
            <div class="grid-item success">
              <h3>动态 TTS：实时变量语音合成</h3>

              <h4>技术特点</h4>
              <ul>
                <li>
                  <strong>供应商 TTS</strong>：使用云端 TTS
                  服务，保证并发处理能力
                </li>
                <li>
                  <strong>变量支持</strong
                  >：支持姓名、还款金额、逾期天数等动态变量
                </li>
                <li>
                  <strong>实时生成</strong
                  >：在外呼前根据客户信息实时合成个性化语音
                </li>
                <li>
                  <strong>并发优化</strong
                  >：云端服务具备足够的并发处理能力，满足大规模外呼需求
                </li>
              </ul>

              <h4>应用场景</h4>
              <ul>
                <li>含变量的话术</li>
              </ul>
            </div>
          </div>

          <div class="grid-3">
            <div class="grid-item warning">
              <h4>语音表现优化策略</h4>
              <p><strong>口癖添加技术</strong>：</p>
              <ul>
                <li>语气词添加："嗯"、"啊"、"那个"等</li>
                <li>停顿优化：通过标点符号控制语音停顿节奏</li>
                <li>语调调整：根据话术情感色彩调整语音语调和语速</li>
                <li>口语化改写：将书面化表达转换为口语习惯表达</li>
              </ul>
            </div>
            <div class="grid-item highlight">
              <h4>示例对比</h4>
              <p><strong>原始话术</strong>：</p>
              <code
                >提醒您，公司等不了太久。今天还没处理，你的资料就要被上级部门调走。可能会采取其他手段来追偿，那边没那么好说话。</code
              >

              <p><strong>优化后音频</strong>：</p>
              <audio
                src="https://static.zhongan.com/website/aigc/share/添加口癖音频.wav"
                controls
                style="width: 100%; margin-top: 10px"
              ></audio>
            </div>
            <div class="grid-item success">
              <h4>混合架构优势</h4>
              <ul>
                <li>质量与效率并重</li>
                <li>成本最优化配置</li>
                <li>场景化智能选择</li>
                <li>用户体验提升</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 幻灯片 8: AI 监工 2.0 系统 -->
      <div class="slide">
        <div class="slide-content">
          <h2>AI 监工 2.0 系统：9 维度异常检测与智能告警</h2>

          <div class="quote-box">
            <p>
              AI 监工 2.0 是我们质量保障体系的核心组件，通过<strong
                >9 维度异常检测</strong
              >实现对 AI 外呼质量的全量监控，确保催收过程的合规性和有效性。
            </p>
          </div>

          <div class="svg-container">
            <img
              src="https://static.zhongan.com/website/aigc/share/监工架构图.png"
              alt="AI监工2.0架构图"
              style="
                max-width: 100%;
                height: auto;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
              "
            />
          </div>

          <div class="grid-container">
            <div class="grid-item highlight">
              <h3>9 维度异常检测</h3>

              <h4>系统性能异常</h4>
              <ul>
                <li><strong>响应超时</strong>：AI 响应时间 ≥ 3 秒</li>
                <li><strong>系统报错</strong>：检测到系统错误或异常</li>
                <li><strong>异常挂断</strong>：非正常结束的通话</li>
              </ul>

              <h4>对话质量异常</h4>
              <ul>
                <li><strong>轮次过少</strong>：对话轮次 ≤ 4 轮</li>
                <li><strong>通时过长</strong>：通话时长 ≥ 5 分钟</li>
                <li><strong>话术不匹配</strong>：话术与客户意图不符</li>
              </ul>

              <h4>AI 决策异常</h4>
              <ul>
                <li><strong>意图分类错误</strong>：客户意图识别错误</li>
                <li><strong>话术非最优</strong>：话术选择不是最佳选项</li>
                <li><strong>话术评分过低</strong>：话术质量评分低于阈值</li>
              </ul>
            </div>
            <div class="grid-item success">
              <h3>智能告警机制</h3>

              <h4>实时监控</h4>
              <ul>
                <li><strong>全量监控</strong>：对所有外呼通话进行实时监控</li>
                <li>
                  <strong>异常识别</strong>：基于 9 维度指标实时识别异常情况
                </li>
                <li>
                  <strong>智能分析</strong>：利用大语言模型分析异常原因和影响
                </li>
              </ul>

              <h4>告警推送</h4>
              <ul>
                <li>
                  <strong>企业微信告警</strong>：异常情况实时推送到企业微信群
                </li>
                <li><strong>分级告警</strong>：根据异常严重程度进行分级处理</li>
                <li>
                  <strong>详细信息</strong
                  >：包含异常类型、影响范围、建议处理方案
                </li>
              </ul>

              <h4>闭环管理</h4>
              <ul>
                <li><strong>问题跟踪</strong>：建立异常问题的跟踪和处理机制</li>
                <li><strong>效果评估</strong>：评估异常处理的效果和改进情况</li>
                <li><strong>持续优化</strong>：基于监控数据持续优化系统性能</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 幻灯片 9: 技术对比与效果分析 -->
      <div class="slide">
        <div class="slide-content">
          <h2>技术对比与效果分析</h2>

          <table>
            <thead>
              <tr>
                <th>对比维度</th>
                <th>催收 1.0</th>
                <th>催收 2.0</th>
                <th>提升效果</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><strong>话术选择机制</strong></td>
                <td>一对一固定映射</td>
                <td>双引擎动态选择</td>
                <td>灵活性大幅提升</td>
              </tr>
              <tr>
                <td><strong>个性化程度</strong></td>
                <td>基于意图分类</td>
                <td>九宫格评估机制</td>
                <td>个性化精准度提升</td>
              </tr>
              <tr>
                <td><strong>上下文理解</strong></td>
                <td>单轮意图识别</td>
                <td>多轮对话理解</td>
                <td>上下文连贯性增强</td>
              </tr>
              <tr>
                <td><strong>话术丰富度</strong></td>
                <td>预制话术库</td>
                <td>RAG 召回 + 动态构建</td>
                <td>话术覆盖面扩大</td>
              </tr>
              <tr>
                <td><strong>质量监控</strong></td>
                <td>人工抽检</td>
                <td>AI 监工 2.0 全量监控</td>
                <td>监控效率和准确性提升</td>
              </tr>
              <tr>
                <td><strong>语音质量</strong></td>
                <td>单一 TTS 方案</td>
                <td>静态 + 动态混合 TTS</td>
                <td>语音质量和效率平衡</td>
              </tr>
            </tbody>
          </table>

          <div class="grid-container">
            <div class="grid-item success">
              <h3>核心技术成果</h3>
              <ul>
                <li>
                  <strong>从 0 到 1 的突破</strong>：催收 1.0 实现了 AI
                  外呼催收的基础架构建设
                </li>
                <li>
                  <strong>智能化升级</strong>：催收 2.0
                  实现了从"静态预制"到"动态智能"的跨越
                </li>
                <li>
                  <strong>质量保障体系</strong>：AI 监工 2.0
                  建立了完善的质量监控和异常告警机制
                </li>
                <li>
                  <strong>语音技术优化</strong>：混合 TTS
                  架构实现了质量与效率的最佳平衡
                </li>
              </ul>
            </div>
            <div class="grid-item highlight">
              <h3>业务价值体现</h3>
              <ul>
                <li>
                  <strong>催收效率提升</strong
                  >：个性化策略提升催收针对性和成功率
                </li>
                <li>
                  <strong>运营成本降低</strong>：AI
                  全量监控替代人工抽检，降低质量管理成本
                </li>
                <li>
                  <strong>客户体验改善</strong
                  >：基于客户特征的个性化应对，提升客户满意度
                </li>
                <li>
                  <strong>合规风险控制</strong
                  >：实时异常检测和告警，确保催收过程合规性
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 幻灯片 10: 总结与展望 -->
      <div class="slide">
        <div class="slide-content">
          <h2>总结与展望</h2>

          <div class="quote-box">
            <p>
              通过 AI
              外呼催收技术的持续演进，我们实现了从传统人工催收到智能化催收的重要转变，为金融机构提供了高效、合规、个性化的催收解决方案。
            </p>
          </div>

          <div class="grid-container">
            <div class="grid-item highlight">
              <h3>技术演进总结</h3>

              <h4>催收 1.0：奠定基础</h4>
              <ul>
                <li>从 0 到 1 实现 AI 外呼催收系统</li>
                <li>建立完整的技术架构和业务流程</li>
                <li>积累宝贵的业务数据和经验</li>
              </ul>

              <h4>催收 2.0：智能升级</h4>
              <ul>
                <li>双引擎架构实现动态话术选择</li>
                <li>九宫格评估机制提升个性化程度</li>
                <li>RAG 召回机制丰富话术内容</li>
              </ul>

              <h4>AI 监工 2.0：质量保障</h4>
              <ul>
                <li>9 维度异常检测全量监控</li>
                <li>实时告警和闭环管理</li>
                <li>确保催收质量和合规性</li>
              </ul>
            </div>
            <div class="grid-item success">
              <h3>未来发展方向</h3>

              <h4>技术优化</h4>
              <ul>
                <li>
                  <strong>多模态融合</strong
                  >：结合语音情感分析，提升客户情绪识别能力
                </li>
                <li>
                  <strong>强化学习</strong
                  >：基于催收效果反馈，持续优化话术选择策略
                </li>
                <li>
                  <strong>知识图谱</strong
                  >：构建客户关系和行为知识图谱，提升个性化精度
                </li>
              </ul>

              <h4>业务拓展</h4>
              <ul>
                <li>
                  <strong>场景扩展</strong
                  >：从催收扩展到客户服务、营销等更多业务场景
                </li>
                <li>
                  <strong>行业适配</strong>：针对不同行业特点，定制化 AI
                  外呼解决方案
                </li>
                <li>
                  <strong>智能化升级</strong>：向更高级的 AI Agent 方向发展
                </li>
              </ul>

              <h4>生态建设</h4>
              <ul>
                <li>
                  <strong>开放平台</strong>：构建 AI 外呼能力的开放平台和生态
                </li>
                <li>
                  <strong>标准制定</strong>：参与行业标准制定，推动技术规范化
                </li>
                <li><strong>产学研合作</strong>：加强与高校和研究机构的合作</li>
              </ul>
            </div>
          </div>

          <div class="svg-container">
            <svg width="800" height="200" viewBox="0 0 800 200">
              <!-- 未来发展路线图 -->
              <defs>
                <linearGradient
                  id="futureGradient"
                  x1="0%"
                  y1="0%"
                  x2="100%"
                  y2="0%"
                >
                  <stop
                    offset="0%"
                    style="stop-color: #667eea; stop-opacity: 0.8"
                  />
                  <stop
                    offset="50%"
                    style="stop-color: #764ba2; stop-opacity: 0.8"
                  />
                  <stop
                    offset="100%"
                    style="stop-color: #f093fb; stop-opacity: 0.8"
                  />
                </linearGradient>
              </defs>

              <!-- 发展路径 -->
              <path
                d="M 50 100 Q 200 50 400 100 T 750 100"
                stroke="url(#futureGradient)"
                stroke-width="4"
                fill="none"
              />

              <!-- 关键节点 -->
              <g transform="translate(150, 100)">
                <circle cx="0" cy="0" r="8" fill="#667eea" />
                <text
                  x="0"
                  y="-25"
                  text-anchor="middle"
                  fill="#2c3e50"
                  font-size="12"
                  font-weight="bold"
                >
                  多模态融合
                </text>
              </g>

              <g transform="translate(400, 100)">
                <circle cx="0" cy="0" r="8" fill="#764ba2" />
                <text
                  x="0"
                  y="-25"
                  text-anchor="middle"
                  fill="#2c3e50"
                  font-size="12"
                  font-weight="bold"
                >
                  智能 Agent
                </text>
              </g>

              <g transform="translate(650, 100)">
                <circle cx="0" cy="0" r="8" fill="#f093fb" />
                <text
                  x="0"
                  y="-25"
                  text-anchor="middle"
                  fill="#2c3e50"
                  font-size="12"
                  font-weight="bold"
                >
                  生态平台
                </text>
              </g>

              <text
                x="400"
                y="170"
                text-anchor="middle"
                fill="#2c3e50"
                font-size="16"
                font-weight="bold"
              >
                持续创新，引领 AI 外呼技术发展
              </text>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- 导航控制 -->
    <div class="navigation">
      <button class="nav-btn" id="prevBtn" onclick="changeSlide(-1)">
        上一页
      </button>
      <div class="slide-counter">
        <span id="currentSlide">1</span> / <span id="totalSlides">10</span>
      </div>
      <button class="nav-btn" id="nextBtn" onclick="changeSlide(1)">
        下一页
      </button>
    </div>

    <!-- 图片模态窗口 -->
    <div id="imageModal" class="image-modal">
      <span class="close-modal" onclick="closeModal()">&times;</span>
      <div class="modal-content">
        <img id="modalImage" src="" alt="" />
        <div class="modal-caption" id="modalCaption"></div>
      </div>
    </div>

    <script>
      let currentSlideIndex = 0;
      const slides = document.querySelectorAll(".slide");
      const totalSlides = slides.length;

      document.getElementById("totalSlides").textContent = totalSlides;

      function showSlide(index) {
        slides.forEach((slide) => slide.classList.remove("active"));
        slides[index].classList.add("active");

        document.getElementById("currentSlide").textContent = index + 1;
        document.getElementById("prevBtn").disabled = index === 0;
        document.getElementById("nextBtn").disabled = index === totalSlides - 1;
      }

      function changeSlide(direction) {
        const newIndex = currentSlideIndex + direction;
        if (newIndex >= 0 && newIndex < totalSlides) {
          currentSlideIndex = newIndex;
          showSlide(currentSlideIndex);
        }
      }

      // 键盘导航
      document.addEventListener("keydown", function (event) {
        if (event.key === "ArrowLeft") {
          changeSlide(-1);
        } else if (event.key === "ArrowRight") {
          changeSlide(1);
        } else if (event.key === "Escape") {
          closeModal();
        }
      });

      // 触摸手势支持
      let touchStartX = 0;
      let touchEndX = 0;

      document.addEventListener("touchstart", function (event) {
        touchStartX = event.changedTouches[0].screenX;
      });

      document.addEventListener("touchend", function (event) {
        touchEndX = event.changedTouches[0].screenX;
        handleSwipe();
      });

      function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
          if (diff > 0) {
            changeSlide(1); // 向左滑动，下一页
          } else {
            changeSlide(-1); // 向右滑动，上一页
          }
        }
      }

      // 图片点击放大
      document.addEventListener("click", function (event) {
        if (event.target.tagName === "IMG" && event.target.src) {
          openModal(event.target.src, event.target.alt);
        }
      });

      function openModal(src, caption) {
        const modal = document.getElementById("imageModal");
        const modalImg = document.getElementById("modalImage");
        const modalCaption = document.getElementById("modalCaption");

        modal.style.display = "block";
        modalImg.src = src;
        modalCaption.textContent = caption;
      }

      function closeModal() {
        document.getElementById("imageModal").style.display = "none";
      }

      // 点击模态窗口背景关闭
      document
        .getElementById("imageModal")
        .addEventListener("click", function (event) {
          if (event.target === this) {
            closeModal();
          }
        });

      // 初始化
      showSlide(0);
    </script>
  </body>
</html>
