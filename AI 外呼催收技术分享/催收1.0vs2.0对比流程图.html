<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>催收1.0 vs 2.0对比流程图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>催收1.0 vs 2.0技术架构对比</h1>
        
        <svg width="1150" height="700" viewBox="0 0 1150 700" xmlns="http://www.w3.org/2000/svg">
            <!-- 定义样式 -->
            <defs>
                <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.2" />
                </filter>
                
                <linearGradient id="v1Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color: #ffebee; stop-opacity: 1" />
                    <stop offset="100%" style="stop-color: #ffcdd2; stop-opacity: 0.8" />
                </linearGradient>
                
                <linearGradient id="v2Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color: #e8f5e9; stop-opacity: 1" />
                    <stop offset="100%" style="stop-color: #c8e6c9; stop-opacity: 0.8" />
                </linearGradient>
            </defs>

            <!-- 标题 -->
            <text x="575" y="30" font-size="22" text-anchor="middle" font-weight="bold" fill="#2c3e50">技术架构演进对比</text>

            <!-- 催收1.0区域 -->
            <rect x="50" y="60" width="500" height="580" rx="15" fill="url(#v1Gradient)" stroke="#e57373" stroke-width="3" filter="url(#shadow)" />
            <text x="300" y="90" font-size="18" text-anchor="middle" font-weight="bold" fill="#c62828">催收1.0：预制话术模式</text>

            <!-- 催收2.0区域 -->
            <rect x="600" y="60" width="500" height="580" rx="15" fill="url(#v2Gradient)" stroke="#66bb6a" stroke-width="3" filter="url(#shadow)" />
            <text x="850" y="90" font-size="18" text-anchor="middle" font-weight="bold" fill="#2e7d32">催收2.0：智能选择模式</text>

            <!-- 催收1.0流程 -->
            <!-- 客户回复 -->
            <rect x="80" y="120" width="120" height="40" rx="5" fill="white" stroke="#e57373" stroke-width="2" />
            <text x="140" y="145" font-size="12" text-anchor="middle" fill="#c62828">客户回复</text>

            <!-- 意图识别 -->
            <rect x="80" y="190" width="120" height="40" rx="5" fill="#ffcdd2" stroke="#e57373" stroke-width="2" />
            <text x="140" y="215" font-size="12" text-anchor="middle" fill="#c62828">意图识别</text>

            <!-- 预制话术库 -->
            <rect x="250" y="190" width="120" height="40" rx="5" fill="#ffcdd2" stroke="#e57373" stroke-width="2" />
            <text x="310" y="215" font-size="12" text-anchor="middle" fill="#c62828">预制话术库</text>

            <!-- 一对一映射 -->
            <rect x="80" y="260" width="290" height="40" rx="5" fill="#ffcdd2" stroke="#e57373" stroke-width="2" />
            <text x="225" y="285" font-size="12" text-anchor="middle" fill="#c62828">一对一意图话术映射</text>

            <!-- 话术播报 -->
            <rect x="80" y="330" width="120" height="40" rx="5" fill="white" stroke="#e57373" stroke-width="2" />
            <text x="140" y="355" font-size="12" text-anchor="middle" fill="#c62828">话术播报</text>

            <!-- 基础监工 -->
            <rect x="250" y="330" width="120" height="40" rx="5" fill="#ffcdd2" stroke="#e57373" stroke-width="2" />
            <text x="310" y="355" font-size="12" text-anchor="middle" fill="#c62828">基础监工</text>

            <!-- 多维度小结 -->
            <rect x="80" y="400" width="290" height="40" rx="5" fill="#ffcdd2" stroke="#e57373" stroke-width="2" />
            <text x="225" y="425" font-size="12" text-anchor="middle" fill="#c62828">多维度小结分析</text>

            <!-- 催收1.0特点 -->
            <rect x="80" y="470" width="290" height="120" rx="5" fill="white" stroke="#e57373" stroke-width="1" />
            <text x="225" y="490" font-size="12" text-anchor="middle" font-weight="bold" fill="#c62828">技术特点</text>
            <text x="225" y="510" font-size="11" text-anchor="middle" fill="#d32f2f">• 固定话术模板</text>
            <text x="225" y="525" font-size="11" text-anchor="middle" fill="#d32f2f">• 简单意图分类</text>
            <text x="225" y="540" font-size="11" text-anchor="middle" fill="#d32f2f">• 人工抽检监控</text>
            <text x="225" y="555" font-size="11" text-anchor="middle" fill="#d32f2f">• 周期性更新</text>
            <text x="225" y="570" font-size="11" text-anchor="middle" fill="#d32f2f">• 相对简单维护</text>

            <!-- 催收2.0流程 -->
            <!-- 客户回复 -->
            <rect x="630" y="120" width="120" height="40" rx="5" fill="white" stroke="#66bb6a" stroke-width="2" />
            <text x="690" y="145" font-size="12" text-anchor="middle" fill="#2e7d32">客户回复</text>

            <!-- 风格判断Agent -->
            <rect x="630" y="190" width="120" height="40" rx="5" fill="#c8e6c9" stroke="#66bb6a" stroke-width="2" />
            <text x="690" y="210" font-size="11" text-anchor="middle" fill="#2e7d32">风格判断</text>
            <text x="690" y="225" font-size="11" text-anchor="middle" fill="#2e7d32">Agent</text>

            <!-- 话术选择Agent -->
            <rect x="800" y="190" width="120" height="40" rx="5" fill="#c8e6c9" stroke="#66bb6a" stroke-width="2" />
            <text x="860" y="210" font-size="11" text-anchor="middle" fill="#2e7d32">话术选择</text>
            <text x="860" y="225" font-size="11" text-anchor="middle" fill="#2e7d32">Agent</text>

            <!-- 九宫格评估 -->
            <rect x="630" y="260" width="120" height="40" rx="5" fill="#c8e6c9" stroke="#66bb6a" stroke-width="2" />
            <text x="690" y="285" font-size="12" text-anchor="middle" fill="#2e7d32">九宫格评估</text>

            <!-- RAG召回 -->
            <rect x="800" y="260" width="120" height="40" rx="5" fill="#c8e6c9" stroke="#66bb6a" stroke-width="2" />
            <text x="860" y="285" font-size="12" text-anchor="middle" fill="#2e7d32">RAG召回</text>

            <!-- 动态话术构建 -->
            <rect x="630" y="330" width="290" height="40" rx="5" fill="#c8e6c9" stroke="#66bb6a" stroke-width="2" />
            <text x="775" y="355" font-size="12" text-anchor="middle" fill="#2e7d32">动态话术选项构建</text>

            <!-- 话术播报 -->
            <rect x="630" y="400" width="120" height="40" rx="5" fill="white" stroke="#66bb6a" stroke-width="2" />
            <text x="690" y="425" font-size="12" text-anchor="middle" fill="#2e7d32">话术播报</text>

            <!-- AI监工2.0 -->
            <rect x="800" y="400" width="120" height="40" rx="5" fill="#c8e6c9" stroke="#66bb6a" stroke-width="2" />
            <text x="860" y="425" font-size="12" text-anchor="middle" fill="#2e7d32">AI监工2.0</text>

            <!-- 催收2.0特点 -->
            <rect x="630" y="470" width="290" height="120" rx="5" fill="white" stroke="#66bb6a" stroke-width="1" />
            <text x="775" y="490" font-size="12" text-anchor="middle" font-weight="bold" fill="#2e7d32">技术特点</text>
            <text x="775" y="510" font-size="11" text-anchor="middle" fill="#388e3c">• 动态话术选择</text>
            <text x="775" y="525" font-size="11" text-anchor="middle" fill="#388e3c">• 个性化风格判断</text>
            <text x="775" y="540" font-size="11" text-anchor="middle" fill="#388e3c">• 全量智能监控</text>
            <text x="775" y="555" font-size="11" text-anchor="middle" fill="#388e3c">• 实时优化调整</text>
            <text x="775" y="570" font-size="11" text-anchor="middle" fill="#388e3c">• 复杂架构管理</text>

            <!-- 连接线 - 催收1.0 -->
            <path d="M 140,160 L 140,190" stroke="#c62828" stroke-width="2" marker-end="url(#arrowhead1)" />
            <path d="M 200,210 L 250,210" stroke="#c62828" stroke-width="2" marker-end="url(#arrowhead1)" />
            <path d="M 225,230 L 225,260" stroke="#c62828" stroke-width="2" marker-end="url(#arrowhead1)" />
            <path d="M 140,300 L 140,330" stroke="#c62828" stroke-width="2" marker-end="url(#arrowhead1)" />
            <path d="M 225,370 L 225,400" stroke="#c62828" stroke-width="2" marker-end="url(#arrowhead1)" />

            <!-- 连接线 - 催收2.0 -->
            <path d="M 690,160 L 690,190" stroke="#2e7d32" stroke-width="2" marker-end="url(#arrowhead2)" />
            <path d="M 690,230 L 690,260" stroke="#2e7d32" stroke-width="2" marker-end="url(#arrowhead2)" />
            <path d="M 860,230 L 860,260" stroke="#2e7d32" stroke-width="2" marker-end="url(#arrowhead2)" />
            <path d="M 775,300 L 775,330" stroke="#2e7d32" stroke-width="2" marker-end="url(#arrowhead2)" />
            <path d="M 690,370 L 690,400" stroke="#2e7d32" stroke-width="2" marker-end="url(#arrowhead2)" />
            <path d="M 860,370 L 860,400" stroke="#2e7d32" stroke-width="2" marker-end="url(#arrowhead2)" />

            <!-- 演进箭头 -->
            <path d="M 550,350 L 600,350" stroke="#34495e" stroke-width="4" marker-end="url(#evolutionArrow)" />
            <text x="575" y="340" font-size="14" text-anchor="middle" font-weight="bold" fill="#34495e">技术演进</text>

            <!-- 箭头定义 -->
            <defs>
                <marker id="arrowhead1" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                    <polygon points="0 0, 8 3, 0 6" fill="#c62828" />
                </marker>
                <marker id="arrowhead2" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                    <polygon points="0 0, 8 3, 0 6" fill="#2e7d32" />
                </marker>
                <marker id="evolutionArrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                    <polygon points="0 0, 12 4, 0 8" fill="#34495e" />
                </marker>
            </defs>
        </svg>
    </div>
</body>
</html>
