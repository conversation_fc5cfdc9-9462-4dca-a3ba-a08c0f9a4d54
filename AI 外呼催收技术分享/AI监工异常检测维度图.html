<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI监工2.0异常检测维度</title>
    <style>
      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        max-width: 1000px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      h1 {
        text-align: center;
        color: #2c3e50;
        margin-bottom: 30px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>AI监工2.0：9大异常检测维度</h1>

      <svg
        width="950"
        height="650"
        viewBox="0 0 950 650"
        xmlns="http://www.w3.org/2000/svg"
      >
        <!-- 定义样式 -->
        <defs>
          <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.2" />
          </filter>

          <linearGradient
            id="systemGradient"
            x1="0%"
            y1="0%"
            x2="100%"
            y2="100%"
          >
            <stop offset="0%" style="stop-color: #ffebee; stop-opacity: 1" />
            <stop
              offset="100%"
              style="stop-color: #ffcdd2; stop-opacity: 0.8"
            />
          </linearGradient>

          <linearGradient
            id="businessGradient"
            x1="0%"
            y1="0%"
            x2="100%"
            y2="100%"
          >
            <stop offset="0%" style="stop-color: #fff3e0; stop-opacity: 1" />
            <stop
              offset="100%"
              style="stop-color: #ffcc02; stop-opacity: 0.3"
            />
          </linearGradient>

          <linearGradient
            id="qualityGradient"
            x1="0%"
            y1="0%"
            x2="100%"
            y2="100%"
          >
            <stop offset="0%" style="stop-color: #e8f5e9; stop-opacity: 1" />
            <stop
              offset="100%"
              style="stop-color: #c8e6c9; stop-opacity: 0.8"
            />
          </linearGradient>
        </defs>

        <!-- 标题 -->
        <text
          x="475"
          y="30"
          font-size="20"
          text-anchor="middle"
          font-weight="bold"
          fill="#2c3e50"
        >
          全方位异常检测体系
        </text>

        <!-- 系统异常监控 -->
        <rect
          x="50"
          y="60"
          width="280"
          height="160"
          rx="15"
          fill="url(#systemGradient)"
          stroke="#f44336"
          stroke-width="3"
          filter="url(#shadow)"
        />
        <text
          x="190"
          y="85"
          font-size="16"
          text-anchor="middle"
          font-weight="bold"
          fill="#d32f2f"
        >
          系统异常监控
        </text>

        <!-- 响应超时 -->
        <rect
          x="70"
          y="100"
          width="240"
          height="35"
          rx="5"
          fill="white"
          stroke="#f44336"
          stroke-width="1"
        />
        <text x="90" y="115" font-size="12" font-weight="bold" fill="#c62828">
          1. 响应超时
        </text>
        <text x="90" y="130" font-size="10" fill="#d32f2f">
          灵犀响应时间 ≥3秒
        </text>

        <!-- 系统报错 -->
        <rect
          x="70"
          y="145"
          width="240"
          height="35"
          rx="5"
          fill="white"
          stroke="#f44336"
          stroke-width="1"
        />
        <text x="90" y="160" font-size="12" font-weight="bold" fill="#c62828">
          2. 系统报错
        </text>
        <text x="90" y="175" font-size="10" fill="#d32f2f">
          捕获异常日志并分析
        </text>

        <!-- 业务异常监控 -->
        <rect
          x="350"
          y="60"
          width="280"
          height="160"
          rx="15"
          fill="url(#businessGradient)"
          stroke="#ff9800"
          stroke-width="3"
          filter="url(#shadow)"
        />
        <text
          x="490"
          y="85"
          font-size="16"
          text-anchor="middle"
          font-weight="bold"
          fill="#f57c00"
        >
          业务异常监控
        </text>

        <!-- 轮次过少 -->
        <rect
          x="370"
          y="100"
          width="240"
          height="25"
          rx="5"
          fill="white"
          stroke="#ff9800"
          stroke-width="1"
        />
        <text x="390" y="115" font-size="12" font-weight="bold" fill="#ef6c00">
          3. 轮次过少
        </text>
        <text x="520" y="115" font-size="10" fill="#e65100">
          系统挂断但对话≤4轮
        </text>

        <!-- 异常挂断 -->
        <rect
          x="370"
          y="130"
          width="240"
          height="25"
          rx="5"
          fill="white"
          stroke="#ff9800"
          stroke-width="1"
        />
        <text x="390" y="145" font-size="12" font-weight="bold" fill="#ef6c00">
          4. 异常挂断
        </text>
        <text x="520" y="145" font-size="10" fill="#e65100">
          未命中结束语的系统挂断
        </text>

        <!-- 通时过长 -->
        <rect
          x="370"
          y="160"
          width="240"
          height="25"
          rx="5"
          fill="white"
          stroke="#ff9800"
          stroke-width="1"
        />
        <text x="390" y="175" font-size="12" font-weight="bold" fill="#ef6c00">
          5. 通时过长
        </text>
        <text x="520" y="175" font-size="10" fill="#e65100">
          单次通话时长≥5分钟
        </text>

        <!-- 话术质量监控 -->
        <rect
          x="650"
          y="60"
          width="280"
          height="160"
          rx="15"
          fill="url(#qualityGradient)"
          stroke="#4caf50"
          stroke-width="3"
          filter="url(#shadow)"
        />
        <text
          x="790"
          y="85"
          font-size="16"
          text-anchor="middle"
          font-weight="bold"
          fill="#2e7d32"
        >
          话术质量监控
        </text>

        <!-- 话术不匹配 -->
        <rect
          x="670"
          y="100"
          width="240"
          height="25"
          rx="5"
          fill="white"
          stroke="#4caf50"
          stroke-width="1"
        />
        <text x="690" y="115" font-size="12" font-weight="bold" fill="#388e3c">
          6. 话术不匹配
        </text>
        <text x="810" y="115" font-size="10" fill="#2e7d32">
          回复与用户意图不符
        </text>

        <!-- 意图分类错误 -->
        <rect
          x="670"
          y="130"
          width="240"
          height="25"
          rx="5"
          fill="white"
          stroke="#4caf50"
          stroke-width="1"
        />
        <text x="690" y="145" font-size="12" font-weight="bold" fill="#388e3c">
          7. 意图分类错误
        </text>
        <text x="810" y="145" font-size="10" fill="#2e7d32">
          一级意图识别错误
        </text>

        <!-- 话术非最优 -->
        <rect
          x="670"
          y="160"
          width="240"
          height="25"
          rx="5"
          fill="white"
          stroke="#4caf50"
          stroke-width="1"
        />
        <text x="690" y="175" font-size="12" font-weight="bold" fill="#388e3c">
          8. 话术非最优
        </text>
        <text x="810" y="175" font-size="10" fill="#2e7d32">
          存在更优话术选择
        </text>

        <!-- 话术评分过低 -->
        <rect
          x="670"
          y="190"
          width="240"
          height="25"
          rx="5"
          fill="white"
          stroke="#4caf50"
          stroke-width="1"
        />
        <text x="690" y="205" font-size="12" font-weight="bold" fill="#388e3c">
          9. 话术评分过低
        </text>
        <text x="810" y="205" font-size="10" fill="#2e7d32">
          质量评分低于阈值
        </text>

        <!-- 检测流程 -->
        <rect
          x="150"
          y="280"
          width="650"
          height="120"
          rx="10"
          fill="#ecf0f1"
          stroke="#95a5a6"
          stroke-width="2"
          filter="url(#shadow)"
        />
        <text
          x="475"
          y="305"
          font-size="16"
          text-anchor="middle"
          font-weight="bold"
          fill="#2c3e50"
        >
          检测处理流程
        </text>

        <!-- 流程步骤 -->
        <circle
          cx="200"
          cy="340"
          r="20"
          fill="#3498db"
          stroke="white"
          stroke-width="2"
        />
        <text
          x="200"
          y="347"
          font-size="12"
          text-anchor="middle"
          font-weight="bold"
          fill="white"
        >
          1
        </text>
        <text
          x="200"
          y="370"
          font-size="11"
          text-anchor="middle"
          fill="#2c3e50"
        >
          数据入口
        </text>

        <circle
          cx="320"
          cy="340"
          r="20"
          fill="#3498db"
          stroke="white"
          stroke-width="2"
        />
        <text
          x="320"
          y="347"
          font-size="12"
          text-anchor="middle"
          font-weight="bold"
          fill="white"
        >
          2
        </text>
        <text
          x="320"
          y="370"
          font-size="11"
          text-anchor="middle"
          fill="#2c3e50"
        >
          并行检测
        </text>

        <circle
          cx="440"
          cy="340"
          r="20"
          fill="#3498db"
          stroke="white"
          stroke-width="2"
        />
        <text
          x="440"
          y="347"
          font-size="12"
          text-anchor="middle"
          font-weight="bold"
          fill="white"
        >
          3
        </text>
        <text
          x="440"
          y="370"
          font-size="11"
          text-anchor="middle"
          fill="#2c3e50"
        >
          异常汇总
        </text>

        <circle
          cx="560"
          cy="340"
          r="20"
          fill="#3498db"
          stroke="white"
          stroke-width="2"
        />
        <text
          x="560"
          y="347"
          font-size="12"
          text-anchor="middle"
          font-weight="bold"
          fill="white"
        >
          4
        </text>
        <text
          x="560"
          y="370"
          font-size="11"
          text-anchor="middle"
          fill="#2c3e50"
        >
          分级告警
        </text>

        <circle
          cx="680"
          cy="340"
          r="20"
          fill="#3498db"
          stroke="white"
          stroke-width="2"
        />
        <text
          x="680"
          y="347"
          font-size="12"
          text-anchor="middle"
          font-weight="bold"
          fill="white"
        >
          5
        </text>
        <text
          x="680"
          y="370"
          font-size="11"
          text-anchor="middle"
          fill="#2c3e50"
        >
          闭环跟踪
        </text>

        <!-- 连接线 -->
        <path
          d="M 220,340 L 300,340"
          stroke="#3498db"
          stroke-width="2"
          marker-end="url(#arrowhead)"
        />
        <path
          d="M 340,340 L 420,340"
          stroke="#3498db"
          stroke-width="2"
          marker-end="url(#arrowhead)"
        />
        <path
          d="M 460,340 L 540,340"
          stroke="#3498db"
          stroke-width="2"
          marker-end="url(#arrowhead)"
        />
        <path
          d="M 580,340 L 660,340"
          stroke="#3498db"
          stroke-width="2"
          marker-end="url(#arrowhead)"
        />

        <!-- 关键指标 -->
        <rect
          x="150"
          y="450"
          width="650"
          height="150"
          rx="10"
          fill="#f8f9fa"
          stroke="#6c757d"
          stroke-width="2"
          filter="url(#shadow)"
        />
        <text
          x="475"
          y="475"
          font-size="16"
          text-anchor="middle"
          font-weight="bold"
          fill="#2c3e50"
        >
          关键性能指标
        </text>

        <!-- 指标项 -->
        <rect
          x="180"
          y="490"
          width="180"
          height="80"
          rx="5"
          fill="white"
          stroke="#17a2b8"
          stroke-width="1"
        />
        <text
          x="270"
          y="510"
          font-size="12"
          text-anchor="middle"
          font-weight="bold"
          fill="#17a2b8"
        >
          覆盖率指标
        </text>
        <text
          x="270"
          y="530"
          font-size="10"
          text-anchor="middle"
          fill="#495057"
        >
          • 全量监控：100%
        </text>
        <text
          x="270"
          y="545"
          font-size="10"
          text-anchor="middle"
          fill="#495057"
        >
          • 实时性：≤1分钟
        </text>
        <text
          x="270"
          y="560"
          font-size="10"
          text-anchor="middle"
          fill="#495057"
        >
          • 准确率：≥85%
        </text>

        <rect
          x="385"
          y="490"
          width="180"
          height="80"
          rx="5"
          fill="white"
          stroke="#28a745"
          stroke-width="1"
        />
        <text
          x="475"
          y="510"
          font-size="12"
          text-anchor="middle"
          font-weight="bold"
          fill="#28a745"
        >
          处理效率指标
        </text>
        <text
          x="475"
          y="530"
          font-size="10"
          text-anchor="middle"
          fill="#495057"
        >
          • 处理闭环率：≥90%
        </text>
        <text
          x="475"
          y="545"
          font-size="10"
          text-anchor="middle"
          fill="#495057"
        >
          • 平均处理时长：≤24h
        </text>
        <text
          x="475"
          y="560"
          font-size="10"
          text-anchor="middle"
          fill="#495057"
        >
          • 重复异常率：≤5%
        </text>

        <rect
          x="590"
          y="490"
          width="180"
          height="80"
          rx="5"
          fill="white"
          stroke="#dc3545"
          stroke-width="1"
        />
        <text
          x="680"
          y="510"
          font-size="12"
          text-anchor="middle"
          font-weight="bold"
          fill="#dc3545"
        >
          业务效果指标
        </text>
        <text
          x="680"
          y="530"
          font-size="10"
          text-anchor="middle"
          fill="#495057"
        >
          • 催收成功率提升
        </text>
        <text
          x="680"
          y="545"
          font-size="10"
          text-anchor="middle"
          fill="#495057"
        >
          • 客户满意度改善
        </text>
        <text
          x="680"
          y="560"
          font-size="10"
          text-anchor="middle"
          fill="#495057"
        >
          • 合规性保障
        </text>

        <!-- 箭头定义 -->
        <defs>
          <marker
            id="arrowhead"
            markerWidth="8"
            markerHeight="6"
            refX="7"
            refY="3"
            orient="auto"
          >
            <polygon points="0 0, 8 3, 0 6" fill="#3498db" />
          </marker>
        </defs>
      </svg>
    </div>
  </body>
</html>
