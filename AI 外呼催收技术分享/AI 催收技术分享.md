# AI 外呼催收技术演进：从 1.0 到 2.0 的智能化升级之路

## 引言

在金融科技快速发展的今天，AI 外呼催收技术已成为金融机构提升催收效率、降低运营成本的重要手段。随着大语言模型技术的成熟和应用场景的深化，我们的 AI 外呼催收系统经历了从 1.0 到 2.0 的重要技术演进，实现了从"静态预制"到"动态智能"的跨越式升级。

### 业务价值与技术挑战

AI 外呼催收系统面临的核心挑战包括：

- **个性化应对**：不同客户的还款能力、还款意愿差异巨大，需要个性化的催收策略
- **话术适配性**：传统预制话术难以覆盖复杂多变的客户回复场景
- **质量监控**：如何确保 AI 外呼的质量和合规性，及时发现和纠正异常情况
- **效果评估**：如何科学评估催收效果，持续优化催收策略
- **语音质量**：如何生成高质量、自然流畅的语音，提升客户体验

## AI 外呼催收流程概览

在深入了解技术演进之前，我们先来了解 AI 外呼催收的整体流程。基于我们的时序图设计，整个外呼流程包含以下关键环节：

![外呼流程时序图](https://static.zhongan.com/website/aigc/share/时序图.png)

从时序图可以看出，整个外呼流程涉及多个系统的协同工作：

### 核心流程步骤

**1. 外呼任务发起**

- 催收系统根据业务规则和客户信息，生成外呼任务
- 任务包含客户基本信息、逾期情况、历史催收记录等关键数据

**2. CC 外呼中心处理**

- CC 外呼中心接收任务，向客户发起电话呼叫
- 建立通话连接后，播放核身开场白进行客户身份确认

**3. 客户回复与意图识别**

- 客户对开场白进行回复
- AI 系统基于客户回复进行实时意图识别和分析

**4. 话术选择与播报**

- 根据识别的客户意图，选择对应的催收话术
- 通过 TTS 技术将话术转换为语音并播报给客户

**5. 多轮对话交互**

- 重复步骤 3-4，形成多轮对话交互
- 根据对话进展，动态调整催收策略和话术选择

**6. 通话结束与小结**

- 通话结束后，系统进行多维度的对话小结和标签提取
- 为后续催收策略制定提供数据支撑

这一流程在催收 1.0 和 2.0 中保持基本一致，主要差异在于意图识别和话术选择的实现方式。

## 催收 1.0 技术架构：从 0 到 1 的突破性建设

### 核心技术特点

催收 1.0 是我们 **AI 外呼催收系统从 0 到 1 的重要里程碑**，标志着传统人工催收向智能化催收的历史性转变。系统采用的是**意图识别驱动的预制话术体系**，其核心特点包括：

- **一对一话术映射**：每个意图类别对应一套预制话术，关系固定
- **分层流程控制**：通过灵犀平台的 flow 控制不同催收阶段（核身层、告知层、施压层）
- **大模型意图识别**：主要依靠大语言模型进行客户意图的识别和分类
- **FAQ 知识库**：通过 RAG 召回建立 FAQ 知识库，作为话术选项的补充
- **基础架构搭建**：完成了从无到有的系统架构设计和技术栈选型

### 意图识别机制

基于我们的催收 1.0 话术模板，系统建立了完整的意图识别体系：

#### 意图分类体系

催收 1.0 的意图识别采用层次化分类结构，主要包括：

- **A 类 - 资金困难**：客户表达经济困难，无法按时还款
- **B 类 - 承诺还款**：客户明确表示愿意还款，需要确认具体时间和金额
- **C 类 - 拖延时间**：客户试图推迟还款，寻找各种理由延期
- **D 类 - 拒绝还款**：客户明确拒绝还款或否认债务
- **E 类 - 其他情况**：无法归类到上述类别的其他回复
  <!-- 表示还有其他情况，但是没有列出来 -->
  ...

#### 意图识别流程

1. **语音转文本**：将客户语音转换为文本内容
2. **文本预处理**：清洗和标准化文本数据
3. **大模型推理**：使用大语言模型进行意图分类
4. **话术匹配**：根据识别出的意图类别，匹配对应的预制话术

### 多维度小结评估体系

催收 1.0 在通话结束后，会进行全面的多维度分析，主要评估维度包括：

**对话内容分析**

- **对话概述**：生成简洁的对话内容摘要，便于后续催收员快速了解沟通情况
- **客户声明**：记录客户是否强调自己从未逾期等关键声明

**客户特征分析**

- **施压敏感点**：识别客户在催收过程中的敏感反应点，为后续策略制定提供参考
- **逾期原因分析**：分析客户逾期的具体原因，如资金困难、忘记还款等

**还款评估**

- **还款能力评估**：基于客户透露的信息，评估其还款能力等级（高、中、低）
- **还款意愿评估**：分析客户的还款意愿强度，为催收策略提供依据
- **承诺记录**：记录客户的具体还款承诺，包括承诺金额和时间

**联系信息更新**

- **新联系方式**：收集客户提供的最新联系方式
- **沟通时间偏好**：记录客户方便接听电话的时间段

![小结](https://static.zhongan.com/website/aigc/share/小结图.png)

### 催收 1.0 的历史价值与技术局限

**历史价值**

催收 1.0 作为 AI 外呼催收的开创性版本，具有重要的历史意义：

1. **技术突破**：从 0 到 1 实现了大规模 AI 外呼催收
2. **流程标准化**：建立了完整的催收外呼流程和质量标准
3. **数据积累**：为后续版本优化提供了宝贵的业务数据和经验
4. **团队建设**：培养了专业的 AI 催收技术团队和运营体系

**技术局限**

尽管催收 1.0 在意图识别和话术播报方面已经相对成熟，但仍存在一些技术局限：

1. **话术灵活性不足**：预制话术无法应对复杂多变的客户回复场景
2. **个性化程度有限**：缺乏基于客户特征的个性化话术选择机制
3. **上下文理解能力弱**：难以充分利用对话历史信息进行决策
4. **实时优化能力不足**：话术更新和优化周期较长，响应变化较慢

## 催收 2.0 技术突破：动态话术选择的智能化升级

为了解决催收 1.0 的技术局限，我们开发了催收 2.0 系统，实现了从"静态预制"到"动态智能"的重要跨越。

### 核心技术创新

催收 2.0 的核心创新在于**风格判断 Agent + 话术选择 Agent 的双引擎架构**：

![催收2.0](https://static.zhongan.com/website/aigc/share/催收2.0架构图.png)

### 双引擎架构详解

#### 1. 风格判断 Agent

风格判断 Agent 负责分析客户特征，选择最适合的催收风格：

**九宫格评估机制**

- **评估维度**：还款能力 × 还款意愿
- **能力评估**：高、中、低三个等级
- **意愿评估**：高、中、低三个等级
- **组合策略**：9 种不同的客户类型组合

**风格选择策略**

- **施压风格**：适用于能力强、意愿弱的客户，采用更加直接的催收方式
- **共情风格**：适用于能力弱、意愿强的客户，采用理解和帮助的方式
- **通用风格**：适用于其他情况，采用平衡的催收策略

**意图大类识别**
系统会识别客户的主要意图类别：

- 承诺还款
- 敷衍/拒绝沟通
- 拒绝还款
- 协商还款

#### 2. 话术选择 Agent

话术选择 Agent 基于风格判断结果，动态构建和选择最优话术：

**话术包分类体系**

- 根据客户意图大类，系统会匹配对应的话术包
- 每个话术包包含多种不同风格的话术选项
- 话术内容会结合 RAG 召回的 FAQ 信息进行动态组合

**动态话术选项构建**

- **基础话术包**：根据意图类别匹配对应的话术包
- **FAQ 内容整合**：通过 RAG 召回相关 FAQ 内容，作为可选话术选项
- **选项组合**：基于客户标签和风格判断，构建可选择的话术选项列表

### RAG 召回机制

催收 2.0 采用了 RAG（Retrieval-Augmented Generation）召回机制：

**召回流程**

1. **客户情况分析**：根据客户最新回复内容
2. **相关 FAQ 检索**：从知识库中检索与客户情况最相关的 FAQ 内容
3. **内容整合**：将召回的 FAQ 内容与话术模板进行智能整合

**知识库构建**

- **FAQ 分类**：按照客户问题类型、催收阶段等维度进行分类
- **内容更新**：基于实际催收效果和客户反馈持续更新知识库
- **质量控制**：建立 FAQ 内容的质量评估和审核机制

### 智能结束规则判断

催收 2.0 引入了智能化的对话结束规则判断机制：

**结束条件**

- **承诺还款**：客户明确承诺具体的还款时间和金额
- **轮次限制**：对话轮次超过 8 轮，避免过度催收
- **客户明确拒绝**：客户明确表示拒绝沟通或还款
- **系统异常**：检测到系统异常或通话质量问题

**智能判断逻辑**

- 基于对话内容和客户回复，智能判断是否满足结束条件
- 结合客户情绪分析，避免在客户情绪激动时强行继续
- 考虑催收效果和客户体验的平衡

### 催收 2.0 的技术优势

相比催收 1.0，催收 2.0 在多个维度实现了显著提升：

**1. 个性化程度大幅提升**

- 基于九宫格评估的个性化风格选择
- 动态话术构建，适应不同客户特征
- 实时调整催收策略，提升催收效果

**2. 话术选择灵活性显著增强**

- 从固定预制话术转向动态话术选项构建
- RAG 召回机制丰富可选话术内容，提升应对能力
- 多风格话术包，覆盖更多催收场景

**3. 上下文理解能力提升**

- 充分利用对话历史信息进行决策
- 考虑客户情绪和反馈，调整催收策略
- 基于客户标签和历史记录进行个性化处理

**4. 实时优化能力增强**

- 基于实际催收效果持续优化话术库
- 快速响应市场变化和客户需求
- 支持 A/B 测试，验证不同策略效果

## AI 语音生成技术：静态与动态 TTS 的智能化应用

在 AI 外呼催收系统中，高质量的语音生成是提升客户体验的关键技术。我们采用了**静态 TTS + 动态 TTS** 的混合架构，实现了语音质量与生成效率的最佳平衡。

### 静态 TTS：高质量预生成语音

**技术特点**

- **本地模型优势**：使用 GPT-SoVITS、CosyVoice、F5-TTS 等开源 TTS 模型
- **音质优势**：本地 TTS 模型生成的语音拟人度更高，音质更自然
- **预生成策略**：针对不包含变量的固定话术，提前批量生成音频文件
- **存储优化**：预生成的音频文件可重复使用，减少实时计算压力

**应用场景**

- 不带变量的固定话术

### 动态 TTS：实时变量语音合成

**技术特点**

- **供应商 TTS**：使用云端 TTS 服务，保证并发处理能力
- **变量支持**：支持姓名、还款金额、逾期天数等动态变量
- **实时生成**：在外呼前根据客户信息实时合成个性化语音
- **并发优化**：云端服务具备足够的并发处理能力，满足大规模外呼需求

**应用场景**

- 含变量的话术

### 语音表现优化策略

**口癖添加技术**

为了提升语音对话的自然度，我们对话术内容进行了专门的优化：

- **语气词添加**：在话术中适当添加"嗯"、"啊"、"那个"等语气词
- **停顿优化**：通过标点符号和特殊标记控制语音停顿节奏
- **语调调整**：根据话术情感色彩调整语音的语调和语速
- **口语化改写**：将书面化表达转换为更符合口语习惯的表达方式

**示例对比**

```
原始话术：提醒您，公司等不了太久。今天还没处理，你的资料就要被上级部门调走。可能会采取其他手段来追偿，那边没那么好说话。
```

优化后音频：

<div>
<audio src="https://static.zhongan.com/website/aigc/share/添加口癖音频.wav" controls></audio>
</div>

## AI 监工 2.0 系统：准实时异常检测与智能告警

在催收 2.0 系统的基础上，我们进一步开发了 AI 监工 2.0 系统，实现了对外呼质量的全方位监控和管理。当 LLM 发现异常时，系统会自动进行告警，确保问题能够及时发现和处理。

### 系统架构概览

![监工架构图](https://static.zhongan.com/website/aigc/share/监工架构图.png)

### 9 大异常检测维度与智能告警机制

AI 监工 2.0 建立了全面的异常检测体系，覆盖 9 个关键维度。当 LLM 检测到异常时，系统会自动触发告警机制：

#### 系统异常监控

1. **响应超时**：灵犀响应时间 ≥3 秒

   - **告警机制**：实时监控响应时间，超时立即触发企微告警
   - **处理策略**：自动记录异常日志，推送给技术团队处理

2. **系统报错**：捕获异常日志并分析
   - **告警机制**：系统异常自动捕获，LLM 分析错误原因
   - **处理策略**：分级告警，严重错误立即通知，一般错误定期汇总

#### 业务异常监控

3. **轮次过少**：系统挂断但对话 ≤4 轮

   - **告警机制**：检测到异常短对话时，LLM 分析挂断原因
   - **处理策略**：推送异常案例给业务团队，优化对话策略

4. **异常挂断**：未命中结束语的系统挂断

   - **告警机制**：监控对话结束方式，异常挂断触发告警
   - **处理策略**：分析挂断时机和原因，优化结束判断逻辑

5. **通时过长**：单次通话时长 ≥5 分钟
   - **告警机制**：监控通话时长，超时自动告警
   - **处理策略**：分析长时间对话原因，优化对话效率

#### 话术质量监控

6. **话术不匹配**：回复与用户意图不符

   - **告警机制**：LLM 实时评估话术匹配度，不匹配时告警
   - **处理策略**：收集不匹配案例，优化意图识别和话术选择

7. **意图分类错误**：一级意图识别错误

   - **告警机制**：LLM 验证意图分类准确性，错误时告警
   - **处理策略**：标注错误案例，持续优化意图识别模型

8. **话术非最优**：存在更优话术选择

   - **告警机制**：LLM 评估话术选择，发现更优选项时告警
   - **处理策略**：收集优化建议，持续改进话术库

9. **话术评分过低**：质量评分低于阈值
   - **告警机制**：LLM 对话术质量打分，低分时告警
   - **处理策略**：分析低分原因，优化话术内容和表达方式

### 监工机制

#### 催收多维监工（准实时）

**核心流程**：

1. **会话数据入口**：接收完整的会话数据
2. **完整性检查**：验证数据完整性和有效性
3. **多维度并行检测**：
   - 系统异常检测模块
   - 业务异常检测模块
   - 话术质量检测模块
4. **异常汇总分析**：LLM 综合分析+优化建议生成
5. **分级告警**：根据异常严重程度触发不同级别告警
6. **闭环跟踪**：AI 标注页面展示，跟踪处理进度

**技术创新**：

- 基于会话数据准实时触发
- 多维度并行异常检测
- 智能异常聚合分析

### 核心技术流程详解

#### 工程检查模块

**系统异常检查**

- **未返回有效意图 ID**：检测系统是否正常返回意图识别结果
- **响应超时检查**：监控系统响应时间，超过 3 秒阈值触发告警
- **轮次检查**：验证对话轮次是否符合业务要求（≥4 轮）
- **通时检查**：监控通话时长，超过 5 分钟阈值进行分析

**检查逻辑**

```
IF 工程检查发现异常 THEN
    触发企微告警群通知
    记录异常类型和详细信息
    跳转到触达系统打标流程
ELSE
    进入LLM处理模块
END IF
```

#### LLM 处理模块

**意图大类正确性判断**

- 根据话术 ID 反查对应的意图大类
- 使用 LLM 验证意图识别的准确性
- 分析客户实际表达与识别结果的匹配度

**并行处理机制**

1. **LLM 客户意图改写**：提取和标准化客户的真实意图
2. **切割最新会话**：获取最近的对话内容进行分析

**双路 RAG 召回**

- **路径一**：基于客户意图召回相关 FAQ
- **路径二**：基于话术包召回最佳实践
- **智能融合**：将两路召回结果进行智能整合

**话术合理性判断**

- 基于 FAQ 和话术包，评估当前话术选择的合理性
- 分析是否存在更优的话术选择
- 生成具体的优化建议

### 闭环管理机制

AI 监工 2.0 建立了完整的闭环管理体系：

#### 异常处理闭环

**1. 异常检测**

- 多维度实时监控
- 智能异常识别和分类
- 异常严重程度评估

**2. 告警触达**

- **企微告警群**：实时推送异常信息
- **分级告警**：根据异常类型和严重程度采用不同告警策略
- **告警聚合**：避免告警风暴，智能聚合相似异常

**3. 跟踪处理**

- **触达系统打标**：记录异常处理状态
- **进度跟踪**：实时跟踪异常处理进度
- **效果验证**：验证处理效果，确保问题解决

**4. 持续改进**

- **生产复验**：在生产环境验证修复效果
- **模型优化**：基于异常案例优化检测模型
- **流程改进**：持续优化监工流程和机制

#### 关键指标体系

**覆盖率指标**

- **全量监控**：100%覆盖所有外呼会话
- **实时性**：≤1 分钟响应时效
- **准确率**：异常检测准确率 ≥85%

**处理效率指标**

- **处理闭环率**：≥90%的异常得到有效处理
- **平均处理时长**：异常处理平均时长 ≤24 小时
- **重复异常率**：≤5%的异常重复出现

**业务效果指标**

- **催收成功率提升**：通过质量监控提升催收效果
- **客户满意度**：减少异常情况，提升客户体验
- **合规性保障**：确保催收过程符合监管要求

## 技术对比与效果分析

### 催收 1.0 vs 2.0 全面对比

| 对比维度         | 催收 1.0：预制话术模式               | 催收 2.0：智能选择模式                    |
| ---------------- | ------------------------------------ | ----------------------------------------- |
| **话术生成方式** | • 预制话术库<br>• 一对一意图映射     | • 动态话术选项构建<br>• RAG 召回+选项组合 |
| **个性化程度**   | • 基础意图分类<br>• 有限个性化       | • 九宫格评估<br>• 风格化个性定制          |
| **上下文理解**   | • 单轮意图识别<br>• 历史信息利用有限 | • 多轮对话理解<br>• 充分利用对话历史      |
| **质量监控**     | • 基础监工机制<br>• 人工抽检为主     | • AI 监工 2.0 系统<br>• 9 维度全量监控    |
| **优化能力**     | • 周期性话术更新<br>• 响应速度较慢   | • 实时优化调整<br>• 快速响应变化          |

### 技术架构演进路径

**催收 1.0 → 催收 2.0 → AI 监工 2.0**

| 阶段            | 核心特征                                                                           | 关键技术能力                                                                         |
| --------------- | ---------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------ |
| **催收 1.0**    | • 预制话术库<br>• 意图识别驱动<br>• 一对一映射<br>• 基础监工<br>• 人工抽检         | • 个性化程度：30%<br>• 话术适配性：40%<br>• 质量监控覆盖：20%<br>• 响应速度：天级    |
| **催收 2.0**    | • 动态话术选项构建<br>• 双引擎架构<br>• 九宫格评估<br>• RAG 召回<br>• 智能风格选择 | • 个性化程度：85%<br>• 话术适配性：90%<br>• 质量监控覆盖：20%<br>• 响应速度：小时级  |
| **AI 监工 2.0** | • 9 维度监控<br>• 准实时检测<br>• 闭环管理<br>• 智能告警<br>• 持续优化             | • 个性化程度：85%<br>• 话术适配性：90%<br>• 质量监控覆盖：100%<br>• 响应速度：分钟级 |

## 总结

AI 外呼催收技术从 1.0 到 2.0 的演进，代表了从"规则驱动"到"智能驱动"的重要转变。这一技术演进历程不仅体现了我们在 AI 应用领域的技术积累，更展现了从 0 到 1 再到持续优化的完整产品化能力。

**催收 1.0：奠定基础，实现突破**

- 首次实现大规模 AI 外呼催收的商业化应用，完成了从 0 到 1 的历史性突破
- 建立了完整的 AI 外呼技术架构和规范的催收外呼流程

**催收 2.0：智能升级，效果显著**

- 通过**风格判断 Agent + 话术选择 Agent 的双引擎架构**，实现了从"静态预制"到"动态选择"的技术升级
- 个性化程度从 30% 提升到 85%，话术适配性从 40% 提升到 90%

**AI 监工 2.0：质量保障，智能告警**

- **AI 监工 2.0 系统**的引入，进一步保障了催收质量和合规性，建立了相对完整的闭环管理体系
- 质量监控覆盖从 20% 提升到 100%，响应速度从天级优化到分钟级

**语音生成技术：体验优化，质量提升**

- 混合 TTS 架构（静态高质量 + 动态灵活性）和口癖优化技术，显著提升语音对话的自然度

### 核心技术成果

1. **个性化催收策略**：九宫格评估机制实现客户分类，提升催收针对性
2. **动态话术选择**：RAG 召回+选项构建，提升话术适配性
3. **全量质量监控**：9 维度异常检测，实现准实时质量保障
4. **闭环管理机制**：从异常检测到处理验证的管理闭环

### 技术价值体现

- **效率提升**：通过个性化策略和智能话术选择，提升催收针对性
- **质量保障**：从人工抽检转向 AI 全量监控，覆盖面显著扩大
- **成本优化**：减少人工监工投入，提升自动化程度
- **体验改善**：基于客户特征的个性化应对，改善客户体验

### 技术特点

我们的 AI 外呼催收技术具有以下特点：

- **架构设计**：双引擎架构实现了风格判断与话术选择的分离
- **监控覆盖**：9 维度异常检测覆盖了主要的质量风险点
- **响应效率**：分钟级异常响应满足了业务实时性要求
- **管理闭环**：建立了从检测到处理的完整管理流程

通过这次技术升级，我们在催收效率、质量保障和成本控制方面都取得了实际的改进效果。同时，我们也认识到在复杂业务场景下，技术方案仍需要持续优化和完善，以更好地服务于实际的催收业务需求。
