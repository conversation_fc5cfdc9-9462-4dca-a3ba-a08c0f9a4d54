<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多维度小结评估体系</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>多维度小结评估体系</h1>
        
        <svg width="850" height="600" viewBox="0 0 850 600" xmlns="http://www.w3.org/2000/svg">
            <!-- 定义样式 -->
            <defs>
                <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.2" />
                </filter>
                
                <linearGradient id="contentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color: #e3f2fd; stop-opacity: 1" />
                    <stop offset="100%" style="stop-color: #bbdefb; stop-opacity: 1" />
                </linearGradient>
                
                <linearGradient id="characterGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color: #f3e5f5; stop-opacity: 1" />
                    <stop offset="100%" style="stop-color: #e1bee7; stop-opacity: 1" />
                </linearGradient>
                
                <linearGradient id="paymentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color: #e8f5e9; stop-opacity: 1" />
                    <stop offset="100%" style="stop-color: #c8e6c9; stop-opacity: 1" />
                </linearGradient>
                
                <linearGradient id="contactGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color: #fff3e0; stop-opacity: 1" />
                    <stop offset="100%" style="stop-color: #ffcc02; stop-opacity: 0.3" />
                </linearGradient>
            </defs>

            <!-- 标题 -->
            <text x="425" y="30" font-size="20" text-anchor="middle" font-weight="bold" fill="#2c3e50">通话结束后多维度分析</text>

            <!-- 对话内容分析 -->
            <rect x="50" y="60" width="350" height="120" rx="10" fill="url(#contentGradient)" stroke="#2196f3" stroke-width="2" filter="url(#shadow)" />
            <text x="225" y="85" font-size="16" text-anchor="middle" font-weight="bold" fill="#1976d2">对话内容分析</text>
            
            <circle cx="80" cy="110" r="4" fill="#1976d2" />
            <text x="95" y="115" font-size="12" fill="#1565c0">对话概述：生成简洁的对话内容摘要</text>
            
            <circle cx="80" cy="135" r="4" fill="#1976d2" />
            <text x="95" y="140" font-size="12" fill="#1565c0">客户声明：记录关键声明（如从未逾期等）</text>
            
            <circle cx="80" cy="160" r="4" fill="#1976d2" />
            <text x="95" y="165" font-size="12" fill="#1565c0">便于后续催收员快速了解沟通情况</text>

            <!-- 客户特征分析 -->
            <rect x="450" y="60" width="350" height="120" rx="10" fill="url(#characterGradient)" stroke="#9c27b0" stroke-width="2" filter="url(#shadow)" />
            <text x="625" y="85" font-size="16" text-anchor="middle" font-weight="bold" fill="#7b1fa2">客户特征分析</text>
            
            <circle cx="480" cy="110" r="4" fill="#7b1fa2" />
            <text x="495" y="115" font-size="12" fill="#6a1b9a">施压敏感点：识别客户敏感反应点</text>
            
            <circle cx="480" cy="135" r="4" fill="#7b1fa2" />
            <text x="495" y="140" font-size="12" fill="#6a1b9a">逾期原因分析：资金困难、忘记还款等</text>
            
            <circle cx="480" cy="160" r="4" fill="#7b1fa2" />
            <text x="495" y="165" font-size="12" fill="#6a1b9a">为后续策略制定提供参考依据</text>

            <!-- 还款评估 -->
            <rect x="50" y="220" width="350" height="180" rx="10" fill="url(#paymentGradient)" stroke="#4caf50" stroke-width="2" filter="url(#shadow)" />
            <text x="225" y="245" font-size="16" text-anchor="middle" font-weight="bold" fill="#2e7d32">还款评估</text>
            
            <!-- 还款能力 -->
            <rect x="70" y="260" width="130" height="60" rx="5" fill="white" stroke="#4caf50" stroke-width="1" />
            <text x="135" y="280" font-size="12" text-anchor="middle" font-weight="bold" fill="#388e3c">还款能力</text>
            <text x="135" y="295" font-size="10" text-anchor="middle" fill="#2e7d32">高</text>
            <text x="135" y="308" font-size="10" text-anchor="middle" fill="#2e7d32">中</text>
            <text x="135" y="321" font-size="10" text-anchor="middle" fill="#2e7d32">低</text>
            
            <!-- 还款意愿 -->
            <rect x="220" y="260" width="130" height="60" rx="5" fill="white" stroke="#4caf50" stroke-width="1" />
            <text x="285" y="280" font-size="12" text-anchor="middle" font-weight="bold" fill="#388e3c">还款意愿</text>
            <text x="285" y="295" font-size="10" text-anchor="middle" fill="#2e7d32">高</text>
            <text x="285" y="308" font-size="10" text-anchor="middle" fill="#2e7d32">中</text>
            <text x="285" y="321" font-size="10" text-anchor="middle" fill="#2e7d32">低</text>
            
            <!-- 承诺记录 -->
            <rect x="70" y="340" width="280" height="45" rx="5" fill="white" stroke="#4caf50" stroke-width="1" />
            <text x="210" y="360" font-size="12" text-anchor="middle" font-weight="bold" fill="#388e3c">承诺记录</text>
            <text x="210" y="375" font-size="10" text-anchor="middle" fill="#2e7d32">记录客户具体还款承诺（金额和时间）</text>

            <!-- 联系信息更新 -->
            <rect x="450" y="220" width="350" height="180" rx="10" fill="url(#contactGradient)" stroke="#ff9800" stroke-width="2" filter="url(#shadow)" />
            <text x="625" y="245" font-size="16" text-anchor="middle" font-weight="bold" fill="#f57c00">联系信息更新</text>
            
            <!-- 新联系方式 -->
            <rect x="470" y="260" width="310" height="60" rx="5" fill="white" stroke="#ff9800" stroke-width="1" />
            <text x="625" y="280" font-size="12" text-anchor="middle" font-weight="bold" fill="#ef6c00">新联系方式</text>
            <text x="625" y="295" font-size="10" text-anchor="middle" fill="#e65100">收集客户提供的最新联系方式</text>
            <text x="625" y="308" font-size="10" text-anchor="middle" fill="#e65100">确保信息完整性和有效性</text>
            
            <!-- 沟通时间偏好 -->
            <rect x="470" y="340" width="310" height="45" rx="5" fill="white" stroke="#ff9800" stroke-width="1" />
            <text x="625" y="360" font-size="12" text-anchor="middle" font-weight="bold" fill="#ef6c00">沟通时间偏好</text>
            <text x="625" y="375" font-size="10" text-anchor="middle" fill="#e65100">记录客户方便接听电话的时间段</text>

            <!-- 连接线 -->
            <path d="M 225,180 L 225,220" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)" />
            <path d="M 625,180 L 625,220" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)" />

            <!-- 底部总结 -->
            <rect x="150" y="450" width="550" height="80" rx="10" fill="#ecf0f1" stroke="#95a5a6" stroke-width="2" filter="url(#shadow)" />
            <text x="425" y="475" font-size="14" text-anchor="middle" font-weight="bold" fill="#2c3e50">评估结果应用</text>
            <text x="425" y="495" font-size="12" text-anchor="middle" fill="#34495e">• 为后续催收策略制定提供数据支撑</text>
            <text x="425" y="510" font-size="12" text-anchor="middle" fill="#34495e">• 支持客户分类和个性化处理</text>
            <text x="425" y="525" font-size="12" text-anchor="middle" fill="#34495e">• 建立完整的客户沟通档案</text>

            <!-- 箭头定义 -->
            <defs>
                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
                </marker>
            </defs>
        </svg>
    </div>
</body>
</html>
