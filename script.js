// ===== 全局变量 =====
let isScrolling = false;
let currentTheme = "dark";

// ===== DOM 加载完成后执行 =====
document.addEventListener("DOMContentLoaded", function () {
  initializeApp();
});

// ===== 应用初始化 =====
function initializeApp() {
  setupNavigation();
  setupScrollEffects();
  setupAnimations();
  setupThemeToggle();
  setupParticles();
  setupTypingAnimation();
  setupCounterAnimation();
  setupLazyLoading();
}

// ===== 导航功能 =====
function setupNavigation() {
  const hamburger = document.querySelector(".hamburger");
  const navMenu = document.querySelector(".nav-menu");
  const navLinks = document.querySelectorAll(".nav-link");

  // 汉堡菜单切换
  hamburger?.addEventListener("click", () => {
    toggleMobileMenu();
  });

  // 导航链接点击
  navLinks.forEach((link) => {
    link.addEventListener("click", (e) => {
      e.preventDefault();
      const targetId = link.getAttribute("href");
      const targetSection = document.querySelector(targetId);

      if (targetSection) {
        // 关闭移动端菜单
        hamburger?.classList.remove("active");
        navMenu?.classList.remove("active");
        document.body.style.overflow = "";

        // 平滑滚动到目标区域
        smoothScrollTo(targetSection);

        // 更新活动链接
        updateActiveNavLink(link);
      }
    });
  });

  // 滚动时更新导航栏
  window.addEventListener("scroll", throttle(updateNavbarOnScroll, 100));

  // 移动端菜单切换函数
  function toggleMobileMenu() {
    hamburger?.classList.toggle("active");
    navMenu?.classList.toggle("active");
    document.body.style.overflow = navMenu?.classList.contains("active")
      ? "hidden"
      : "";
  }

  // 将函数暴露到全局作用域，供其他函数使用
  window.toggleMobileMenu = toggleMobileMenu;
}

// 平滑滚动
document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
  anchor.addEventListener("click", function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute("href"));
    if (target) {
      const offsetTop = target.offsetTop - 70; // 导航栏高度
      window.scrollTo({
        top: offsetTop,
        behavior: "smooth",
      });
    }
  });
});

// 打字机效果
function typeWriter(element, text, speed = 100) {
  let i = 0;
  element.innerHTML = "";

  function type() {
    if (i < text.length) {
      element.innerHTML += text.charAt(i);
      i++;
      setTimeout(type, speed);
    }
  }

  type();
}

// 启动打字机效果
window.addEventListener("load", function () {
  const typingElement = document.querySelector(".typing-animation");
  if (typingElement) {
    setTimeout(() => {
      typeWriter(typingElement, 'echo "Hello World"', 150);
    }, 1000);
  }
});

// 滚动动画
const observerOptions = {
  threshold: 0.1,
  rootMargin: "0px 0px -50px 0px",
};

const observer = new IntersectionObserver(function (entries) {
  entries.forEach((entry) => {
    if (entry.isIntersecting) {
      entry.target.style.opacity = "1";
      entry.target.style.transform = "translateY(0)";
    }
  });
}, observerOptions);

// 观察所有需要动画的元素
document.addEventListener("DOMContentLoaded", function () {
  const animatedElements = document.querySelectorAll(
    ".blog-card, .project-card, .contact-item, .stat-item"
  );

  animatedElements.forEach((el) => {
    el.style.opacity = "0";
    el.style.transform = "translateY(20px)";
    el.style.transition = "opacity 0.6s ease, transform 0.6s ease";
    observer.observe(el);
  });
});

// 数字动画
function animateNumbers() {
  const numberElements = document.querySelectorAll(".stat-number");

  numberElements.forEach((el) => {
    const finalNumber = parseInt(el.textContent.replace(/[^\d]/g, ""));
    const suffix = el.textContent.replace(/[\d]/g, "");
    let currentNumber = 0;
    const increment = finalNumber / 50;

    function updateNumber() {
      currentNumber += increment;
      if (currentNumber < finalNumber) {
        el.textContent = Math.floor(currentNumber) + suffix;
        requestAnimationFrame(updateNumber);
      } else {
        el.textContent = finalNumber + suffix;
      }
    }

    updateNumber();
  });
}

// 当统计数据进入视口时触发动画
const statsObserver = new IntersectionObserver(
  function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        animateNumbers();
        statsObserver.unobserve(entry.target);
      }
    });
  },
  { threshold: 0.5 }
);

document.addEventListener("DOMContentLoaded", function () {
  const statsSection = document.querySelector(".about-stats");
  if (statsSection) {
    statsObserver.observe(statsSection);
  }
});

// 表单处理
document.addEventListener("DOMContentLoaded", function () {
  const form = document.querySelector(".form");
  if (form) {
    form.addEventListener("submit", function (e) {
      e.preventDefault();

      // 获取表单数据
      const formData = new FormData(form);
      const name = form.querySelector('input[type="text"]').value;
      const email = form.querySelector('input[type="email"]').value;
      const message = form.querySelector("textarea").value;

      // 简单验证
      if (!name || !email || !message) {
        showNotification("请填写所有必填字段", "error");
        return;
      }

      // 模拟发送
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalText = submitBtn.innerHTML;
      submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';
      submitBtn.disabled = true;

      setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        form.reset();
        showNotification("消息发送成功！我会尽快回复您。", "success");
      }, 2000);
    });
  }
});

// 通知功能
function showNotification(message, type = "success") {
  // 创建通知元素
  const notification = document.createElement("div");
  notification.className = `notification notification-${type}`;
  notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${
              type === "success" ? "check-circle" : "exclamation-circle"
            }"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;

  // 添加样式
  notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === "success" ? "#10b981" : "#ef4444"};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 400px;
    `;

  // 添加到页面
  document.body.appendChild(notification);

  // 显示动画
  setTimeout(() => {
    notification.style.transform = "translateX(0)";
  }, 100);

  // 关闭功能
  const closeBtn = notification.querySelector(".notification-close");
  closeBtn.addEventListener("click", () => {
    notification.style.transform = "translateX(100%)";
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 300);
  });

  // 自动关闭
  setTimeout(() => {
    if (document.body.contains(notification)) {
      notification.style.transform = "translateX(100%)";
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }
  }, 5000);
}

// 粒子背景效果
class ParticleSystem {
  constructor(canvas) {
    this.canvas = canvas;
    this.ctx = canvas.getContext("2d");
    this.particles = [];
    this.mouse = { x: 0, y: 0 };

    this.init();
    this.animate();
    this.setupEventListeners();
  }

  init() {
    this.resize();
    this.createParticles();
  }

  resize() {
    this.canvas.width = window.innerWidth;
    this.canvas.height = window.innerHeight;
  }

  createParticles() {
    const particleCount = Math.floor(
      (this.canvas.width * this.canvas.height) / 15000
    );

    for (let i = 0; i < particleCount; i++) {
      this.particles.push({
        x: Math.random() * this.canvas.width,
        y: Math.random() * this.canvas.height,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        size: Math.random() * 2 + 0.5,
        opacity: Math.random() * 0.5 + 0.2,
      });
    }
  }

  animate() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    this.particles.forEach((particle) => {
      // 更新位置
      particle.x += particle.vx;
      particle.y += particle.vy;

      // 边界检测
      if (particle.x < 0 || particle.x > this.canvas.width) particle.vx *= -1;
      if (particle.y < 0 || particle.y > this.canvas.height) particle.vy *= -1;

      // 鼠标交互
      const dx = this.mouse.x - particle.x;
      const dy = this.mouse.y - particle.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance < 100) {
        const force = (100 - distance) / 100;
        particle.vx += dx * force * 0.001;
        particle.vy += dy * force * 0.001;
      }

      // 绘制粒子
      this.ctx.beginPath();
      this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
      this.ctx.fillStyle = `rgba(0, 212, 255, ${particle.opacity})`;
      this.ctx.fill();
    });

    // 连接附近的粒子
    this.connectParticles();

    requestAnimationFrame(() => this.animate());
  }

  connectParticles() {
    for (let i = 0; i < this.particles.length; i++) {
      for (let j = i + 1; j < this.particles.length; j++) {
        const dx = this.particles[i].x - this.particles[j].x;
        const dy = this.particles[i].y - this.particles[j].y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 120) {
          const opacity = ((120 - distance) / 120) * 0.2;
          this.ctx.beginPath();
          this.ctx.moveTo(this.particles[i].x, this.particles[i].y);
          this.ctx.lineTo(this.particles[j].x, this.particles[j].y);
          this.ctx.strokeStyle = `rgba(0, 212, 255, ${opacity})`;
          this.ctx.lineWidth = 1;
          this.ctx.stroke();
        }
      }
    }
  }

  setupEventListeners() {
    window.addEventListener("resize", () => {
      this.resize();
    });

    document.addEventListener("mousemove", (e) => {
      this.mouse.x = e.clientX;
      this.mouse.y = e.clientY;
    });
  }
}

// 初始化粒子系统
document.addEventListener("DOMContentLoaded", function () {
  // 创建画布
  const canvas = document.createElement("canvas");
  canvas.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 0;
        opacity: 0.3;
    `;

  document.body.appendChild(canvas);

  // 启动粒子系统
  new ParticleSystem(canvas);
});

// 技能条动画
function animateSkillBars() {
  const skillItems = document.querySelectorAll(".skill-item");

  skillItems.forEach((item, index) => {
    setTimeout(() => {
      item.style.transform = "translateX(0)";
      item.style.opacity = "1";
    }, index * 100);
  });
}

// 监听技能区域
const skillsObserver = new IntersectionObserver(
  function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        animateSkillBars();
        skillsObserver.unobserve(entry.target);
      }
    });
  },
  { threshold: 0.3 }
);

document.addEventListener("DOMContentLoaded", function () {
  const skillsGrid = document.querySelector(".skills-grid");
  if (skillsGrid) {
    // 初始化技能项目
    const skillItems = document.querySelectorAll(".skill-item");
    skillItems.forEach((item) => {
      item.style.transform = "translateX(-50px)";
      item.style.opacity = "0";
      item.style.transition = "transform 0.6s ease, opacity 0.6s ease";
    });

    skillsObserver.observe(skillsGrid);
  }
});

// 主题切换功能（可选）
function createThemeToggle() {
  const themeToggle = document.createElement("button");
  themeToggle.className = "theme-toggle";
  themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
  themeToggle.style.cssText = `
        position: fixed;
        top: 50%;
        right: 20px;
        transform: translateY(-50%);
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--primary-color);
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
        z-index: 1000;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
    `;

  document.body.appendChild(themeToggle);

  themeToggle.addEventListener("click", function () {
    document.body.classList.toggle("light-theme");
    const icon = themeToggle.querySelector("i");
    icon.className = document.body.classList.contains("light-theme")
      ? "fas fa-sun"
      : "fas fa-moon";
  });
}

// 懒加载图片
function lazyLoadImages() {
  const images = document.querySelectorAll("img[data-src]");

  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.classList.remove("lazy");
        imageObserver.unobserve(img);
      }
    });
  });

  images.forEach((img) => imageObserver.observe(img));
}

// 初始化所有功能
document.addEventListener("DOMContentLoaded", function () {
  // 创建主题切换器
  createThemeToggle();

  // 初始化懒加载
  lazyLoadImages();

  // 添加加载完成类
  window.addEventListener("load", function () {
    document.body.classList.add("loaded");
  });
});

// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 优化滚动性能
window.addEventListener(
  "scroll",
  debounce(function () {
    const scrolled = window.pageYOffset;
    const parallaxElements = document.querySelectorAll(".parallax");

    parallaxElements.forEach((element) => {
      const speed = element.dataset.speed || 0.5;
      element.style.transform = `translateY(${scrolled * speed}px)`;
    });
  }, 10)
);

// ===== 缺失的函数实现 =====

// 平滑滚动函数
function smoothScrollTo(target) {
  const targetPosition = target.offsetTop - 70; // 减去导航栏高度
  const startPosition = window.pageYOffset;
  const distance = targetPosition - startPosition;
  const duration = 1000;
  let start = null;

  function animation(currentTime) {
    if (start === null) start = currentTime;
    const timeElapsed = currentTime - start;
    const run = easeInOutQuad(timeElapsed, startPosition, distance, duration);
    window.scrollTo(0, run);
    if (timeElapsed < duration) requestAnimationFrame(animation);
  }

  requestAnimationFrame(animation);
}

// 缓动函数
function easeInOutQuad(t, b, c, d) {
  t /= d / 2;
  if (t < 1) return (c / 2) * t * t + b;
  t--;
  return (-c / 2) * (t * (t - 2) - 1) + b;
}

// 更新活动导航链接
function updateActiveNavLink(activeLink) {
  document.querySelectorAll(".nav-link").forEach((link) => {
    link.classList.remove("active");
  });
  activeLink.classList.add("active");
}

// 滚动时更新导航栏
function updateNavbarOnScroll() {
  const navbar = document.querySelector(".navbar");
  const scrolled = window.pageYOffset > 50;

  if (scrolled) {
    navbar.style.background = "rgba(10, 10, 10, 0.95)";
    navbar.style.boxShadow = "0 2px 20px rgba(0, 0, 0, 0.1)";
  } else {
    navbar.style.background = "rgba(10, 10, 10, 0.9)";
    navbar.style.boxShadow = "none";
  }
}

// 滚动效果设置
function setupScrollEffects() {
  // 滚动指示器点击
  const scrollIndicator = document.querySelector(".scroll-indicator");
  scrollIndicator?.addEventListener("click", () => {
    const articlesSection = document.querySelector("#articles");
    if (articlesSection) {
      smoothScrollTo(articlesSection);
    }
  });

  // 视差滚动效果
  window.addEventListener("scroll", throttle(handleParallaxScroll, 16));
}

// 视差滚动处理
function handleParallaxScroll() {
  const scrolled = window.pageYOffset;
  const parallaxElements = document.querySelectorAll(
    ".floating-tech .tech-icon"
  );

  parallaxElements.forEach((element, index) => {
    const speed = 0.5 + index * 0.1;
    const yPos = -(scrolled * speed);
    element.style.transform = `translateY(${yPos}px) rotate(${
      scrolled * 0.02
    }deg)`;
  });
}

// 动画设置
function setupAnimations() {
  // 观察器选项
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px",
  };

  // 创建观察器
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add("loaded");

        // 如果是统计数字，启动计数动画
        if (entry.target.classList.contains("stat-number")) {
          animateCounter(entry.target);
        }
      }
    });
  }, observerOptions);

  // 观察需要动画的元素
  const animatedElements = document.querySelectorAll(
    ".article-card, .stat-item, .hero-content"
  );
  animatedElements.forEach((el) => {
    el.classList.add("loading");
    observer.observe(el);
  });
}

// 主题切换设置
function setupThemeToggle() {
  const themeToggle = document.querySelector(".theme-toggle");
  const themeIcon = themeToggle?.querySelector("i");

  themeToggle?.addEventListener("click", () => {
    currentTheme = currentTheme === "dark" ? "light" : "dark";
    document.body.classList.toggle("light-theme");

    if (themeIcon) {
      themeIcon.className =
        currentTheme === "dark" ? "fas fa-moon" : "fas fa-sun";
    }

    // 保存主题偏好
    localStorage.setItem("theme", currentTheme);
  });

  // 加载保存的主题
  const savedTheme = localStorage.getItem("theme");
  if (savedTheme && savedTheme !== currentTheme) {
    themeToggle?.click();
  }
}

// 粒子效果设置
function setupParticles() {
  createStarField();
}

function createStarField() {
  const particlesContainer = document.getElementById("particles-js");
  if (!particlesContainer) return;

  const numberOfStars = 100;

  for (let i = 0; i < numberOfStars; i++) {
    const star = document.createElement("div");
    star.className = "star";
    star.style.cssText = `
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(255, 255, 255, ${Math.random() * 0.8 + 0.2});
            border-radius: 50%;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: twinkle ${Math.random() * 3 + 2}s infinite;
        `;
    particlesContainer.appendChild(star);
  }

  // 添加闪烁动画
  const style = document.createElement("style");
  style.textContent = `
        @keyframes twinkle {
            0%, 100% { opacity: 0.2; }
            50% { opacity: 1; }
        }
    `;
  document.head.appendChild(style);
}

// 打字动画设置
function setupTypingAnimation() {
  const typingElements = document.querySelectorAll(".typing");

  typingElements.forEach((element) => {
    const text = element.textContent;
    element.textContent = "";
    element.style.borderRight = "2px solid var(--primary-color)";

    let i = 0;
    const typeWriter = () => {
      if (i < text.length) {
        element.textContent += text.charAt(i);
        i++;
        setTimeout(typeWriter, 100);
      } else {
        // 打字完成后，让光标闪烁
        setTimeout(() => {
          element.style.borderRight = "none";
        }, 1000);
      }
    };

    // 延迟开始打字动画
    setTimeout(typeWriter, 2000);
  });
}

// 计数器动画设置
function setupCounterAnimation() {
  // 这个函数在 setupAnimations 中被调用
}

function animateCounter(element) {
  const target = parseInt(element.getAttribute("data-count"));
  const duration = 2000;
  const increment = target / (duration / 16);
  let current = 0;

  element.classList.add("counting");

  const timer = setInterval(() => {
    current += increment;
    if (current >= target) {
      current = target;
      clearInterval(timer);
      element.classList.remove("counting");
    }
    element.textContent = Math.floor(current).toLocaleString();
  }, 16);
}

// 懒加载设置
function setupLazyLoading() {
  const images = document.querySelectorAll("img[data-src]");

  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.classList.remove("lazy");
        imageObserver.unobserve(img);
      }
    });
  });

  images.forEach((img) => {
    img.classList.add("lazy");
    imageObserver.observe(img);
  });
}

// 节流函数
function throttle(func, limit) {
  let inThrottle;
  return function () {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// 错误处理
window.addEventListener("error", (e) => {
  console.error("JavaScript Error:", e.error);
});

// 性能监控
window.addEventListener("load", () => {
  const loadTime = performance.now();
  console.log(`Page loaded in ${loadTime.toFixed(2)}ms`);
});

// ===== 移动端优化 =====
function initMobileOptimizations() {
  // 检测移动设备
  const isMobile =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
  const isTouchDevice =
    "ontouchstart" in window || navigator.maxTouchPoints > 0;

  if (isMobile || isTouchDevice) {
    document.body.classList.add("mobile-device");

    // 优化触摸滚动
    document.body.style.webkitOverflowScrolling = "touch";

    // 防止双击缩放
    let lastTouchEnd = 0;
    document.addEventListener(
      "touchend",
      function (event) {
        const now = new Date().getTime();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      },
      false
    );

    // 优化点击延迟
    document.addEventListener("touchstart", function () {}, true);

    // 移动端导航优化
    setupMobileNavigation();

    // 触摸手势支持
    setupTouchGestures();
  }

  // 检测网络状态
  if ("connection" in navigator) {
    const connection = navigator.connection;
    if (
      connection.effectiveType === "slow-2g" ||
      connection.effectiveType === "2g"
    ) {
      // 低网速优化
      document.body.classList.add("slow-connection");
      optimizeForSlowConnection();
    }
  }
}

function setupMobileNavigation() {
  const navbar = document.querySelector(".navbar");
  let lastScrollY = window.scrollY;

  // 滚动时隐藏/显示导航栏
  window.addEventListener(
    "scroll",
    throttle(() => {
      const currentScrollY = window.scrollY;

      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        // 向下滚动，隐藏导航栏
        navbar.style.transform = "translateY(-100%)";
      } else {
        // 向上滚动，显示导航栏
        navbar.style.transform = "translateY(0)";
      }

      lastScrollY = currentScrollY;
    }, 100)
  );

  // 点击外部关闭菜单
  document.addEventListener("click", (e) => {
    const navMenu = document.querySelector(".nav-menu");
    const hamburger = document.querySelector(".hamburger");

    if (
      navMenu.classList.contains("active") &&
      !navMenu.contains(e.target) &&
      !hamburger.contains(e.target)
    ) {
      toggleMobileMenu();
    }
  });
}

// toggleMobileMenu函数在setupNavigation中已定义

function setupTouchGestures() {
  let startX, startY, endX, endY;

  document.addEventListener("touchstart", (e) => {
    startX = e.touches[0].clientX;
    startY = e.touches[0].clientY;
  });

  document.addEventListener("touchend", (e) => {
    endX = e.changedTouches[0].clientX;
    endY = e.changedTouches[0].clientY;

    const deltaX = endX - startX;
    const deltaY = endY - startY;

    // 检测滑动手势
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
      if (deltaX > 0) {
        // 向右滑动
        handleSwipeRight();
      } else {
        // 向左滑动
        handleSwipeLeft();
      }
    }
  });
}

function handleSwipeRight() {
  const navMenu = document.querySelector(".nav-menu");
  if (!navMenu.classList.contains("active")) {
    toggleMobileMenu();
  }
}

function handleSwipeLeft() {
  const navMenu = document.querySelector(".nav-menu");
  if (navMenu.classList.contains("active")) {
    toggleMobileMenu();
  }
}

function optimizeForSlowConnection() {
  // 延迟加载非关键资源
  const images = document.querySelectorAll("img[data-src]");
  images.forEach((img) => {
    img.style.display = "none";
  });

  // 减少动画
  document.body.classList.add("reduced-animations");

  // 显示网络状态提示
  showNetworkStatus("检测到网络较慢，已优化页面加载");
}

function showNetworkStatus(message) {
  const statusBar = document.createElement("div");
  statusBar.className = "network-status";
  statusBar.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #f59e0b;
    color: white;
    padding: 8px 16px;
    text-align: center;
    font-size: 0.9rem;
    z-index: 10000;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
  `;
  statusBar.textContent = message;

  document.body.appendChild(statusBar);

  setTimeout(() => {
    statusBar.style.transform = "translateY(0)";
  }, 100);

  setTimeout(() => {
    statusBar.style.transform = "translateY(-100%)";
    setTimeout(() => {
      if (document.body.contains(statusBar)) {
        document.body.removeChild(statusBar);
      }
    }, 300);
  }, 5000);
}

// ===== 性能监控 =====
function initPerformanceMonitoring() {
  // 监控页面加载性能
  window.addEventListener("load", () => {
    if ("performance" in window) {
      const perfData = performance.getEntriesByType("navigation")[0];
      const loadTime = perfData.loadEventEnd - perfData.loadEventStart;

      if (loadTime > 3000) {
        console.warn("页面加载时间较长:", loadTime + "ms");
        // 可以发送性能数据到分析服务
      }
    }
  });

  // 监控内存使用
  if ("memory" in performance) {
    setInterval(() => {
      const memory = performance.memory;
      if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
        console.warn("内存使用率过高");
        // 执行内存清理
        cleanupMemory();
      }
    }, 30000);
  }
}

function cleanupMemory() {
  // 清理不必要的事件监听器
  const oldElements = document.querySelectorAll("[data-cleanup]");
  oldElements.forEach((element) => {
    element.removeEventListener("click", element._clickHandler);
    element.removeEventListener("scroll", element._scrollHandler);
  });

  // 清理粒子系统
  if (window.particleSystem) {
    window.particleSystem.cleanup();
  }
}

// 在应用初始化时调用移动端优化
document.addEventListener("DOMContentLoaded", function () {
  initMobileOptimizations();
  initPerformanceMonitoring();
});
