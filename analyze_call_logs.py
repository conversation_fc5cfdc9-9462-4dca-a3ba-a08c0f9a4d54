#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调用日志分析脚本
生成三级结构的Excel文件：
1. 一级：时间日期（具体到日期）
2. 二级：case维度（会话ID）
3. 三级：数量维度（每个case的异常数据条数）
"""

import pandas as pd
from datetime import datetime
import json
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

def analyze_call_logs():
    """分析调用日志并生成三级结构Excel"""
    
    # 读取原始Excel文件
    print("正在读取原始Excel文件...")
    df = pd.read_excel('调用日志-2025-07-09-11_19_06.xlsx')
    
    # 提取日期
    df['日期'] = df['请求时间'].dt.date
    
    # 分析响应内容，提取错误类型
    print("正在分析响应内容...")
    def extract_error_info(response_content):
        """提取响应内容中的错误信息"""
        if pd.isna(response_content):
            return None, None

        try:
            # 尝试解析JSON
            if isinstance(response_content, str):
                response_json = json.loads(response_content)
                correct_flag = response_json.get('correctFlag', True)
                err_type = response_json.get('errType', '未知错误')
                return correct_flag, err_type
            return None, None
        except:
            return None, None

    # 提取错误信息
    df[['correctFlag', 'errType']] = df['响应内容'].apply(
        lambda x: pd.Series(extract_error_info(x))
    )
    
    # 按日期和会话ID分组统计
    print("正在生成统计数据...")
    result_data = []
    
    # 按日期分组
    for date in sorted(df['日期'].unique()):
        date_df = df[df['日期'] == date]
        
        # 按会话ID分组
        session_groups = date_df.groupby('会话ID')
        
        for session_id, session_df in session_groups:
            # 统计该会话的数据数量和错误类型分布
            total_count = len(session_df)
            error_types = session_df['errType'].value_counts().to_dict()
            error_summary = ', '.join([f"{k}:{v}" for k, v in error_types.items() if pd.notna(k)])

            result_data.append({
                '日期': date,
                '会话ID': session_id,
                '记录数量': total_count,
                '错误类型分布': error_summary if error_summary else '无错误信息',
                '主要错误类型': session_df['errType'].mode().iloc[0] if not session_df['errType'].isna().all() else '未知'
            })
    
    # 创建结果DataFrame
    result_df = pd.DataFrame(result_data)
    
    # 创建Excel工作簿
    print("正在生成Excel文件...")
    wb = Workbook()
    ws = wb.active
    ws.title = "调用日志三级分析"
    
    # 设置样式
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    date_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
    session_fill = PatternFill(start_color="F2F2F2", end_color="F2F2F2", fill_type="solid")
    
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # 写入表头
    headers = ['层级', '日期/会话ID', '记录数量', '错误类型分布', '主要错误类型', '备注']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.border = border
    
    # 写入数据
    current_row = 2
    current_date = None
    
    for _, row in result_df.iterrows():
        date = row['日期']
        session_id = row['会话ID']
        record_count = row['记录数量']
        error_distribution = row['错误类型分布']
        main_error_type = row['主要错误类型']

        # 如果是新的日期，先写入日期行
        if current_date != date:
            current_date = date

            # 计算该日期的汇总数据
            date_data = result_df[result_df['日期'] == date]
            date_total_count = date_data['记录数量'].sum()
            date_session_count = len(date_data)

            # 统计该日期的主要错误类型
            all_errors = []
            for _, date_row in date_data.iterrows():
                if pd.notna(date_row['主要错误类型']):
                    all_errors.append(date_row['主要错误类型'])

            main_date_error = max(set(all_errors), key=all_errors.count) if all_errors else '未知'

            # 写入日期汇总行
            date_row_data = [
                '一级-日期',
                str(date),
                date_total_count,
                f'包含{date_session_count}个会话',
                main_date_error,
                f'当日总记录数: {date_total_count}'
            ]

            for col, value in enumerate(date_row_data, 1):
                cell = ws.cell(row=current_row, column=col, value=value)
                cell.fill = date_fill
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = border

            current_row += 1

        # 写入会话数据行
        session_row_data = [
            '二级-会话',
            session_id,
            record_count,
            error_distribution,
            main_error_type,
            f'该会话共{record_count}条记录'
        ]

        for col, value in enumerate(session_row_data, 1):
            cell = ws.cell(row=current_row, column=col, value=value)
            cell.fill = session_fill
            cell.alignment = Alignment(horizontal='left', vertical='center')
            cell.border = border

        current_row += 1
    
    # 调整列宽
    column_widths = [15, 35, 12, 40, 20, 25]
    for col, width in enumerate(column_widths, 1):
        ws.column_dimensions[chr(64 + col)].width = width
    
    # 添加汇总信息工作表
    summary_ws = wb.create_sheet("汇总统计")
    
    # 生成汇总统计
    summary_data = []
    for date in sorted(result_df['日期'].unique()):
        date_data = result_df[result_df['日期'] == date]

        # 统计错误类型分布
        all_errors = []
        for _, row in date_data.iterrows():
            if pd.notna(row['主要错误类型']):
                all_errors.append(row['主要错误类型'])

        error_counts = {}
        for error in all_errors:
            error_counts[error] = error_counts.get(error, 0) + 1

        top_error = max(error_counts.items(), key=lambda x: x[1]) if error_counts else ('未知', 0)

        summary_data.append({
            '日期': str(date),
            '会话数量': len(date_data),
            '总记录数': date_data['记录数量'].sum(),
            '主要错误类型': top_error[0],
            '主要错误数量': top_error[1]
        })
    
    summary_df = pd.DataFrame(summary_data)
    
    # 写入汇总数据
    for r in dataframe_to_rows(summary_df, index=False, header=True):
        summary_ws.append(r)
    
    # 设置汇总表样式
    for row in summary_ws.iter_rows(min_row=1, max_row=1):
        for cell in row:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = border
    
    for row in summary_ws.iter_rows(min_row=2):
        for cell in row:
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = border
    
    # 调整汇总表列宽
    for col in range(1, 6):
        summary_ws.column_dimensions[chr(64 + col)].width = 15
    
    # 保存文件
    output_filename = f"调用日志三级分析_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    wb.save(output_filename)
    
    print(f"分析完成！结果已保存到: {output_filename}")
    print(f"总共处理了 {len(df)} 条记录")
    print(f"涉及 {len(result_df['日期'].unique())} 个日期")
    print(f"涉及 {len(result_df)} 个会话")
    print(f"总记录数: {result_df['记录数量'].sum()} 条")
    
    return output_filename

if __name__ == "__main__":
    analyze_call_logs()
