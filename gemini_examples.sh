#!/bin/bash

# Gemini CLI 使用示例
echo "🤖 Gemini CLI 使用示例"
echo "====================="
echo ""

# 检查API密钥
if [ -z "$GEMINI_API_KEY" ]; then
    echo "❌ 请先设置GEMINI_API_KEY环境变量"
    echo "运行: export GEMINI_API_KEY='your-api-key-here'"
    exit 1
fi

echo "✅ API密钥已设置，开始演示..."
echo ""

# 示例1：简单对话
echo "📝 示例1：简单对话"
echo "=================="
echo "Hello, how are you today?" | gemini
echo ""

# 示例2：代码生成
echo "🔧 示例2：代码生成"
echo "=================="
echo "Write a Python function to calculate fibonacci numbers" | gemini
echo ""

# 示例3：文件分析（如果有Python文件）
if [ -f "*.py" ]; then
    echo "📁 示例3：文件分析"
    echo "=================="
    gemini -p "Please analyze this Python code and suggest improvements" *.py
    echo ""
fi

# 示例4：使用不同模型
echo "🧠 示例4：使用不同模型"
echo "===================="
echo "Explain quantum computing in simple terms" | gemini -m "gemini-2.5-flash"
echo ""

# 示例5：调试模式
echo "🐛 示例5：调试模式"
echo "=================="
echo "What is machine learning?" | gemini -d
echo ""

echo "✨ 演示完成！"
echo ""
echo "💡 更多用法："
echo "- gemini --help                    # 查看所有选项"
echo "- echo 'question' | gemini         # 管道输入"
echo "- gemini -p 'prompt' file.txt      # 分析文件"
echo "- gemini -m gemini-2.5-flash       # 使用不同模型"
echo "- gemini -s                        # 沙盒模式"
echo "- gemini -a                        # 包含所有文件"
