#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 导入修改后的函数
from generate_json_v4 import generate_json

def test_generate_json():
    """测试generate_json函数的各种场景"""
    
    print("="*50)
    print("测试修改后的意图识别脚本")
    print("="*50)
    
    # 测试用例1：二级分类A01（拖延时间/固定时间处理）
    print("\n1. 测试二级分类A01（拖延时间/固定时间处理）")
    result = generate_json("A01", [], "明天还款")
    print(f"输入: A01, 输出: {result}")
    
    # 测试用例2：二级分类B01（资金困难/钱到就还）
    print("\n2. 测试二级分类B01（资金困难/钱到就还）")
    result = generate_json("B01", [], "工资到了就还")
    print(f"输入: B01, 输出: {result}")
    
    # 测试用例3：一级分类A（拖延时间）
    print("\n3. 测试一级分类A（拖延时间）")
    result = generate_json("A", [], "需要时间")
    print(f"输入: A, 输出: {result}")
    
    # 测试用例4：一级分类B（资金困难）
    print("\n4. 测试一级分类B（资金困难）")
    result = generate_json("B", [], "没钱")
    print(f"输入: B, 输出: {result}")
    
    # 测试用例5：重复意图检测
    print("\n5. 测试重复意图检测")
    result = generate_json("A01", ["A01", "A01", "A01"], "明天还款")
    print(f"输入: A01 (重复3次), 输出: {result}")
    
    # 测试用例6：FAQ匹配场景
    print("\n6. 测试FAQ匹配场景")
    result = generate_json("A01", [], "明天还款", faq_id="SZ12")
    print(f"输入: A01, FAQ: SZ12, 输出: {result}")
    
    # 测试用例7：H系列处理
    print("\n7. 测试H系列处理")
    result = generate_json("H", [], "这是保费吗")
    print(f"输入: H, 输出: {result}")
    
    # 测试用例8：其他一级分类
    print("\n8. 测试其他一级分类")
    test_cases = [
        ("F01", "好的，马上还"),
        ("F02", "稍后处理"),
        ("D", "看看吧"),
        ("C", "还不了"),
        ("E", "已经还了")
    ]
    
    for intent_id, reply in test_cases:
        result = generate_json(intent_id, [], reply)
        print(f"输入: {intent_id}, 输出: {result}")
    
    # 测试用例9：所有二级分类
    print("\n9. 测试所有二级分类")
    secondary_cases = [
        ("A01", "明天还"),
        ("A02", "申请延期"),
        ("A03", "需要时间"),
        ("B01", "工资到就还"),
        ("B02", "没钱"),
        ("B03", "周转困难")
    ]
    
    for intent_id, reply in secondary_cases:
        result = generate_json(intent_id, [], reply)
        print(f"输入: {intent_id}, 输出: {result}")

if __name__ == "__main__":
    test_generate_json()