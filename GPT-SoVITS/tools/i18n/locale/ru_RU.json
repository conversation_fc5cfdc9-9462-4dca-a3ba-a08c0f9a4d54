{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net(onnx_dereverb):Это лучший выбор для реверберации с двумя каналами, но он не может устранить реверберацию с одним каналом;", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho:Устраняет эффект задержки. Aggressive устраняет более тщательно, чем Normal, DeReverb дополнительно устраняет реверберацию, может устранить реверберацию с одного канала, но не полностью устраняет высокочастотную реверберацию.", "*GPT模型列表": "*Список моделей GPT", "*SoVITS模型列表": "*Список моделей SoVITS", "*实验/模型名": "*Название эксперимента/модели", "*文本标注文件": "*Файл текстовой аннотации", "*训练集音频文件目录": "*Директория аудиофайлов обучающего набора", "*请上传并填写参考信息": "*Пожалуйста, загрузите и заполните референтные данные", "*请填写需要合成的目标文本和语种模式": "*Пожалуйста, введите целевой текст для синтеза и режим языка", ".list标注文件的路径": "Путь к файлу аннотации .list", ".限制范围越小判别效果越好。": "Чем меньше языков, тем лучше", "0-前置数据集获取工具": "0-Инструмент для получения предварительного набора данных", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0a-Инструмент для разделения вокала и аккомпанемента UVR5 & устранения реверберации и задержек", "0b-语音切分工具": "0b-Инструмент для разделения речи", "0bb-语音降噪工具": "0bb-Инструмент для подавления шумов в голосе", "0c-中文批量离线ASR工具": "0c-Инструмент для пакетной офлайн ASR на китайском", "0d-语音文本校对标注工具": "0d-Инструмент для коррекции и аннотации текста речи", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1A-训练集格式化工具": "1A-Инструмент для форматирования обучающего набора", "1Aa-文本内容": "1Aa-Содержание текста", "1Aabc-训练集格式化一键三连": "1Aabc-Форматирование обучающего набора одним нажатием", "1Ab-SSL自监督特征提取": "1Ab-Самоконтролируемое извлечение признаков SSL", "1Ac-语义token提取": "1Ac-Извлечение семантических токенов", "1B-微调训练": "1B-Дообучение", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1Ba-Обучение SoVITS. Файлы моделей для распространения находятся в SoVITS_weights.", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1Bb-Обучение GPT. Файлы моделей для распространения находятся в GPT_weights.", "1C-推理": "1C-Инференс", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1. Время обработки модели De<PERSON>-DeReverb почти вдвое больше, чем у двух других моделей DeEcho;", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1. Сохранение голоса: выберите этот для аудио без гармоний, сохранение голоса будет лучше, чем HP5. Встроенные модели HP2 и HP3, HP3 может немного пропускать сопровождение, но сохраняет голос немного лучше, чем HP2;", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-переозвучивание", "2、MDX-Net-Dereverb模型挺慢的；": "2. Модель MDX-Net-Dereverb довольно медленная;", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2. Сохранение только основного голоса: выберите это для аудио с гармониями, может ослабить основной голос. Встроенная модель HP5;", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3. Лично рекомендованная самая чистая конфигурация — сначала MDX-Net, затем DeEcho-Aggressive.", "3、去混响、去延迟模型（by FoxJoy）：": "3. Модель удаления реверберации и задержек (от FoxJoy):", "ASR 模型": "Модель ASR", "ASR 模型尺寸": "Размер модели ASR", "ASR 语言设置": "Настройки языка ASR", "ASR进程输出信息": "Информация о процессе ASR", "GPT模型列表": "Список моделей GPT", "GPT训练进程输出信息": "Информация о процессе обучения GPT", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "Параметры выборки GPT (не устанавливайте слишком низкие значения, если нет ссылочного текста. Используйте значения по умолчанию, если не уверены):", "GPU卡号,只能填1个整数": "Номер GPU, можно указать только одно целое число", "GPU卡号以-分割，每个卡号一个进程": "Номера GPU разделяются дефисом, на каждый номер отдельный процесс", "SSL进程输出信息": "Информация о процессе SSL", "SoVITS模型列表": "Список моделей SoVITS", "SoVITS训练进程输出信息": "Информация о процессе обучения SoVITS", "TTS推理WebUI进程输出信息": "Информация о процессе TTS инференса WebUI", "TTS推理进程已关闭": "Процесс TTS-инференции остановлен", "TTS推理进程已开启": "Процесс TTS-инференции запущен", "UVR5已关闭": "UVR5 выключен", "UVR5已开启": "UVR5 включен", "UVR5进程输出信息": "Вывод информации процесса UVR5", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix:Какая доля нормализованного аудио смешивается", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size:Как рассчитывается кривая громкости, чем меньше, тем выше точность и больше вычислительная нагрузка (большая точность не всегда означает лучший результат)", "max:归一化后最大值多少": "max:Максимальное значение после нормализации", "max_sil_kept:切完后静音最多留多长": "max_sil_kept:Максимальная длительность тишины после разреза", "min_interval:最短切割间隔": "min_interval:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ный интервал разреза", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length:Минимальная длина каждого отрезка; если первый отрезок слишком короткий, он будет соединен с последующими до достижения этого значения", "temperature": "temperature", "threshold:音量小于这个值视作静音的备选切割点": "threshold:Значение громкости ниже этого считается тишиной для альтернативной точки разреза", "top_k": "top_k", "top_p": "top_p", "一键三连进程输出信息": "Информация о процессе одного нажатия", "不切": "Не разрезать", "中文": "Китайский", "中文教程文档：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e": "Документация на китайском языке：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e", "中英混合": "Китайский и английский", "也可批量输入音频文件, 二选一, 优先读文件夹": "Можно также импортировать несколько аудиофайлов. Если путь к папке существует, то этот ввод игнорируется.", "人声伴奏分离批量处理， 使用UVR5模型。": "Обработка разделения вокала и аккомпанемента пакетно с использованием модели UVR5.", "人声提取激进程度": "Степень агрессивности извлечения вокала", "以下文件或文件夹不存在": "Такого файла или папки не существует", "以下模型不存在:": "Этот модель не существует", "伴奏人声分离&去混响&去回声": "Разделение вокала/аккомпанемента и удаление эхо", "使用无参考文本模式时建议使用微调的GPT，听不清参考音频说的啥(不晓得写啥)可以开。<br>开启后无视填写的参考文本。": "При использовании режима без референсного текста рекомендуется использовать настроенную модель GPT. Если не удается разобрать, что говорит референсное аудио (не знаете, что писать), можете включить этот режим, и он проигнорирует введенный референсный текст.", "保存频率save_every_epoch": "Частота сохранения save_every_epoch", "关闭TTS推理WebUI": "Закрыть TTS Inference WebUI", "关闭UVR5-WebUI": "Закрыть UVR5-WebUI", "关闭打标WebUI": "Закрыть Labeling WebUI", "凑50字一切": "Соберите все в 50 символов", "凑四句一切": "Собрать четыре предложения и разрезать", "切分后的子音频的输出根目录": "Корневой каталог вывода для подаудио после разделения", "切割使用的进程数": "Количество процессов, используемых для разрезания", "刷新模型路径": "Обновить путь к модели", "前端处理后的文本(每句):": "Текст после предварительной обработки (каждое предложение):", "去混响/去延迟，附：": "Удаление реверберации/удаление задержки, примечание:", "参考音频在3~10秒范围外，请更换！": "Референтное аудио вне диапазона 3~10 секунд, пожалуйста, замените!", "参考音频的文本": "Текст референтного аудио", "参考音频的语种": "Язык референтного аудио", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "Необязательно: загрузите несколько файлов с эталонными аудиозаписями, перетащив их (рекомендуется одного пола), и усредните их тон. Если этот параметр не заполнен, тон будет контролироваться одной эталонной аудиозаписью слева. При тонкой настройке модели рекомендуется, чтобы все эталонные аудиозаписи имели тон в пределах обучающего набора для тонкой настройки; предварительно обученную модель можно игнорировать.", "合成语音": "Синтезированный голос", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "Пример допустимого формата пути к папке: E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例 (просто скопируйте из адресной строки файлового менеджера).", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "Заполните каталог, где находятся аудиофайлы после разрезания! Полный путь к читаемым аудиофайлам = каталог - файл .list, имя файла соответствует волне (не полный путь). Если оставить пустым, будет использоваться абсолютный путь из файла .list.", "多语种混合": "Смешанные языки", "多语种混合(粤语)": "Многоязычная смесь (кантонский)", "实际输入的参考文本:": "Фактически введенный референсный текст:", "实际输入的目标文本(切句后):": "Фактически введенный целевой текст (после разбиения на предложения):", "实际输入的目标文本(每句):": "Фактически введенный целевой текст (каждое предложение):", "实际输入的目标文本:": "Фактически введенный целевой текст:", "导出文件格式": "Формат выходных файлов", "开启GPT训练": "Включить обучение GPT", "开启SSL提取": "Включить извлечение SSL", "开启SoVITS训练": "Включить обучение SoVITS", "开启TTS推理WebUI": "Открыть TTS Inference WebUI", "开启UVR5-WebUI": "Открыть UVR5-WebUI", "开启一键三连": "Включить одно нажатие", "开启打标WebUI": "Открыть Labeling WebUI", "开启文本获取": "Включить получение текста", "开启无参考文本模式。不填参考文本亦相当于开启。": "Включить режим без референтного текста. Не заполняя референтный текст, вы также включаете этот режим.", "开启离线批量ASR": "Включить пакетную офлайн ASR", "开启语义token提取": "Включить извлечение семантических токенов", "开启语音切割": "Включить разрезание речи", "开启语音降噪": "Включить шумоподавление", "怎么切": "Как разрезать", "总训练轮数total_epoch": "Общее количество эпох обучения total_epoch", "总训练轮数total_epoch，不建议太高": "Общее количество эпох обучения total_epoch, не рекомендуется слишком высокое", "打标工具WebUI已关闭": "WebUI инструмента маркировки остановлен", "打标工具WebUI已开启": "WebUI инструмента маркировки запущен", "打标工具进程输出信息": "Информация о процессе аннотации", "指定输出主人声文件夹": "Путь к папке для сохранения вокала:", "指定输出非主人声文件夹": "Путь к папке для сохранения аккомпанемента:", "按中文句号。切": "Разделение по китайским точкам.", "按标点符号切": "Разрезать по пунктуационным знакам", "按英文句号.切": "Разрезать по английской точке.", "数据类型精度": "точность типа данных", "文本模块学习率权重": "Веса скорости обучения текстового модуля", "文本进程输出信息": "Информация о процессе обработки текста", "施工中，请静候佳音": "В разработке, ожидайте хороших новостей", "日文": "Японский", "日英混合": "Японский и английский", "是否仅保存最新的ckpt文件以节省硬盘空间": "Сохранять только последние файлы ckpt для экономии места на диске?", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Сохранять финальную версию модели в папке weights на каждом этапе сохранения?", "是否开启dpo训练选项(实验性)": "Включить опцию тренировки dpo (экспериментально)", "是否直接对上次合成结果调整语速和音色。防止随机性。": "Настройте скорость речи и тон последнего результата синтеза, чтобы избежать случайности.", "显卡信息": "Информация о видеокарте", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "Это программное обеспечение открыто по лицензии MIT, автор не имеет никакого контроля над программным обеспечением, пользователи программного обеспечения и те, кто распространяет звуки, экспортированные программным обеспечением, несут полную ответственность. <br>Если вы не согласны с этими условиями, вы не можете использовать или ссылаться на любой код и файлы в пакете программного обеспечения. Смотрите <b>LICENSE</b> в корневом каталоге.", "模型": "Модели", "模型分为三类：": "Модели делятся на три типа:", "模型切换": "Переключение модели", "每张显卡的batch_size": "Размер пакета для каждой видеокарты", "版本": "Версия", "粤英混合": "Кантоно-английская смесь", "粤语": "Кантонийский", "终止ASR进程": "Прекратить процесс ASR", "终止GPT训练": "Прекратить обучение GPT", "终止SSL提取进程": "Прекратить процесс извлечения SSL", "终止SoVITS训练": "Прекратить обучение SoVITS", "终止一键三连": "Прекратить одно нажатие", "终止文本获取进程": "Прекратить процесс получения текста", "终止语义token提取进程": "Прекратить процесс извлечения семантических токенов", "终止语音切割": "Прекратить разрезание речи", "终止语音降噪进程": "Прекратить процесс шумоподавления", "缺少Hubert数据集": "Отсутствует набор данных Hubert", "缺少语义数据集": "Отсутствует семантический набор данных", "缺少音素数据集": "Отсутствует набор данных фонем", "缺少音频数据集": "Отсутствует набор данных аудио", "英文": "Английский", "语义token提取进程输出信息": "Информация о процессе извлечения семантических токенов", "语速": "Скорость речи", "语速调整，高为更快": "Регулировка скорости речи, чем выше, тем быстрее", "语音切割进程输出信息": "Информация о процессе разрезания речи", "语音降噪进程输出信息": "Информация о процессе шумоподавления", "请上传3~10秒内参考音频，超过会报错！": "Пожалуйста, загрузите референтное аудио длительностью от 3 до 10 секунд, иначе будет ошибка!", "请上传参考音频": "Пожалуйста, загрузите эталонное аудио", "请填入推理文本": "Пожалуйста, введите целевой текст", "请填入正确的List路径": "Пожалуйста, укажите правильный путь к списку", "请填入正确的音频文件夹路径": "Пожалуйста, укажите правильный путь к папке с аудио", "请输入有效文本": "Введите действительный текст", "路径不能为空": "Ожида<PERSON><PERSON><PERSON>я, что путь не будет пустым", "路径错误": "Ошибка пути", "转换": "Преобразовать", "输入待处理音频文件夹路径": "Путь к папке с аудиофайлами для обработки:", "输入文件夹路径": "Введите путь к папке", "输出logs/实验名目录下应有23456开头的文件和文件夹": "В директории logs/имя_эксперимента должны быть файлы и папки, начинающиеся с 23456", "输出信息": "Статистика", "输出文件夹路径": "Путь к папке для вывода", "输出的语音": "Выводимый звук", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "Выберите модель, сохраненную в SoVITS_weights и GPT_weights после обучения. По умолчанию используется базовая модель для 5-секундного Zero Shot TTS.", "降噪结果输出文件夹": "Папка для вывода результатов шумоподавления", "降噪音频文件输入文件夹": "Папка для ввода аудиофайлов для шумоподавления", "需要合成的文本": "Текст для синтеза", "需要合成的语种": "Язык для синтеза", "韩文": "Корейский", "韩英混合": "Корейско-английская смесь", "音频加载失败": "Не удалось загрузить аудио", "音频自动切分输入路径，可文件可文件夹": "Путь ввода для автоматического разделения аудио, может быть файлом или папкой", "预训练的GPT模型路径": "Путь к предварительно обученной модели GPT", "预训练的SSL模型路径": "Путь к предварительно обученной модели SSL", "预训练的SoVITS-D模型路径": "Путь к предварительно обученной модели SoVITS-D", "预训练的SoVITS-G模型路径": "Путь к предварительно обученной модели SoVITS-G", "预训练的中文BERT模型路径": "Путь к предварительно обученной китайской модели BERT"}