{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net (onnx_dereverb): È la scelta migliore per la riverberazione a due canali, ma non può rimuovere la riverberazione a canale singolo;", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho: Rimuove gli effetti di ritardo. Aggressive è più completo di Normal nella rimozione, DeReverb rimuove ulteriormente la riverberazione, può rimuovere la riverberazione a canale singolo, ma non rimuove completamente la riverberazione a piastra ad alta frequenza.", "*GPT模型列表": "*Lista dei modelli GPT", "*SoVITS模型列表": "*Lista dei modelli SoVITS", "*实验/模型名": "*Nome dell'esperimento/modello", "*文本标注文件": "*File di annotazione del testo", "*训练集音频文件目录": "*Directory dei file audio del set di addestramento", "*请上传并填写参考信息": "*Carica e compila le informazioni di riferimento", "*请填写需要合成的目标文本和语种模式": "*Si prega di inserire il testo di destinazione da sintetizzare e la modalità lingua", ".list标注文件的路径": "Percorso del file di annotazione .list", ".限制范围越小判别效果越好。": "Meno multilingue è meglio", "0-前置数据集获取工具": "0-Strumento di acquisizione del dataset preliminare", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0a-Strumento di separazione voce e accompagnamento UVR5 & Rimozione riverbero e ritardo", "0b-语音切分工具": "0b-Strumento di segmentazione vocale", "0bb-语音降噪工具": "0bb-Strumento di riduzione del rumore vocale", "0c-中文批量离线ASR工具": "0c-Strumento di ASR offline batch in cinese", "0d-语音文本校对标注工具": "0d-Strumento di correzione e annotazione testo vocale", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1A-训练集格式化工具": "1A-Strumento di formattazione del set di addestramento", "1Aa-文本内容": "1Aa-Contenuto del testo", "1Aabc-训练集格式化一键三连": "1Aabc-Strumento di formattazione del set di addestramento con tre passaggi", "1Ab-SSL自监督特征提取": "1Ab-Estrazione di caratteristiche auto-supervisionata SSL", "1Ac-语义token提取": "1Ac-Estrazione del token semantico", "1B-微调训练": "1B-Allenamento di affinamento", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1Ba-Allenamento di SoVITS. I file del modello destinati alla condivisione sono salvati in SoVITS_weights.", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1Bb-Allenamento di GPT. I file del modello destinati alla condivisione sono salvati in GPT_weights.", "1C-推理": "1C-Inferenza", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1. Il tempo di elaborazione del modello DeEcho-DeReverb è quasi il doppio di quello degli altri due modelli DeEcho;", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1. Conserva la voce principale: scegli questa opzione per audio senza armonie, poiché conserva meglio la voce principale rispetto al modello HP5. Include due modelli integrati, HP2 e HP3. HP3 potrebbe far passare leggermente l'accompagnamento ma conserva meglio la voce principale rispetto a HP2;", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-Voce modificata", "2、MDX-Net-Dereverb模型挺慢的；": "2. <PERSON> modello MDX-Net-Dereverb è piuttosto lento;", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2. Solo conserva la voce principale: scegli questa opzione per audio con armonie, poiché potrebbe indebolire la voce principale. Include un modello HP5;", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3. La configurazione più pulita consigliata è MDX-Net seguito da DeEcho-Aggressive.", "3、去混响、去延迟模型（by FoxJoy）：": "3. <PERSON><PERSON> per rimuovere la riverberazione e il ritardo (by <PERSON><PERSON><PERSON>):", "ASR 模型": "Modello ASR", "ASR 模型尺寸": "Dimensioni del modello ASR", "ASR 语言设置": "Impostazioni linguistiche ASR", "ASR进程输出信息": "Informazioni sull'output del processo ASR", "GPT模型列表": "Elenco dei modelli GPT", "GPT训练进程输出信息": "Informazioni sull'output del processo di allenamento di GPT", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "Parametri di campionamento di GPT (non troppo bassi quando non c'è testo di riferimento. Utilizzare i valori predefiniti in caso di incertezza):", "GPU卡号,只能填1个整数": "Numero della scheda grafica, può essere inserito solo un numero intero", "GPU卡号以-分割，每个卡号一个进程": "Numero di GPU separati da '-'; ogni numero corrisponde a un processo", "SSL进程输出信息": "Informazioni sull'output del processo SSL", "SoVITS模型列表": "Elenco dei modelli SoVITS", "SoVITS训练进程输出信息": "Informazioni sull'output del processo di allenamento di SoVITS", "TTS推理WebUI进程输出信息": "Informazioni sull'output del processo dell'interfaccia utente Web per l'inferenza TTS", "TTS推理进程已关闭": "Il processo di inferenza TTS è stato chiuso", "TTS推理进程已开启": "Il processo di inferenza TTS è stato avviato", "UVR5已关闭": "UVR5 è disattivato", "UVR5已开启": "UVR5 è attivato", "UVR5进程输出信息": "Informazioni sull'output del processo UVR5", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix: Quanta proporzione dell'audio normalizzato deve essere miscelata", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size: Come calcolare la curva del volume. <PERSON><PERSON> piccolo <PERSON>, maggiore è la precisione ma aumenta la complessità computazionale (non significa che una maggiore precisione dà risultati migliori)", "max:归一化后最大值多少": "max: <PERSON><PERSON> valore dopo la normalizzazione", "max_sil_kept:切完后静音最多留多长": "max_sil_kept: Massima durata del silenzio dopo il taglio", "min_interval:最短切割间隔": "min_interval: Intervallo minimo di taglio", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length: <PERSON><PERSON><PERSON><PERSON> minima per segmento; se il primo segmento è troppo corto, sarà unito ai segmenti successivi fino a superare questo valore", "temperature": "temperatura", "threshold:音量小于这个值视作静音的备选切割点": "threshold: Punto di taglio alternativo considerato silenzioso se il volume è inferiore a questo valore", "top_k": "top_k", "top_p": "top_p", "一键三连进程输出信息": "Informazioni sull'output del processo di 'One Click Three Connect'", "不切": "Nessuna suddivisione", "中文": "Cinese", "中文教程文档：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e": "Documentazione del tutorial in cinese：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e", "中英混合": "Cinese e inglese misti", "也可批量输入音频文件, 二选一, 优先读文件夹": "È possibile anche inserire file audio in batch, una delle due opzioni, con priorità alla lettura della cartella", "人声伴奏分离批量处理， 使用UVR5模型。": "Separazione voce-accompagnamento in batch, utilizza il modello UVR5.", "人声提取激进程度": "Grado di aggressività dell'estrazione vocale", "以下文件或文件夹不存在": "Nessun File o Cartella di Questo Tipo", "以下模型不存在:": "<PERSON><PERSON><PERSON> del Genere:", "伴奏人声分离&去混响&去回声": "Separazione tra accompagnamento e voce & Rimozione dell'eco & Rimozione dell'eco", "使用无参考文本模式时建议使用微调的GPT，听不清参考音频说的啥(不晓得写啥)可以开。<br>开启后无视填写的参考文本。": "Si consiglia di utilizzare GPT fine-tuned quando si utilizza la modalità senza testo di riferimento. Se non si riesce a capire cosa dice l'audio di riferimento (e non si sa cosa scrivere), è possibile abilitare questa opzione, ignorando il testo di riferimento inserito.", "保存频率save_every_epoch": "Frequenza di salvataggio ogni epoca", "关闭TTS推理WebUI": "Chiudere TTS Inference WebUI", "关闭UVR5-WebUI": "Chiudere UVR5-WebUI", "关闭打标WebUI": "Chiudere Labeling WebUI", "凑50字一切": "Riempire con 50 caratteri per tutto", "凑四句一切": "Riempire con quattro frasi per tutto", "切分后的子音频的输出根目录": "Directory radice di output per gli audio segmentati", "切割使用的进程数": "Numero di processi utilizzati per il taglio", "刷新模型路径": "Aggiorna il percorso del modello", "前端处理后的文本(每句):": "<PERSON>o elaborato dal front-end (per frase):", "去混响/去延迟，附：": "Rimozione della riverberazione/ritardo, allegato:", "参考音频在3~10秒范围外，请更换！": "L'audio di riferimento è al di fuori dell'intervallo di 3-10 secondi. Si prega di cambiarlo!", "参考音频的文本": "Testo dell'audio di riferimento", "参考音频的语种": "Lingua dell'audio di riferimento", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "Opzionale: Carica più file audio di riferimento trascinandoli (si consiglia dello stesso genere) e media il loro tono. Se questa opzione è lasciata vuota, il tono sarà controllato dal singolo file audio di riferimento a sinistra. Se si sta perfezionando il modello, è consigliato che tutti i file audio di riferimento abbiano toni presenti nel set di addestramento per il perfezionamento; il modello pre-addestrato può essere ignorato.", "合成语音": "Sintesi vocale", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "Formato di percorso della cartella valido: E:\\codes\\py39\\vits_vc_gpu\\Esempio di test di BaiLuShuangHua (copiare direttamente dalla barra degli indirizzi del gestore file).", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "Inserisci la directory dell'audio segmentato! Il percorso completo del file audio letto = questa directory - unione del nome del file corrispondente alle forme d'onda nel file .list (non il percorso completo). Se lasciato vuoto, verrà utilizzato il percorso assoluto nel file .list.", "多语种混合": "Mix multilingue", "多语种混合(粤语)": "<PERSON><PERSON> (Cantonese)", "实际输入的参考文本:": "Testo di riferimento effettivamente inserito:", "实际输入的目标文本(切句后):": "Testo di destinazione effettivamente inserito (dopo il taglio delle frasi):", "实际输入的目标文本(每句):": "Testo di destinazione effettivamente inserito (per frase):", "实际输入的目标文本:": "Testo di destinazione effettivamente inserito:", "导出文件格式": "Formato di esportazione del file", "开启GPT训练": "Attivare l'allenamento di GPT", "开启SSL提取": "Attivare l'estrazione SSL", "开启SoVITS训练": "Attivare l'allenamento di SoVITS", "开启TTS推理WebUI": "Aprire TTS Inference WebUI", "开启UVR5-WebUI": "Aprire UVR5-WebUI", "开启一键三连": "Attivare la formattazione con tre passaggi", "开启打标WebUI": "Aprire Labeling WebUI", "开启文本获取": "Attivare l'estrazione del testo", "开启无参考文本模式。不填参考文本亦相当于开启。": "Attivare la modalità senza testo di riferimento. Anche se non inserisci un testo di riferimento, la modalità verrà attivata.", "开启离线批量ASR": "Attivare ASR offline batch", "开启语义token提取": "Attivare l'estrazione del token semantico", "开启语音切割": "Attivare la segmentazione vocale", "开启语音降噪": "Attivare la riduzione del rumore vocale", "怎么切": "Come tagliare", "总训练轮数total_epoch": "Numero totale di epoche di addestramento", "总训练轮数total_epoch，不建议太高": "Numero totale di epoche di addestramento, non raccomandato troppo alto", "打标工具WebUI已关闭": "L'interfaccia utente Web dello strumento di annotazione è stata chiusa", "打标工具WebUI已开启": "L'interfaccia utente Web dello strumento di annotazione è stata avviata", "打标工具进程输出信息": "Informazioni sull'output del processo di annotazione", "指定输出主人声文件夹": "Specifica la cartella di output per la voce principale", "指定输出非主人声文件夹": "Specifica la cartella di output per la non voce principale", "按中文句号。切": "Taglia secondo il punto cinese.", "按标点符号切": "Taglia secondo i segni di punteggiatura", "按英文句号.切": "Taglia secondo il punto inglese", "数据类型精度": "precisione del tipo di dati", "文本模块学习率权重": "Peso del tasso di apprendimento del modulo di testo", "文本进程输出信息": "Informazioni sull'output del processo di estrazione del testo", "施工中，请静候佳音": "In costruzione, attendi pazientemente le buone notizie", "日文": "Giapponese", "日英混合": "Mix giapponese e inglese", "是否仅保存最新的ckpt文件以节省硬盘空间": "<PERSON><PERSON><PERSON> solo il file ckpt più recente per risparmiare spazio su disco", "是否在每次保存时间点将最终小模型保存至weights文件夹": "<PERSON><PERSON><PERSON> il modello finale più piccolo nella cartella weights ad ogni punto di salvataggio", "是否开启dpo训练选项(实验性)": "Attivare l'opzione di addestramento DPO (sperimentale)", "是否直接对上次合成结果调整语速和音色。防止随机性。": "Regola la velocità del parlato e il tono dell'ultimo risultato di sintesi per prevenire la casualità.", "显卡信息": "Informazioni sulla scheda grafica", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "Questo software è open source con licenza MIT. L'autore non ha alcun controllo sul software. L'utente che utilizza il software o diffonde i suoni derivati dal software ne è responsabile. <br>Se non accetti questi termini, non puoi utilizzare o citare alcun codice o file all'interno del pacchetto software. Vedi la cartella principale<b>LICENSE</b> per i dettagli.", "模型": "<PERSON><PERSON>", "模型分为三类：": "I modelli sono divisi in tre categorie:", "模型切换": "Cambio del modello", "每张显卡的batch_size": "Batch size per ogni scheda grafica", "版本": "Versione", "粤英混合": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "粤语": "Cantonese", "终止ASR进程": "Terminare il processo ASR", "终止GPT训练": "Terminare l'allenamento di GPT", "终止SSL提取进程": "Terminare il processo di estrazione SSL", "终止SoVITS训练": "Terminare l'allenamento di SoVITS", "终止一键三连": "Terminare la formattazione con tre passaggi", "终止文本获取进程": "Terminare il processo di estrazione del testo", "终止语义token提取进程": "Terminare il processo di estrazione del token semantico", "终止语音切割": "Terminare la segmentazione vocale", "终止语音降噪进程": "Termina il processo di riduzione del rumore vocale", "缺少Hubert数据集": "Dataset di Hubert <PERSON>", "缺少语义数据集": "Dataset Semantico Mancante", "缺少音素数据集": "Dataset di Fonemi Mancante", "缺少音频数据集": "Dataset Audio Mancante", "英文": "<PERSON><PERSON><PERSON>", "语义token提取进程输出信息": "Informazioni sull'output del processo di estrazione del token semantico", "语速": "Velocità della voce", "语速调整，高为更快": "Regolare la velocità della voce, più alta per più veloce", "语音切割进程输出信息": "Informazioni sull'output del processo di segmentazione vocale", "语音降噪进程输出信息": "Informazioni sull'output del processo di riduzione del rumore vocale", "请上传3~10秒内参考音频，超过会报错！": "Carica un audio di riferimento della durata compresa tra 3 e 10 secondi. Superiore a questo, verrà generato un errore!", "请上传参考音频": "Si prega di caricare l'audio di riferimento", "请填入推理文本": "Si prega di inserire il testo di destinazione", "请填入正确的List路径": "Si Prega di Inserire il Percorso Corretto della Lista", "请填入正确的音频文件夹路径": "Si Prega di Inserire il Percorso Corretto della Cartella Audio", "请输入有效文本": "Inserisci un testo valido", "路径不能为空": "Percorso Vuoto Non Consentito", "路径错误": "Errore di Percorso", "转换": "<PERSON><PERSON><PERSON>", "输入待处理音频文件夹路径": "Inserisci il percorso della cartella dei file audio da elaborare", "输入文件夹路径": "Inserisci il percorso della cartella", "输出logs/实验名目录下应有23456开头的文件和文件夹": "Nella cartella logs/nome dell'esperimento dovrebbero esserci file e cartelle che iniziano con 23456", "输出信息": "Informazioni di output", "输出文件夹路径": "Percorso della cartella di output", "输出的语音": "Audio di output", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "<PERSON><PERSON><PERSON> il modello salvato in SoVITS_weights e GPT_weights dopo l'addestramento. Uno di default è il modello di base, utilizzato per l'esperienza di Zero Shot TTS in 5 secondi.", "降噪结果输出文件夹": "Cartella di output dei risultati di riduzione del rumore", "降噪音频文件输入文件夹": "Cartella di input dei file audio per la riduzione del rumore", "需要合成的文本": "<PERSON>o da sintetizzare", "需要合成的语种": "Lingua da sintetizzare", "韩文": "<PERSON><PERSON>", "韩英混合": "<PERSON><PERSON>", "音频加载失败": "Caricamento Audio Fallito", "音频自动切分输入路径，可文件可文件夹": "Percorso di input per la segmentazione automatica dell'audio, può essere un file o una cartella", "预训练的GPT模型路径": "Percorso del modello preaddestrato GPT", "预训练的SSL模型路径": "Percorso del modello SSL preaddestrato", "预训练的SoVITS-D模型路径": "Percorso del modello preaddestrato SoVITS-D", "预训练的SoVITS-G模型路径": "Percorso del modello preaddestrato SoVITS-G", "预训练的中文BERT模型路径": "Percorso del modello BERT cinese preaddestrato"}