{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1) MDX-Net (onnx_dereverb) : C'est le meilleur choix pour la réverbération à deux canaux, mais il ne peut pas éliminer la réverbération à un seul canal;", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho : Supprime les effets de délai. Aggressive est plus exhaustif que Normal dans la suppression, DeReverb élimine également la réverbération, peut supprimer la réverbération monocanal, mais n'élimine pas complètement la réverbération de plaque à haute fréquence.", "*GPT模型列表": "*Liste des modèles GPT", "*SoVITS模型列表": "*Liste des modèles SoVITS", "*实验/模型名": "*Nom de l'expérience/modèle", "*文本标注文件": "*Fichier d'annotation de texte", "*训练集音频文件目录": "*Répertoire des fichiers audio d'entraînement", "*请上传并填写参考信息": "*Veuillez télécharger et remplir les informations de référence", "*请填写需要合成的目标文本和语种模式": "*Veuillez saisir le texte cible à synthétiser et le mode de langue.", ".list标注文件的路径": "Chemin du fichier d'annotation .list", ".限制范围越小判别效果越好。": "Moins il y a de langues, mieux c'est", "0-前置数据集获取工具": "0-Outil de récupération de jeu de données préalable", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0a-Outil de séparation de la voix humaine et de l'accompagnement UVR5 & suppression de la réverbération et du retard", "0b-语音切分工具": "0b-<PERSON><PERSON> vocal", "0bb-语音降噪工具": "0bb-<PERSON><PERSON> de réduction du bruit vocal", "0c-中文批量离线ASR工具": "0c-Outil chinois de transcription automatique hors ligne en masse", "0d-语音文本校对标注工具": "0d-Outil de correction et d'annotation de texte vocal", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1A-训练集格式化工具": "1A-Outil de formatage du jeu de données d'entraînement", "1Aa-文本内容": "1Aa-Contenu du texte", "1Aabc-训练集格式化一键三连": "1Aabc-Formatage en un clic du jeu de données d'entraînement", "1Ab-SSL自监督特征提取": "1Ab-Extraction de caractéristiques auto-supervisée SSL", "1Ac-语义token提取": "1Ac-Extraction de jetons sémantiques", "1B-微调训练": "1B-Entraînement fin", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1Ba-Entraînement SoVITS. Les fichiers de modèle destinés au partage sont enregistrés sous SoVITS_weights.", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1Bb-Entraînement GPT. Les fichiers de modèle destinés au partage sont enregistrés sous GPT_weights.", "1C-推理": "1C-Inférence", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1. Le temps de traitement du modèle <PERSON>-DeReverb est presque le double de celui des deux autres modèles DeEcho;", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1. Préserver les voix : Choisissez cette option pour les audio sans harmonie, car elle conserve mieux la voix principale par rapport au modèle HP5. Deux modèles intégrés, HP2 et HP3, sont disponibles. HP3 peut légèrement laisser passer l'accompagnement mais conserve la voix principale un peu mieux que HP2;", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-Modification de la voix", "2、MDX-Net-Dereverb模型挺慢的；": "2. <PERSON> modèle MDX-Net-Dereverb est assez lent;", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2. Conserver uniquement la voix principale : Choisissez cette option pour les audio avec harmonie, car elle peut affaiblir la voix principale. Un modèle HP5 intégré est disponible;", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3. La configuration la plus propre que je recommande est d'utiliser d'abord MDX-Net, puis DeEcho-Aggressive.", "3、去混响、去延迟模型（by FoxJoy）：": "3. Modèle de suppression de réverbération et de retard (par FoxJoy) :", "ASR 模型": "Modèle ASR", "ASR 模型尺寸": "Taille du modèle ASR", "ASR 语言设置": "Paramètres de langue ASR", "ASR进程输出信息": "Informations de processus ASR", "GPT模型列表": "Liste des modèles GPT", "GPT训练进程输出信息": "Informations de processus d'entraînement GPT", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "Paramètres d'échantillonnage de GPT (ne pas mettre trop bas lorsqu'il n'y a pas de texte de référence. Utilisez les valeurs par défaut si vous n'êtes pas sûr):", "GPU卡号,只能填1个整数": "Numéro de carte GPU, ne peut contenir qu'un seul entier", "GPU卡号以-分割，每个卡号一个进程": "Numéro de carte GPU séparé par des tirets, un processus par numéro de carte", "SSL进程输出信息": "Informations de processus SSL", "SoVITS模型列表": "Liste des modèles SoVITS", "SoVITS训练进程输出信息": "Informations de processus d'entraînement SoVITS", "TTS推理WebUI进程输出信息": "Informations de processus de l'interface Web d'inférence TTS", "TTS推理进程已关闭": "Le processus d'inférence TTS est terminé", "TTS推理进程已开启": "Le processus d'inférence TTS est en cours", "UVR5已关闭": "UVR5 est désactivé", "UVR5已开启": "UVR5 est activé", "UVR5进程输出信息": "Informations de processus UVR5", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix: proportion d'audio normalisé mélangé", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size: comment calculer la courbe de volume, plus petit pour une précision plus élevée mais une charge de calcul plus élevée (ce n'est pas une meilleure précision)", "max:归一化后最大值多少": "max: valeur maximale après normalisation", "max_sil_kept:切完后静音最多留多长": "max_sil_kept: durée maximale de silence après la coupe", "min_interval:最短切割间隔": "min_interval: intervalle de coupe minimum", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length:longueur minimale de chaque segment ; si le premier segment est trop court, il est concaténé avec les segments suivants jusqu'à ce que la longueur dépasse cette valeur", "temperature": "température", "threshold:音量小于这个值视作静音的备选切割点": "seuil: le volume inférieur à cette valeur est considéré comme un point de coupe silencieux alternatif", "top_k": "top_k", "top_p": "top_p", "一键三连进程输出信息": "Informations de processus de l'un clic trois connexions", "不切": "Pas de découpe", "中文": "<PERSON><PERSON>", "中文教程文档：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e": "Documentation du tutoriel en chinois：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e", "中英混合": "Mélange de chinois et d'anglais", "也可批量输入音频文件, 二选一, 优先读文件夹": "Également possible d'entrer en lot des fichiers audio, au choix, privilégiez la lecture du dossier", "人声伴奏分离批量处理， 使用UVR5模型。": "Traitement par lot de séparation voix-accompagnement en utilisant le modèle UVR5.", "人声提取激进程度": "Degré d'extraction des voix", "以下文件或文件夹不存在": "<PERSON><PERSON><PERSON> ou Dossier de ce Type", "以下模型不存在:": "Aucun Modèle de ce Type:", "伴奏人声分离&去混响&去回声": "Séparation de la voix et de l'accompagnement, suppression de la réverbération et de l'écho", "使用无参考文本模式时建议使用微调的GPT，听不清参考音频说的啥(不晓得写啥)可以开。<br>开启后无视填写的参考文本。": "Il est recommandé d'utiliser GPT finement ajusté en mode sans texte de référence. Si vous ne comprenez pas ce que dit l'audio de référence (vous ne savez pas quoi écrire), vous pouvez l'activer ; une fois activé, ignorez le texte de référence saisi.", "保存频率save_every_epoch": "<PERSON><PERSON><PERSON> de sauvegarde (sauvegarder à chaque époque)", "关闭TTS推理WebUI": "Fermer TTS Inference WebUI", "关闭UVR5-WebUI": "<PERSON>rmer <PERSON>R5-WebUI", "关闭打标WebUI": "Fermer Labeling WebUI", "凑50字一切": "Assembler 50 mots tout", "凑四句一切": "Composez quatre phrases pour tout remplir", "切分后的子音频的输出根目录": "Répertoire racine de sortie des sous-audios après découpage", "切割使用的进程数": "Nombre de processus utilisés pour le découpage", "刷新模型路径": "Actualiser le chemin du modèle", "前端处理后的文本(每句):": "Texte après traitement frontal (par phrase):", "去混响/去延迟，附：": "Suppression de la réverbération / suppression du retard, ci-joint:", "参考音频在3~10秒范围外，请更换！": "Veuillez remplacer l'audio de référence si sa durée est en dehors de la plage de 3 à 10 secondes!", "参考音频的文本": "Texte de l'audio de référence", "参考音频的语种": "Langue de l'audio de référence", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "Optionnel : Téléchargez plusieurs fichiers audio de référence en les faisant glisser (recommandé d'être du même genre) et fusionnez leur tonalité. Si cette option est laissée vide, la tonalité sera contrôlée par l'unique fichier audio de référence à gauche. Si vous ajustez le modèle, il est recommandé que tous les fichiers audio de référence aient des tonalités dans l'ensemble d'entraînement d'ajustement ; le modèle pré-entrainé peut être ignoré.", "合成语音": "Synthèse vocale", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "Exemple de format de chemin de dossier valide : E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例 (copiez-le depuis la barre d'adresse de l'explorateur de fichiers).", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "Veuillez indiquer le répertoire contenant les audio découpés ! Le chemin complet du fichier audio à lire = ce répertoire - nom du fichier correspondant à l'onde dans le fichier .list (pas le chemin complet). Si laissé vide, le chemin absolu dans le fichier .list sera utilisé.", "多语种混合": "Mélange multilingue", "多语种混合(粤语)": "Mélange Multilingue (Cantonais)", "实际输入的参考文本:": "Texte de référence réellement saisi:", "实际输入的目标文本(切句后):": "Texte cible réellement saisi (après découpage):", "实际输入的目标文本(每句):": "Texte cible réellement saisi (par phrase):", "实际输入的目标文本:": "Texte cible réellement saisi:", "导出文件格式": "Format d'exportation du fichier", "开启GPT训练": "Activer l'entraînement GPT", "开启SSL提取": "Activer l'extraction SSL", "开启SoVITS训练": "Activer l'entraînement SoVITS", "开启TTS推理WebUI": "Ouvrir TTS Inference WebUI", "开启UVR5-WebUI": "Ouvrir UVR5-WebUI", "开启一键三连": "Activer l'un clic trois connexions", "开启打标WebUI": "Ouvrir Labeling WebUI", "开启文本获取": "Activer l'extraction de texte", "开启无参考文本模式。不填参考文本亦相当于开启。": "Activer le mode sans texte de référence. Laisser le texte de référence vide équivaut également à activer le mode.", "开启离线批量ASR": "Activer la transcription automatique hors ligne en masse", "开启语义token提取": "Activer l'extraction de jetons sémantiques", "开启语音切割": "<PERSON><PERSON> le dé<PERSON>upage vocal", "开启语音降噪": "<PERSON><PERSON> la réduction de bruit vocal", "怎么切": "Comment d<PERSON>", "总训练轮数total_epoch": "Nombre total d'époques d'entraînement", "总训练轮数total_epoch，不建议太高": "Nombre total d'époques d'entraînement, pas recommandé d'être trop élevé", "打标工具WebUI已关闭": "L'interface Web de l'outil d'annotation est terminée", "打标工具WebUI已开启": "L'interface Web de l'outil d'annotation est en cours", "打标工具进程输出信息": "Informations de processus de l'outil d'annotation", "指定输出主人声文件夹": "Spécifier le dossier de sortie pour la voix principale", "指定输出非主人声文件夹": "Spécifier le dossier de sortie pour la non-voix principale", "按中文句号。切": "Couper selon les points en chinois.", "按标点符号切": "Couper selon les signes de ponctuation", "按英文句号.切": "Découpez par des points en anglais", "数据类型精度": "précision du type de données", "文本模块学习率权重": "Poids du taux d'apprentissage du module de texte", "文本进程输出信息": "Informations de processus de texte", "施工中，请静候佳音": "En construction, veuillez attendre patiemment", "日文": "Japonais", "日英混合": "<PERSON><PERSON><PERSON><PERSON>", "是否仅保存最新的ckpt文件以节省硬盘空间": "Sauvegarder uniquement le dernier fichier ckpt pour économiser de l'espace disque", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Sauvegarder le petit modèle final dans le dossier weights à chaque point de sauvegarde", "是否开启dpo训练选项(实验性)": "Activer l'option d'entraînement DPO (expérimental)", "是否直接对上次合成结果调整语速和音色。防止随机性。": "Ajuster la vitesse de parole et la tonalité du dernier résultat de synthèse pour prévenir l'aléatoire.", "显卡信息": "Informations sur la carte graphique", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "Ce logiciel est open source sous la licence MIT. L'auteur n'a aucun contrôle sur le logiciel. Les utilisateurs et les diffuseurs du son exporté par le logiciel en assument l'entière responsabilité. <br>Si vous n'acceptez pas ces termes, vous ne pouvez ni utiliser ni citer aucun code ou fichier à l'intérieur du package. Voir <b>LICENSE</b> dans le répertoire racine pour plus de détails.", "模型": "<PERSON><PERSON><PERSON><PERSON>", "模型分为三类：": "Les modèles sont classés en trois catégories:", "模型切换": "Changement de modèle", "每张显卡的batch_size": "Taille de lot par carte graphique", "版本": "Version", "粤英混合": "Mélange Cantonais-Anglais", "粤语": "Cantonais", "终止ASR进程": "Arrêter le processus ASR", "终止GPT训练": "Arrêter l'entraînement GPT", "终止SSL提取进程": "<PERSON><PERSON><PERSON><PERSON> le processus d'extraction SSL", "终止SoVITS训练": "Arrêter l'entraînement SoVITS", "终止一键三连": "Arr<PERSON><PERSON> l'un clic trois connexions", "终止文本获取进程": "<PERSON><PERSON><PERSON><PERSON> le processus d'extraction de texte", "终止语义token提取进程": "<PERSON><PERSON><PERSON><PERSON> le processus d'extraction de jetons sémantiques", "终止语音切割": "<PERSON><PERSON><PERSON><PERSON> le dé<PERSON>upage vocal", "终止语音降噪进程": "<PERSON><PERSON><PERSON><PERSON> le processus de réduction du bruit vocal", "缺少Hubert数据集": "<PERSON><PERSON> <PERSON><PERSON>", "缺少语义数据集": "<PERSON><PERSON> <PERSON> Don<PERSON>", "缺少音素数据集": "<PERSON><PERSON> <PERSON> Données de Phonèmes Manquant", "缺少音频数据集": "Jeu de Données Audio Manquant", "英文": "<PERSON><PERSON><PERSON>", "语义token提取进程输出信息": "Informations de processus d'extraction de jetons sémantiques", "语速": "Débit de parole", "语速调整，高为更快": "Ajuster la vitesse de parole, plus élevée pour plus rapide", "语音切割进程输出信息": "Informations de processus de découpage vocal", "语音降噪进程输出信息": "Informations de sortie du processus de réduction du bruit vocal", "请上传3~10秒内参考音频，超过会报错！": "Veuillez télécharger une référence audio de 3 à 10 secondes ; les fichiers plus longs généreront une erreur!", "请上传参考音频": "Veuillez télécharger l'audio de référence", "请填入推理文本": "<PERSON><PERSON><PERSON>z remplir le texte cible", "请填入正确的List路径": "Veuillez Remplir le Chemin Correct de la Liste", "请填入正确的音频文件夹路径": "Veuillez Remplir le Chemin Correct du Dossier Audio", "请输入有效文本": "Veuillez entrer un texte valide", "路径不能为空": "Chemin Non Vide Attendu", "路径错误": "<PERSON><PERSON><PERSON>", "转换": "Conversion", "输入待处理音频文件夹路径": "Entrez le chemin du dossier audio à traiter", "输入文件夹路径": "Chemin du dossier à entrer", "输出logs/实验名目录下应有23456开头的文件和文件夹": "Les fichiers et dossiers commençant par 23456 devraient être présents dans le répertoire logs/nom de l'expérience", "输出信息": "Sortie d'information", "输出文件夹路径": "Chemin du dossier de sortie", "输出的语音": "Audio de sortie", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "Choisissez le modèle entraîné stocké sous SoVITS_weights et GPT_weights. Par d<PERSON><PERSON><PERSON>, l'un d'eux est un modèle de base pour l'expérience de TTS Zero Shot de 5 secondes.", "降噪结果输出文件夹": "Dossier de sortie des résultats de réduction du bruit", "降噪音频文件输入文件夹": "Dossier d'entrée des fichiers audio de réduction du bruit", "需要合成的文本": "Texte à synthétiser", "需要合成的语种": "Langue de synthèse requise", "韩文": "<PERSON><PERSON>", "韩英混合": "<PERSON><PERSON><PERSON><PERSON>-Inglés", "音频加载失败": "Échec du Chargement de l'Audio", "音频自动切分输入路径，可文件可文件夹": "Chemin d'entrée automatique de découpage audio, peut être un fichier ou un dossier", "预训练的GPT模型路径": "Chemin du modèle GPT pré-entraîné", "预训练的SSL模型路径": "Chemin du modèle SSL pré-entraîné", "预训练的SoVITS-D模型路径": "Chemin du modèle SoVITS-D pré-entraîné", "预训练的SoVITS-G模型路径": "Chemin du modèle SoVITS-G pré-entraîné", "预训练的中文BERT模型路径": "Chemin du modèle BERT chinois pré-entraîné"}