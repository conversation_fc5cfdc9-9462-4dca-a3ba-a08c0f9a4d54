#!/bin/bash

# 1. 设置环境变量
export CONDA_ROOT=/root/miniconda3
export NLTK_DATA=/alidata1/nltk_data

# 2. 确保 conda 命令可用
eval "$(/root/miniconda3/bin/conda shell.bash hook)"

# 3. 激活环境
conda activate GPTSoVITS_fix

# 4. 切换到项目目录
cd /alidata1/GPT-SoVITS

# 5. 启动服务
python GPT_SoVITS/inference_webui_fast.py --root-path /inference



# #!/bin/bash

# # 激活 Conda 环境
# export CONDA_ROOT=/alidata1/conda_envs
# source ${CONDA_ROOT}/GPTSoVITS_fix/bin/activate

# export NLTK_DATA=/alidata1/nltk_data

# # 切换到项目目录
# cd /alidata1/GPT-SoVITS
# conda init

# conda activate GPTSoVITS_fix

# # 启动推理服务
# python GPT_SoVITS/inference_webui_fast.py --root-path /inference