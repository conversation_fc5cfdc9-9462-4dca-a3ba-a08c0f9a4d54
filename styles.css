/* ===== 基础样式重置 ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* 颜色变量 */
  --primary-color: #00d4ff;
  --secondary-color: #ff6b6b;
  --accent-color: #4ecdc4;
  --bg-primary: #0a0a0a;
  --bg-secondary: #111111;
  --bg-tertiary: #1a1a1a;
  --text-primary: #ffffff;
  --text-secondary: #b0b0b0;
  --text-muted: #666666;
  --border-color: #333333;
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);

  /* 渐变 */
  --gradient-primary: linear-gradient(135deg, #00d4ff 0%, #4ecdc4 100%);
  --gradient-secondary: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
  --gradient-dark: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);

  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-glow: 0 0 20px rgba(0, 212, 255, 0.3);

  /* 字体 */
  --font-primary: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  --font-mono: "JetBrains Mono", "Fira Code", monospace;

  /* 间距 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* 边框半径 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;

  /* 过渡 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

/* ===== 粒子背景 ===== */
#particles-js {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: 0.6;
}

/* ===== 导航栏 ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(10, 10, 10, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  transition: var(--transition-normal);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--text-primary);
  text-decoration: none;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: var(--gradient-primary);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: var(--spacing-xl);
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  position: relative;
  transition: var(--transition-normal);
}

.nav-link:hover {
  color: var(--primary-color);
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: var(--transition-normal);
}

.nav-link:hover::after {
  width: 100%;
}

.theme-toggle {
  width: 40px;
  height: 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--glass-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
}

.theme-toggle:hover {
  background: var(--glass-border);
  border-color: var(--primary-color);
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.bar {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  border-radius: 2px;
  transition: var(--transition-normal);
}

/* ===== 英雄区域 ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  background: radial-gradient(
    ellipse at center,
    rgba(0, 212, 255, 0.1) 0%,
    transparent 70%
  );
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
}

.hero-content {
  z-index: 2;
}

.hero-badge {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-lg);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  font-size: 0.9rem;
  margin-bottom: var(--spacing-lg);
  backdrop-filter: blur(10px);
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: var(--spacing-lg);
}

.title-line-1 {
  display: block;
  color: var(--text-secondary);
  font-size: 0.7em;
  font-weight: 500;
}

.title-line-2 {
  display: block;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-line-3 {
  display: block;
  font-size: 0.6em;
  color: var(--text-secondary);
  font-weight: 400;
}

.hero-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2xl);
  max-width: 500px;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-3xl);
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-lg);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-glow);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
}

.btn-glass {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  color: var(--text-primary);
  backdrop-filter: blur(10px);
}

.btn-glass:hover {
  background: var(--glass-border);
  border-color: var(--primary-color);
}

.hero-stats {
  display: flex;
  gap: var(--spacing-2xl);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  display: block;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-muted);
}

/* ===== 英雄视觉效果 ===== */
.hero-visual {
  position: relative;
  z-index: 1;
}

.terminal-window {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  overflow: hidden;
  box-shadow: var(--shadow-xl);
}

.terminal-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--border-color);
}

.terminal-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.control.red {
  background: #ff5f56;
}
.control.yellow {
  background: #ffbd2e;
}
.control.green {
  background: #27ca3f;
}

.terminal-title {
  font-family: var(--font-mono);
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.terminal-body {
  padding: var(--spacing-lg);
  font-family: var(--font-mono);
  font-size: 0.9rem;
  line-height: 1.8;
}

.terminal-line {
  margin-bottom: var(--spacing-sm);
}

.prompt {
  color: var(--primary-color);
  margin-right: var(--spacing-sm);
}

.path {
  color: var(--accent-color);
  margin-right: var(--spacing-sm);
}

.command {
  color: var(--text-primary);
}

.output {
  color: var(--text-secondary);
}

.output.success {
  color: var(--accent-color);
}

.cursor {
  color: var(--primary-color);
  animation: blink 1s infinite;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

.typing {
  overflow: hidden;
  border-right: 2px solid var(--primary-color);
  white-space: nowrap;
  animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from,
  to {
    border-color: transparent;
  }
  50% {
    border-color: var(--primary-color);
  }
}

/* ===== 浮动技术图标 ===== */
.floating-tech {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.tech-icon {
  position: absolute;
  width: 60px;
  height: 60px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--primary-color);
  backdrop-filter: blur(10px);
  animation: float 6s ease-in-out infinite;
  animation-delay: var(--delay);
}

.tech-icon:nth-child(1) {
  top: 20%;
  left: 10%;
  color: #61dafb;
}

.tech-icon:nth-child(2) {
  top: 60%;
  left: 20%;
  color: #3776ab;
}

.tech-icon:nth-child(3) {
  top: 30%;
  right: 15%;
  color: #f7df1e;
}

.tech-icon:nth-child(4) {
  bottom: 30%;
  right: 25%;
  color: #2496ed;
}

.tech-icon:nth-child(5) {
  bottom: 20%;
  left: 30%;
  color: var(--accent-color);
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(5deg);
  }
  66% {
    transform: translateY(10px) rotate(-3deg);
  }
}

/* ===== 滚动指示器 ===== */
.scroll-indicator {
  position: absolute;
  bottom: var(--spacing-2xl);
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: var(--text-muted);
  animation: bounce 2s infinite;
}

.scroll-text {
  font-size: 0.9rem;
  margin-bottom: var(--spacing-sm);
}

.scroll-arrow {
  font-size: 1.2rem;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* ===== 通用容器和区域 ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

section {
  padding: var(--spacing-3xl) 0;
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.section-badge {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-lg);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  font-size: 0.9rem;
  margin-bottom: var(--spacing-lg);
  backdrop-filter: blur(10px);
}

.section-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  margin-bottom: var(--spacing-lg);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* ===== 精选文章区域 ===== */
.featured-articles {
  background: var(--bg-secondary);
  position: relative;
}

.featured-articles::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--primary-color),
    transparent
  );
}

.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-3xl);
}

.article-card {
  background: var(--bg-tertiary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: var(--transition-normal);
  position: relative;
  cursor: pointer;
}

.article-card:hover {
  transform: translateY(-5px);
  border-color: var(--primary-color);
  box-shadow: 0 20px 40px rgba(0, 212, 255, 0.1);
}

.article-card.featured {
  grid-column: span 2;
}

.article-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.article-card.featured .article-image {
  height: 300px;
}

.article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-slow);
}

.article-card:hover .article-image img {
  transform: scale(1.05);
}

.article-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.7));
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: var(--spacing-lg);
}

.article-category {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-2xl);
  font-size: 0.85rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.article-category.ai {
  background: rgba(78, 205, 196, 0.2);
  border: 1px solid rgba(78, 205, 196, 0.3);
  color: var(--accent-color);
}

.article-category.cloud {
  background: rgba(0, 212, 255, 0.2);
  border: 1px solid rgba(0, 212, 255, 0.3);
  color: var(--primary-color);
}

.article-category.frontend {
  background: rgba(255, 107, 107, 0.2);
  border: 1px solid rgba(255, 107, 107, 0.3);
  color: var(--secondary-color);
}

.article-category.blockchain {
  background: rgba(255, 167, 38, 0.2);
  border: 1px solid rgba(255, 167, 38, 0.3);
  color: #ffa726;
}

.article-content {
  padding: var(--spacing-xl);
}

.article-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  line-height: 1.3;
}

.article-card.featured .article-title {
  font-size: 2rem;
}

.article-excerpt {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

.article-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.article-date,
.article-read-time {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.9rem;
  color: var(--text-muted);
}

.article-date i,
.article-read-time i {
  font-size: 0.8rem;
  color: var(--primary-color);
}

.author-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid var(--border-color);
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.author-title {
  font-size: 0.8rem;
  color: var(--text-muted);
}

.article-stats {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  font-size: 0.85rem;
  color: var(--text-muted);
}

.read-time,
.publish-date,
.view-count {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.article-tags {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.view-all {
  text-align: center;
}

.btn-outline-glow {
  background: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  position: relative;
  overflow: hidden;
}

.btn-outline-glow::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 212, 255, 0.2),
    transparent
  );
  transition: var(--transition-slow);
}

.btn-outline-glow:hover::before {
  left: 100%;
}

.btn-outline-glow:hover {
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  transform: translateY(-2px);
}

/* ===== 响应式设计 ===== */
@media (max-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    text-align: center;
  }

  .hero-visual {
    order: -1;
  }

  .articles-grid {
    grid-template-columns: 1fr;
  }

  .article-card.featured {
    grid-column: span 1;
  }

  .tech-icon {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(20px);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: var(--spacing-3xl);
    transition: var(--transition-normal);
  }

  .nav-menu.active {
    left: 0;
  }

  .hamburger {
    display: flex;
  }

  .hamburger.active .bar:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .hamburger.active .bar:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active .bar:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .hero-stats {
    justify-content: center;
  }

  .terminal-window {
    max-width: 100%;
  }

  .floating-tech {
    display: none;
  }

  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .article-stats {
    flex-wrap: wrap;
    gap: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0 var(--spacing-md);
  }

  .container {
    padding: 0 var(--spacing-md);
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .btn {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 0.9rem;
  }

  .terminal-body {
    padding: var(--spacing-md);
    font-size: 0.8rem;
  }

  .articles-grid {
    gap: var(--spacing-lg);
  }

  .article-content {
    padding: var(--spacing-lg);
  }

  .article-title {
    font-size: 1.3rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .hero-stats {
    gap: var(--spacing-lg);
  }

  .stat-number {
    font-size: 1.5rem;
  }
}

/* ===== 滚动条样式 ===== */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* ===== 选择文本样式 ===== */
::selection {
  background: rgba(0, 212, 255, 0.3);
  color: var(--text-primary);
}

/* ===== 焦点样式 ===== */
*:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ===== 加载动画 ===== */
.loading {
  opacity: 0;
  transform: translateY(20px);
  transition: var(--transition-slow);
}

.loading.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* ===== 数字计数动画 ===== */
.stat-number {
  transition: var(--transition-slow);
}

.stat-number.counting {
  color: var(--primary-color);
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

/* ===== 悬浮效果增强 ===== */
.article-card,
.btn,
.tech-icon {
  will-change: transform;
}

/* ===== 性能优化 ===== */
.hero-visual,
.floating-tech,
.terminal-window {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* ===== 触摸设备优化 ===== */
@media (hover: none) and (pointer: coarse) {
  /* 移除悬浮效果，改为点击效果 */
  .article-card:hover {
    transform: none;
    box-shadow: var(--shadow-md);
  }

  .article-card:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  .btn:hover {
    transform: none;
  }

  .btn:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  .tech-icon:hover {
    transform: none;
  }

  .tech-icon:active {
    transform: scale(0.9);
    transition: transform 0.1s ease;
  }

  /* 增大触摸目标 */
  .nav-link {
    padding: var(--spacing-md) var(--spacing-lg);
    margin: var(--spacing-xs) 0;
  }

  .theme-toggle {
    width: 48px;
    height: 48px;
  }

  .hamburger {
    width: 48px;
    height: 48px;
  }
}

/* ===== 超小屏幕优化 (320px) ===== */
@media (max-width: 320px) {
  .hero-title {
    font-size: 2rem;
    line-height: 1.1;
  }

  .hero-description {
    font-size: 0.9rem;
  }

  .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.8rem;
    min-width: 120px;
  }

  .hero-buttons {
    gap: var(--spacing-md);
  }

  .terminal-window {
    margin: var(--spacing-lg) 0;
  }

  .terminal-body {
    font-size: 0.7rem;
    padding: var(--spacing-sm);
  }

  .article-content {
    padding: var(--spacing-md);
  }

  .article-title {
    font-size: 1.1rem;
    line-height: 1.3;
  }

  .article-excerpt {
    font-size: 0.8rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .stat-number {
    font-size: 1.2rem;
  }

  .stat-label {
    font-size: 0.7rem;
  }
}

/* ===== 横屏手机优化 ===== */
@media (max-width: 768px) and (orientation: landscape) {
  .hero {
    min-height: 100vh;
    padding: var(--spacing-xl) 0;
  }

  .hero-container {
    gap: var(--spacing-xl);
  }

  .hero-title {
    font-size: 2.2rem;
  }

  .terminal-window {
    max-height: 200px;
  }

  .nav-menu {
    height: 100vh;
    padding-top: var(--spacing-xl);
  }
}

/* ===== 平板优化 ===== */
@media (min-width: 769px) and (max-width: 1024px) {
  .hero-title {
    font-size: 3.5rem;
  }

  .articles-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .article-card.featured {
    grid-column: span 2;
  }

  .terminal-window {
    max-width: 500px;
  }

  .hero-stats {
    gap: var(--spacing-2xl);
  }
}

/* ===== 大屏幕优化 ===== */
@media (min-width: 1440px) {
  .container {
    max-width: 1400px;
  }

  .hero-title {
    font-size: 4.5rem;
  }

  .hero-description {
    font-size: 1.3rem;
    max-width: 600px;
  }

  .articles-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .article-card.featured {
    grid-column: span 1;
    grid-row: span 2;
  }

  .terminal-window {
    max-width: 600px;
  }
}

/* ===== 超大屏幕优化 ===== */
@media (min-width: 1920px) {
  .container {
    max-width: 1600px;
  }

  .hero-title {
    font-size: 5rem;
  }

  .section-title {
    font-size: 3.5rem;
  }

  .articles-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .article-card.featured {
    grid-column: span 2;
    grid-row: span 1;
  }
}

/* ===== 减少动画偏好 ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .floating-tech {
    animation: none;
  }

  .typing-animation {
    animation: none;
  }

  .particle-canvas {
    display: none;
  }
}

/* ===== 关于我部分 ===== */
.about {
  padding: 120px 0;
  background: linear-gradient(
    135deg,
    var(--bg-secondary) 0%,
    var(--bg-primary) 100%
  );
  position: relative;
  overflow: hidden;
}

.about::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 70% 30%,
      rgba(0, 212, 255, 0.08) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 30% 70%,
      rgba(255, 0, 150, 0.08) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.about-content {
  position: relative;
  z-index: 2;
}

.about-text {
  max-width: 800px;
  margin: 0 auto 60px;
  text-align: center;
}

.about-text p {
  font-size: 18px;
  line-height: 1.8;
  color: var(--text-secondary);
  margin-bottom: 25px;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 30px;
  max-width: 800px;
  margin: 0 auto;
}

.skill-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 30px 20px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  transition: all 0.3s ease;
  text-align: center;
}

.skill-item:hover {
  transform: translateY(-10px);
  background: var(--glass-bg-hover);
  box-shadow: 0 20px 40px rgba(0, 212, 255, 0.15);
}

.skill-item i {
  font-size: 40px;
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.skill-item:hover i {
  transform: scale(1.1);
  color: var(--accent-color);
}

.skill-item span {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

/* ===== 关于我部分响应式 ===== */
@media (max-width: 768px) {
  .about {
    padding: 80px 0;
  }

  .about-text p {
    font-size: 16px;
  }

  .skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
  }

  .skill-item {
    padding: 25px 15px;
  }

  .skill-item i {
    font-size: 32px;
  }

  .skill-item span {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .about {
    padding: 60px 0;
  }

  .skills-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .skill-item {
    padding: 20px 10px;
  }

  .skill-item i {
    font-size: 28px;
  }
}

/* ===== 项目展示部分 ===== */
.projects {
  padding: 120px 0;
  background: linear-gradient(
    135deg,
    var(--bg-primary) 0%,
    var(--bg-secondary) 100%
  );
  position: relative;
  overflow: hidden;
}

.projects::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(0, 212, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(255, 0, 150, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  position: relative;
  z-index: 2;
}

.project-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.project-card:hover {
  transform: translateY(-10px);
  background: var(--glass-bg-hover);
  box-shadow: 0 25px 50px rgba(0, 212, 255, 0.15);
}

.project-image {
  height: 200px;
  background: linear-gradient(
    135deg,
    rgba(0, 212, 255, 0.2) 0%,
    rgba(255, 0, 150, 0.2) 100%
  );
  position: relative;
  overflow: hidden;
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-links {
  display: flex;
  gap: 20px;
}

.project-link {
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--bg-primary);
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 20px;
}

.project-link:hover {
  background: var(--accent-color);
  transform: scale(1.1);
}

.project-content {
  padding: 30px;
}

.project-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 15px;
}

.project-description {
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-secondary);
  margin-bottom: 25px;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tech-tag {
  padding: 6px 12px;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.tech-tag:hover {
  background: rgba(0, 212, 255, 0.2);
  transform: translateY(-2px);
}

/* ===== 项目展示部分响应式 ===== */
@media (max-width: 768px) {
  .projects {
    padding: 80px 0;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .project-content {
    padding: 25px;
  }

  .project-title {
    font-size: 20px;
  }

  .project-description {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .projects {
    padding: 60px 0;
  }

  .project-content {
    padding: 20px;
  }

  .project-image {
    height: 150px;
  }

  .project-links {
    gap: 15px;
  }

  .project-link {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}

/* ===== 联系我部分 ===== */
.contact {
  padding: 120px 0;
  background: linear-gradient(
    135deg,
    var(--bg-primary) 0%,
    var(--bg-secondary) 100%
  );
  position: relative;
  overflow: hidden;
}

.contact::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 30% 70%,
      rgba(0, 212, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 30%,
      rgba(255, 0, 150, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
  position: relative;
  z-index: 2;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 25px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.contact-item:hover {
  transform: translateY(-5px);
  background: var(--glass-bg-hover);
  box-shadow: 0 20px 40px rgba(0, 212, 255, 0.1);
}

.contact-item i {
  font-size: 24px;
  color: var(--primary-color);
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 212, 255, 0.1);
  border-radius: 12px;
  flex-shrink: 0;
}

.contact-details h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 5px 0;
}

.contact-details p {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.contact-form {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  padding: 40px;
  position: relative;
  overflow: hidden;
}

.contact-form::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--primary-color),
    transparent
  );
}

.form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.form-group {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 18px 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 16px;
  font-family: var(--font-primary);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  resize: vertical;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.form-input::placeholder {
  color: var(--text-muted);
  transition: all 0.3s ease;
}

.form-input:focus::placeholder {
  opacity: 0.7;
  transform: translateY(-2px);
}

textarea.form-input {
  min-height: 120px;
  resize: vertical;
}

.form .btn {
  align-self: flex-start;
  margin-top: 10px;
}

/* ===== 联系我部分响应式 ===== */
@media (max-width: 768px) {
  .contact {
    padding: 80px 0;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .contact-form {
    padding: 30px 20px;
  }

  .contact-item {
    padding: 20px;
    gap: 15px;
  }

  .contact-item i {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .contact {
    padding: 60px 0;
  }

  .contact-item {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .contact-details h4 {
    font-size: 16px;
  }

  .contact-details p {
    font-size: 14px;
  }

  .form-input {
    padding: 15px 18px;
    font-size: 15px;
  }
}

/* ===== 页脚部分 ===== */
.footer {
  background: var(--bg-primary);
  border-top: 1px solid var(--glass-border);
  padding: 60px 0 30px;
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--primary-color),
    transparent
  );
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
}

.footer-text p {
  color: var(--text-secondary);
  font-size: 14px;
  margin: 0;
}

.footer-social {
  display: flex;
  gap: 20px;
}

.social-link {
  width: 40px;
  height: 40px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 16px;
}

.social-link:hover {
  background: var(--primary-color);
  color: var(--bg-primary);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
}

/* ===== 页脚部分响应式 ===== */
@media (max-width: 768px) {
  .footer {
    padding: 40px 0 20px;
  }

  .footer-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .footer-social {
    gap: 15px;
  }

  .social-link {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 30px 0 15px;
  }

  .footer-text p {
    font-size: 12px;
  }

  .footer-social {
    gap: 12px;
  }

  .social-link {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }
}

/* ===== 高对比度模式 ===== */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #00ffff;
    --secondary-color: #ff00ff;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --bg-primary: #000000;
    --bg-secondary: #111111;
    --border-color: #ffffff;
  }

  .article-card {
    border: 2px solid var(--border-color);
  }

  .btn {
    border: 2px solid currentColor;
  }
}

/* ===== 暗色模式偏好 ===== */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #666666;
    --border-color: #333333;
  }
}

/* ===== 打印样式 ===== */
@media print {
  .navbar,
  .hero-visual,
  .floating-tech,
  .scroll-indicator,
  .theme-toggle,
  .hamburger {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
    font-size: 12pt;
    line-height: 1.5;
  }

  .hero {
    min-height: auto;
    padding: 20pt 0;
    page-break-after: always;
  }

  .hero-title {
    font-size: 24pt;
    color: black !important;
  }

  .article-card {
    border: 1pt solid #ccc;
    page-break-inside: avoid;
    margin-bottom: 20pt;
  }

  .section-title {
    font-size: 18pt;
    color: black !important;
    page-break-after: avoid;
  }

  a {
    color: black !important;
    text-decoration: underline;
  }

  .btn {
    border: 1pt solid black;
    background: white !important;
    color: black !important;
  }
}
