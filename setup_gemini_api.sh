#!/bin/bash

# Gemini CLI API密钥配置脚本
echo "🔑 Gemini CLI API密钥配置"
echo "=========================="
echo ""

# 检查是否已设置API密钥
if [ -n "$GEMINI_API_KEY" ]; then
    echo "✅ GEMINI_API_KEY 环境变量已设置"
    echo "当前API密钥: ${GEMINI_API_KEY:0:10}..."
else
    echo "❌ GEMINI_API_KEY 环境变量未设置"
    echo ""
    echo "请按照以下步骤获取API密钥："
    echo "1. 访问 Google AI Studio: https://aistudio.google.com/app/apikey"
    echo "2. 登录您的Google账户"
    echo "3. 点击 'Create API Key'"
    echo "4. 复制生成的API密钥"
    echo ""
    echo "然后运行以下命令设置API密钥："
    echo "export GEMINI_API_KEY='your-api-key-here'"
    echo ""
    echo "或者将以下行添加到您的 ~/.bashrc 或 ~/.zshrc 文件中："
    echo "export GEMINI_API_KEY='your-api-key-here'"
fi

echo ""
echo "🧪 测试连接"
echo "============"

if [ -n "$GEMINI_API_KEY" ]; then
    echo "正在测试Gemini CLI连接..."
    echo "Hello, this is a test message" | gemini -p "Please respond with a simple greeting"
else
    echo "⚠️  请先设置API密钥后再测试连接"
fi
